import { CreateAccountTypeDto } from './dto/create-account-type.dto';
import { AccountTypeService } from './account-types.service';
export declare class AccountTypeController {
    private readonly accountTypeService;
    constructor(accountTypeService: AccountTypeService);
    createBulk(accountTypeDto: CreateAccountTypeDto[]): Promise<any>;
    deleteBulk(body: {
        keys: string[];
    }): Promise<any>;
}
