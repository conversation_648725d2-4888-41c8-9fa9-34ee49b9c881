"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesController = void 0;
const common_1 = require("@nestjs/common");
const profiles_service_1 = require("./profiles.service");
const create_profile_dto_1 = require("./dto/create-profile.dto");
const update_profile_dto_1 = require("./dto/update-profile.dto");
const swagger_1 = require("@nestjs/swagger");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
const user_decorator_1 = require("../../common/decorators/user.decorator");
let ProfilesController = class ProfilesController {
    constructor(profilesService) {
        this.profilesService = profilesService;
    }
    create(createProfileDto) {
        return this.profilesService.create(createProfileDto);
    }
    findAll(user) {
        return this.profilesService.findAll(user.profile.hierarchy);
    }
    findOne(id) {
        return this.profilesService.findOne(+id);
    }
    update(id, updateProfileDto) {
        return this.profilesService.update(+id, updateProfileDto);
    }
    remove(id) {
        return this.profilesService.remove(+id);
    }
    async createBulk(profileDto) {
        if (!profileDto || profileDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.profilesService.createProfileFromBulk(profileDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.ProfilesController = ProfilesController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/profiles'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo Perfil' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perfil criado com sucesso.',
        type: create_profile_dto_1.CreateProfileDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_profile_dto_1.CreateProfileDto]),
    __metadata("design:returntype", void 0)
], ProfilesController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/profiles'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Perfis' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Perfis encontrada com sucesso.',
        type: create_profile_dto_1.CreateProfileDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Perfil encontrado.' }),
    (0, common_1.Get)(),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProfilesController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/profiles'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Perfil pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Perfil encontrado com sucesso.',
        type: create_profile_dto_1.CreateProfileDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Perfil não encontrado.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProfilesController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/profiles'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Perfil' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Perfil atualizado com sucesso.',
        type: update_profile_dto_1.UpdateProfileDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Perfil não encontrado.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_profile_dto_1.UpdateProfileDto]),
    __metadata("design:returntype", void 0)
], ProfilesController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/profiles'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Perfil' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Perfil removido com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Perfil não encontrado.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProfilesController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiExcludeEndpoint)(),
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplos perfis em massa',
        description: `
      Este endpoint permite criar novos perfis ou atualizar perfis já existentes em massa.
      Se um perfil já existir (com base na chave única "key"), seus dados serão atualizados com as informações enviadas.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [create_profile_dto_1.CreateProfileDto],
        description: 'Array de objetos contendo os dados dos perfis a serem criados ou atualizados.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar perfis',
                value: [
                    {
                        name: 'Admin',
                        description: 'Perfil de Administrador',
                        key: 'ADMIN',
                        email: '<EMAIL>',
                        hierarchy: 1,
                    },
                    {
                        name: 'Usuário Padrão',
                        description: 'Perfil de Usuário Padrão',
                        key: 'USER',
                        email: '<EMAIL>',
                        hierarchy: 2,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Perfis criados ou atualizados com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Profiles processed successfully',
                processedProfiles: [
                    {
                        id: 1,
                        name: 'Admin',
                        description: 'Perfil de Administrador',
                        key: 'ADMIN',
                        email: '<EMAIL>',
                        hierarchy: 1,
                        updated: true,
                    },
                    {
                        id: 2,
                        name: 'Usuário Padrão',
                        description: 'Perfil de Usuário Padrão',
                        key: 'USER',
                        email: '<EMAIL>',
                        hierarchy: 2,
                        updated: false,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: name, description, key, hierarchy',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns perfis foram processados com sucesso, mas outros tiveram erros.',
        schema: {
            example: {
                processedProfiles: [
                    {
                        id: 3,
                        name: 'Gerente',
                        description: 'Perfil de Gerente',
                        key: 'MANAGER',
                        email: '<EMAIL>',
                        hierarchy: 3,
                        updated: false,
                    },
                ],
                errors: [
                    {
                        profile: {
                            name: 'Perfil Inválido',
                            key: 'INVALID',
                        },
                        status: 'error',
                        message: 'Hierarchy field is required',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], ProfilesController.prototype, "createBulk", null);
exports.ProfilesController = ProfilesController = __decorate([
    (0, permission_decorator_1.Permission)('profiles'),
    (0, common_1.Controller)('/api/v1/profiles'),
    __metadata("design:paramtypes", [profiles_service_1.ProfilesService])
], ProfilesController);
//# sourceMappingURL=profiles.controller.js.map