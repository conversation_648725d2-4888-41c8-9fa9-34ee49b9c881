"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardTypeSeeder = void 0;
const common_1 = require("@nestjs/common");
const card_type_entity_1 = require("../modules/card-types/entities/card-type.entity");
const typeorm_1 = require("typeorm");
let CardTypeSeeder = class CardTypeSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const cardTypeRepository = this.dataSource.getRepository(card_type_entity_1.CardType);
        await this.seedCardType('CRÉDITO', cardTypeRepository);
        await this.seedCardType('DÉBITO', cardTypeRepository);
    }
    async seedCardType(name, cardTypeRepository) {
        const existing = await cardTypeRepository.findOne({ where: { name } });
        if (!existing) {
            const type = new card_type_entity_1.CardType();
            type.name = name;
            await cardTypeRepository.save(type);
        }
    }
};
exports.CardTypeSeeder = CardTypeSeeder;
exports.CardTypeSeeder = CardTypeSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], CardTypeSeeder);
//# sourceMappingURL=card-type.seeder.js.map