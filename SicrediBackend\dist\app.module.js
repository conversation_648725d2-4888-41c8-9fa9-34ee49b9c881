"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const goal_product_wallet_module_1 = require("./modules/goal-product-wallet/goal-product-wallet.module");
const goal_module_1 = require("./modules/goal/goal.module");
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const users_module_1 = require("./modules/users/users.module");
const auth_module_1 = require("./auth/auth.module");
const config_1 = require("@nestjs/config");
const database_module_1 = require("./configs/database.module");
const federations_module_1 = require("./modules/federations/federations.module");
const audit_logs_module_1 = require("./modules/audit-logs/audit-logs.module");
const nest_winston_1 = require("nest-winston");
const winston_config_1 = require("./configs/winston.config");
const core_1 = require("@nestjs/core");
const logger_interceptor_1 = require("./common/interceptors/logger.interceptor");
const centrals_module_1 = require("./modules/centrals/centrals.module");
const cooperatives_module_1 = require("./modules/cooperatives/cooperatives.module");
const agencies_module_1 = require("./modules/agencies/agencies.module");
const segments_module_1 = require("./modules/segments/segments.module");
const permissions_module_1 = require("./modules/permissions/permissions.module");
const profiles_module_1 = require("./modules/profiles/profiles.module");
const profile_permissions_module_1 = require("./modules/profile-permissions/profile-permissions.module");
const user_agencies_module_1 = require("./modules/user-agencies/user-agencies.module");
const wallets_module_1 = require("./modules/wallets/wallets.module");
const products_module_1 = require("./modules/products/products.module");
const events_module_1 = require("./modules/events/events.module");
const associates_module_1 = require("./modules/associates/associates.module");
const associate_agency_accounts_module_1 = require("./modules/associate-agency-accounts/associate-agency-accounts.module");
const attendance_status_module_1 = require("./modules/attendance-status/attendance-status.module");
const attendances_module_1 = require("./modules/attendances/attendances.module");
const attendance_products_module_1 = require("./modules/attendance-products/attendance-products.module");
const attendance_products_effective_module_1 = require("./modules/attendance-products-effective/attendance-products-effective.module");
const schedules_module_1 = require("./modules/schedules/schedules.module");
const nest_keycloak_connect_1 = require("nest-keycloak-connect");
const keycloak_module_1 = require("./modules/keycloak/keycloak.module");
const wallet_range_values_module_1 = require("./modules/wallet-range-values/wallet-range-values.module");
const strategies_module_1 = require("./modules/strategies/strategies.module");
const attendance_history_module_1 = require("./modules/attendance-history/attendance-history.module");
const strategy_products_module_1 = require("./modules/strategy-products/strategy-products.module");
const propensity_products_module_1 = require("./modules/propensity-products/propensity-products.module");
const user_wallets_module_1 = require("./modules/user-wallets/user-wallets.module");
const wallet_summary_module_1 = require("./modules/wallet-summary/wallet-summary.module");
const filters_module_1 = require("./modules/filters/filters.module");
const attendance_summary_module_1 = require("./modules/attendance-summary/attendance-summary.module");
const accounts_module_1 = require("./modules/accounts/accounts.module");
const account_wallets_module_1 = require("./modules/account-wallets/account-wallets.module");
const account_types_module_1 = require("./modules/account-type/account-types.module");
const associate_details_module_1 = require("./modules/associate-details/associate-details.module");
const cards_module_1 = require("./modules/cards/cards.module");
const notifications_module_1 = require("./modules/notifications/notifications.module");
const schedule_1 = require("@nestjs/schedule");
const associate_phone_module_1 = require("./modules/associate-phone/associate-phone.module");
const rate_limit_middleware_1 = require("./common/middleware/rate-limit.middleware");
const global_rate_limit_middleware_1 = require("./common/middleware/global-rate-limit.middleware");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(global_rate_limit_middleware_1.GlobalRateLimitMiddleware)
            .forRoutes('*')
            .apply(rate_limit_middleware_1.RateLimitMiddleware)
            .forRoutes('api/v1/auth/login');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            goal_product_wallet_module_1.GoalProductWalletModule,
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            nest_keycloak_connect_1.KeycloakConnectModule.register({
                authServerUrl: process.env.KEYCLOAK_URL,
                realm: process.env.KEYCLOAK_REALM,
                clientId: process.env.KEYCLOAK_CLIENT_ID,
                secret: process.env.KEYCLOAK_CLIENT_SECRET,
                cookieKey: 'token',
            }),
            schedule_1.ScheduleModule.forRoot(),
            nest_winston_1.WinstonModule.forRoot(winston_config_1.winstonConfig),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            goal_module_1.GoalModule,
            database_module_1.DatabaseModule,
            federations_module_1.FederationsModule,
            audit_logs_module_1.AuditLogsModule,
            centrals_module_1.CentralsModule,
            cooperatives_module_1.CooperativesModule,
            agencies_module_1.AgenciesModule,
            segments_module_1.SegmentsModule,
            permissions_module_1.PermissionsModule,
            profiles_module_1.ProfilesModule,
            profile_permissions_module_1.ProfilePermissionsModule,
            user_agencies_module_1.UserAgenciesModule,
            wallets_module_1.WalletsModule,
            products_module_1.ProductsModule,
            events_module_1.EventsModule,
            associates_module_1.AssociatesModule,
            associate_agency_accounts_module_1.AssociateAgencyAccountsModule,
            attendance_status_module_1.AttendanceStatusModule,
            attendances_module_1.AttendancesModule,
            attendance_products_module_1.AttendanceProductsModule,
            attendance_products_effective_module_1.AttendanceProductsEffectiveModule,
            schedules_module_1.SchedulesModule,
            keycloak_module_1.KeycloakModule,
            wallet_range_values_module_1.WalletRangeValuesModule,
            strategies_module_1.StrategyModule,
            user_wallets_module_1.UserWalletsModule,
            attendance_history_module_1.AttendanceHistoryModule,
            strategy_products_module_1.StrategyProductsModule,
            propensity_products_module_1.PropensityProductsModule,
            wallet_summary_module_1.WalletSummaryModule,
            filters_module_1.FiltersModule,
            attendance_summary_module_1.AttendanceSummaryModule,
            accounts_module_1.AccountsModule,
            account_wallets_module_1.AccountWalletsModule,
            account_types_module_1.AccountTypeModule,
            associate_details_module_1.AssociateDetailsModule,
            cards_module_1.CardsModule,
            notifications_module_1.NotificationsModule,
            associate_phone_module_1.AssociatePhoneModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: logger_interceptor_1.LoggerInterceptor,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: nest_keycloak_connect_1.AuthGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: nest_keycloak_connect_1.ResourceGuard,
            },
            {
                provide: core_1.APP_GUARD,
                useClass: nest_keycloak_connect_1.RoleGuard,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map