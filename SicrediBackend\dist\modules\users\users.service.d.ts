import { Logger } from '@nestjs/common';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UpdateUserDto } from './dto/updateUser.dto';
import { CreateUserDto } from './dto/createUser.dto';
import { CreateUserResponse } from './dto/createUserResponse.dto';
import { UserDto } from './dto/user.dto';
import { SearchUserDto } from './dto/searchUser.dto';
import { KeycloakService } from 'src/modules/keycloak/keycloak.service';
import { Cryptography } from 'src/common/functions/cryptography';
import { ProfilePermissionsService } from '../profile-permissions/profile-permissions.service';
import { ProfilesService } from '../profiles/profiles.service';
import { CooperativesService } from '../cooperatives/cooperatives.service';
import { SearchUserAndProfileDto } from './dto/searchUserAndProfile.dto';
import { PaginatedUsersDto } from './dto/paginatedUser.dto';
import { CentralsService } from '../centrals/centrals.service';
import { AgenciesService } from '../agencies/agencies.service';
import { FederationsService } from '../federations/federations.service';
export declare class UsersService {
    private readonly usersRepository;
    private readonly logger;
    private readonly keycloak;
    private readonly profileService;
    private readonly cooperativeService;
    private readonly centralService;
    private readonly agenciesSevice;
    private readonly profilePermissionsService;
    private readonly federationsService;
    private readonly cryptography;
    constructor(usersRepository: Repository<User>, logger: Logger, keycloak: KeycloakService, profileService: ProfilesService, cooperativeService: CooperativesService, centralService: CentralsService, agenciesSevice: AgenciesService, profilePermissionsService: ProfilePermissionsService, federationsService: FederationsService, cryptography: Cryptography);
    update(id: number, user: UpdateUserDto, token: string): Promise<{
        message: string;
    }>;
    create(newUser: CreateUserDto, token: string): Promise<CreateUserResponse>;
    findByUserLogin(email: string): Promise<CreateUserDto | null>;
    findByDocuments(cpfList: string[], cnpjList: string[]): Promise<User[]>;
    searchByName(searchUser?: SearchUserDto): Promise<UserDto[]>;
    findById(id: number): Promise<User>;
    remove(id: number, token: string): Promise<void>;
    photoUpload(id: number, photo: any): Promise<CreateUserResponse>;
    findByIdKeyCloeker(searchUser?: SearchUserDto): Promise<UserDto>;
    findAll(): Promise<UserDto[]>;
    testeCripto(data: any): Promise<string>;
    findPaginatedUsers(userHierarchy: number, paginationParams: PaginatedUsersDto): Promise<{
        items: UserDto[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    findAllByAgency(id: number, filter?: string, cpf?: string): Promise<UserDto[]>;
    findAllByKeyProfile(data: string[]): Promise<UserDto[]>;
    findByKeycloakId(keycloakId: string): Promise<SearchUserAndProfileDto>;
    createUserFromBulk(user: UserDto[], token: string): Promise<any>;
    findByDocument(document: string): Promise<SearchUserAndProfileDto>;
    notificationToken(notificationToken: string, user: any): Promise<{
        message: string;
    }>;
}
