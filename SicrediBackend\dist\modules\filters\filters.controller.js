"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FiltersController = void 0;
const common_1 = require("@nestjs/common");
const filters_service_1 = require("./filters.service");
const swagger_1 = require("@nestjs/swagger");
const user_decorator_1 = require("../../common/decorators/user.decorator");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
let FiltersController = class FiltersController {
    constructor(filtersService) {
        this.filtersService = filtersService;
    }
    getSegments() {
        return this.filtersService.getSegments();
    }
    getAgencies(cooperativeId, userProfile) {
        return this.filtersService.getAgencies(cooperativeId, userProfile);
    }
    getUsers(agencyId, profile) {
        return this.filtersService.getUsers(agencyId, profile);
    }
    async getWallets(user, walletManagerId, assistantId) {
        return this.filtersService.getWalletsForUser(user, { walletManagerId, assistantId });
    }
    async getStatus() {
        return this.filtersService.getAllStatus();
    }
};
exports.FiltersController = FiltersController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallet-summary-filters'),
    (0, common_1.Get)('segments'),
    (0, swagger_1.ApiOperation)({ summary: 'Retorna todos os segmentos' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], FiltersController.prototype, "getSegments", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallet-summary-filters'),
    (0, permission_decorator_1.Permission)('wallet-agency-filters'),
    (0, common_1.Get)('agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Retorna as agências da cooperativa' }),
    __param(0, (0, common_1.Query)('cooperativeId')),
    __param(1, (0, common_1.Query)('userProfile')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", void 0)
], FiltersController.prototype, "getAgencies", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallet-summary-filters'),
    (0, common_1.Get)('users'),
    (0, swagger_1.ApiOperation)({ summary: 'Retorna os usuários por agência e perfil' }),
    __param(0, (0, common_1.Query)('agencyId')),
    __param(1, (0, common_1.Query)('profile')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, String]),
    __metadata("design:returntype", void 0)
], FiltersController.prototype, "getUsers", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallet-summary-filters'),
    (0, common_1.Get)('wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Retorna as carteiras disponíveis para o usuário logado ou filtradas pelo assistente/wallet manager' }),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)('walletManagerId')),
    __param(2, (0, common_1.Query)('assistantId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], FiltersController.prototype, "getWallets", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallet-summary-filters'),
    (0, common_1.Get)('status'),
    (0, swagger_1.ApiOperation)({ summary: 'Retorna os status' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FiltersController.prototype, "getStatus", null);
exports.FiltersController = FiltersController = __decorate([
    (0, permission_decorator_1.Permission)('wallet-summary-filters'),
    (0, common_1.Controller)('api/v1/wallet-summary-filters'),
    __metadata("design:paramtypes", [filters_service_1.FiltersService])
], FiltersController);
//# sourceMappingURL=filters.controller.js.map