"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateDetailsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const associate_details_controller_1 = require("./associate-details.controller");
const associate_details_service_1 = require("./associate-details.service");
const associate_details_entity_1 = require("./entities/associate-details.entity");
const associate_entity_1 = require("../associates/entities/associate.entity");
const account_entity_1 = require("../accounts/entities/account.entity");
const card_entity_1 = require("../cards/entities/card.entity");
const card_type_entity_1 = require("../card-types/entities/card-type.entity");
const accounts_module_1 = require("../accounts/accounts.module");
const associates_module_1 = require("../associates/associates.module");
const cards_module_1 = require("../cards/cards.module");
let AssociateDetailsModule = class AssociateDetailsModule {
};
exports.AssociateDetailsModule = AssociateDetailsModule;
exports.AssociateDetailsModule = AssociateDetailsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([associate_details_entity_1.AssociateDetails, associate_entity_1.Associate, account_entity_1.Accounts, card_entity_1.Card, card_type_entity_1.CardType]),
            (0, common_1.forwardRef)(() => associates_module_1.AssociatesModule),
            (0, common_1.forwardRef)(() => accounts_module_1.AccountsModule),
            (0, common_1.forwardRef)(() => cards_module_1.CardsModule)],
        controllers: [associate_details_controller_1.AssociateDetailsController],
        providers: [associate_details_service_1.AssociateDetailsService],
    })
], AssociateDetailsModule);
//# sourceMappingURL=associate-details.module.js.map