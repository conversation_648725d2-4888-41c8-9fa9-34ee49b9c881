"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserDto {
}
exports.UserDto = UserDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1, description: 'ID do usuário' }),
    __metadata("design:type", Number)
], UserDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: true, description: 'Usuário ativo' }),
    __metadata("design:type", Boolean)
], UserDto.prototype, "active", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'John Doe', description: 'Nome do usuário' }),
    __metadata("design:type", String)
], UserDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '<EMAIL>',
        description: 'Endereço de e-mail',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1, description: 'ID de perfil de usuário' }),
    __metadata("design:type", Number)
], UserDto.prototype, "profileId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'ADMIN', description: 'Key de perfil de usuário' }),
    __metadata("design:type", String)
], UserDto.prototype, "profileKey", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '99999999999', description: 'Telefone do usuário' }),
    __metadata("design:type", String)
], UserDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: ['CREATE_TABLE', 'UPDATE_RECORD'],
        description: 'Array de possíveis permissões do usuário',
    }),
    __metadata("design:type", Array)
], UserDto.prototype, "permissions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Administrador',
        description: 'Nome do perfil do usuário',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "profileName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'Hierarquia do perfil do usuário',
    }),
    __metadata("design:type", Number)
], UserDto.prototype, "hierarchy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'Código da agencia relacionada ao usuario',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "agency_code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'Nome da central relacionada ao usuario',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "central_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'Nome da cooperativa relacionada ao usuario',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "cooperative_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 1,
        description: 'Nome da federação relacionada ao usuario',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "federation_name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '03535566000106',
        description: 'cnpj',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "cnpj", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '02366478062',
        description: 'cpf',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "cpf", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '',
        description: 'serviceUnitNumber',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "serviceUnitNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: '02/12/2024',
        description: 'birthDate',
    }),
    __metadata("design:type", Date)
], UserDto.prototype, "birthDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'Santos',
        description: 'lastName',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "lastName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'password123',
        description: 'password',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'idKeyCloak',
        description: 'idKeyCloak',
    }),
    __metadata("design:type", String)
], UserDto.prototype, "idKeyCloak", void 0);
//# sourceMappingURL=user.dto.js.map