"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentralSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const federation_entity_1 = require("../modules/federations/entities/federation.entity");
const central_entity_1 = require("../modules/centrals/entities/central.entity");
let CentralSeeder = class CentralSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedCentral();
    }
    async getFederationByName(name, federationRepository) {
        return await federationRepository.findOne({ where: { name: name } });
    }
    async getCentral(name, address, centralRepository) {
        return await centralRepository.findOne({
            where: { name, address },
        });
    }
    async saveCentral(existing, federation, centralRepository) {
        if (!existing) {
            const central = new central_entity_1.Central();
            central.federationId = federation.id;
            central.federation = federation;
            central.name = 'Central Exemplo';
            central.address = 'Rua Exemplo, 123 - Centro';
            central.createdAt = new Date();
            return await centralRepository.save(central);
        }
        return false;
    }
    async executeSeed() {
        await this.seedCentral();
    }
    async seedCentral() {
        const centralRepository = this.dataSource.getRepository(central_entity_1.Central);
        const federationRepository = this.dataSource.getRepository(federation_entity_1.Federation);
        const federation = await this.getFederationByName('Federação Exemplo', federationRepository);
        if (!federation) {
            console.error('Federation with name "Federação Exemplo" does not exist. Please create it first.');
            return;
        }
        const existingCentral = await this.getCentral('Central Exemplo', 'Rua Exemplo, 123 - Centro', centralRepository);
        await this.saveCentral(existingCentral, federation, centralRepository);
    }
};
exports.CentralSeeder = CentralSeeder;
exports.CentralSeeder = CentralSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], CentralSeeder);
//# sourceMappingURL=central.seeder.js.map