import { AttendanceHistoryService } from './attendance-history.service';
import { CreateAttendanceHistoryDto } from './dto/create-attendance-history.dto';
import { UpdateAttendanceHistoryDto } from './dto/update-attendance-history.dto';
import { ResponseAttendanceDto } from './dto/response-attendance.dto';
export declare class AttendanceHistoryController {
    private readonly attendanceHistoryService;
    constructor(attendanceHistoryService: AttendanceHistoryService);
    create(createAttendanceHistoryDto: CreateAttendanceHistoryDto): Promise<{
        id: number;
        description: string;
        name: string;
        associateId: number;
        attendantId: number;
        statusId: number;
    }>;
    findAllByAssociate(id: number): Promise<ResponseAttendanceDto[]>;
    findOne(id: string): Promise<import("./entities/attendance-history.entity").AttendanceHistory>;
    update(id: string, updateAttendanceHistoryDto: UpdateAttendanceHistoryDto): Promise<{
        id: number;
        description: string;
        attendanceId: number;
        associateId: number;
        attendantId: number;
        statusId: number;
    }>;
    remove(id: string): Promise<void>;
}
