import { CreateSegmentDto } from './dto/create-segment.dto';
import { UpdateSegmentDto } from './dto/update-segment.dto';
import { Repository } from 'typeorm';
import { Segment } from './entities/segment.entity';
import { WalletRangeValue } from '../wallet-range-values/entities/wallet-range-value.entity';
import { Wallet } from '../wallets/entities/wallet.entity';
export declare class SegmentsService {
    private readonly segmentRepository;
    private readonly walletsRepository;
    private readonly walletRangeValueRepository;
    constructor(segmentRepository: Repository<Segment>, walletsRepository: Repository<Wallet>, walletRangeValueRepository: Repository<WalletRangeValue>);
    create(createSegmentDto: CreateSegmentDto): Promise<CreateSegmentDto>;
    findByName(name: string): Promise<CreateSegmentDto | null>;
    findAll(): Promise<CreateSegmentDto[]>;
    findOne(identifier: string | number): Promise<Segment>;
    findByCodes(codes: string[]): Promise<Segment[]>;
    update(id: number, updateSegmentDto: UpdateSegmentDto): Promise<CreateSegmentDto>;
    remove(id: number): Promise<void>;
    createSegmentFromBulk(segmentDto: CreateSegmentDto[]): Promise<any>;
    deleteSegmentsFromBulk(codes: string[]): Promise<any>;
}
