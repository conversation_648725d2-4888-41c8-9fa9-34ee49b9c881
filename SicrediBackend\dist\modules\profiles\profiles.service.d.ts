import { CreateProfileDto } from './dto/create-profile.dto';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { Repository } from 'typeorm';
import { Profile } from './entities/profile.entity';
import { Cryptography } from 'src/common/functions/cryptography';
export declare class ProfilesService {
    private readonly profileRepository;
    private readonly cryptography;
    constructor(profileRepository: Repository<Profile>, cryptography: Cryptography);
    create(createProfileDto: CreateProfileDto): Promise<CreateProfileDto>;
    findByName(name: string): Promise<CreateProfileDto | null>;
    findByKey(key: string): Promise<CreateProfileDto | null>;
    findAll(userHierarchy: number): Promise<CreateProfileDto[]>;
    findOne(id: number): Promise<CreateProfileDto>;
    update(id: number, updateData: UpdateProfileDto): Promise<UpdateProfileDto>;
    remove(id: number): Promise<void>;
    createProfileFromBulk(profileDto: CreateProfileDto[]): Promise<any>;
}
