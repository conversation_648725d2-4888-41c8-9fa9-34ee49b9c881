"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountWalletsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cryptography_1 = require("../../common/functions/cryptography");
const agencies_module_1 = require("../agencies/agencies.module");
const account_wallets_entity_1 = require("./entities/account-wallets.entity");
const account_wallets_service_1 = require("./account-wallets.service");
const account_wallets_controller_1 = require("./account-wallets.controller");
const accounts_module_1 = require("../accounts/accounts.module");
const wallets_module_1 = require("../wallets/wallets.module");
let AccountWalletsModule = class AccountWalletsModule {
};
exports.AccountWalletsModule = AccountWalletsModule;
exports.AccountWalletsModule = AccountWalletsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([account_wallets_entity_1.AccountWallets]),
            (0, common_1.forwardRef)(() => accounts_module_1.AccountsModule),
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule),
            (0, common_1.forwardRef)(() => wallets_module_1.WalletsModule)
        ],
        controllers: [account_wallets_controller_1.AccountWalletsController],
        providers: [account_wallets_service_1.AccountWalletsService, cryptography_1.Cryptography],
        exports: [account_wallets_service_1.AccountWalletsService, typeorm_1.TypeOrmModule],
    })
], AccountWalletsModule);
//# sourceMappingURL=account-wallets.module.js.map