"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociatePhoneModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const associate_phone_entity_1 = require("./entities/associate-phone.entity");
const associate_phone_service_1 = require("./associate-phone.service");
const associate_phone_controller_1 = require("./associate-phone.controller");
const cryptography_1 = require("../../common/functions/cryptography");
const associate_entity_1 = require("../associates/entities/associate.entity");
let AssociatePhoneModule = class AssociatePhoneModule {
};
exports.AssociatePhoneModule = AssociatePhoneModule;
exports.AssociatePhoneModule = AssociatePhoneModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([associate_phone_entity_1.AssociatePhone, associate_entity_1.Associate])],
        controllers: [associate_phone_controller_1.AssociatePhoneController],
        providers: [associate_phone_service_1.AssociatePhoneService, cryptography_1.Cryptography],
        exports: [associate_phone_service_1.AssociatePhoneService, cryptography_1.Cryptography],
    })
], AssociatePhoneModule);
//# sourceMappingURL=associate-phone.module.js.map