import { SelectQueryBuilder } from 'typeorm';
export declare class QueryHelper<T> {
    private query;
    constructor(query: SelectQueryBuilder<T>);
    private addCondition;
    whereHierarchy(operator: '<' | '<=' | '>' | '>=' | '=' | '!=', hierarchy: number, identifier?: string, allowedHierarchies?: number[], alias?: string): this;
    paginate(page: number, limit: number): this;
    distinct(distinct?: boolean): this;
    private ensureJoin;
    private getTableAlias;
    select(selection: string | string[], alias?: string): this;
    addSelect(selection: string, alias?: string): this;
    where(where: string, parameters?: Record<string, any>): this;
    andWhere(where: string, parameters?: Record<string, any>): this;
    orderBy(parameters: string, order?: 'ASC' | 'DESC'): this;
    getRawMany(): Promise<any[]>;
    getCount(): Promise<number>;
    getQueryBuilder(): SelectQueryBuilder<T>;
}
