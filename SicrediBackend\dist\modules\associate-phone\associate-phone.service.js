"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociatePhoneService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const associate_phone_entity_1 = require("./entities/associate-phone.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const typeorm_2 = require("@nestjs/typeorm");
const associate_entity_1 = require("../associates/entities/associate.entity");
let AssociatePhoneService = class AssociatePhoneService {
    constructor(associatePhoneRepository, associateRepository, cryptography) {
        this.associatePhoneRepository = associatePhoneRepository;
        this.associateRepository = associateRepository;
        this.cryptography = cryptography;
    }
    async create(createAssociatePhoneDto) {
        const newAssociatePhone = new associate_phone_entity_1.AssociatePhone();
        newAssociatePhone.associate.id = createAssociatePhoneDto.associateId;
        newAssociatePhone.phone = this.cryptography.encrypt(createAssociatePhoneDto.phone);
        newAssociatePhone.createdAt = new Date();
        const response = await this.associatePhoneRepository.save(newAssociatePhone);
        return {
            id: response.id,
            associateId: response.associate.id,
            phone: this.cryptography.decrypt(response.phone),
            createdAt: response.createdAt,
        };
    }
    async createMany(createAssociatePhoneDto) {
        const associatePhones = createAssociatePhoneDto.map(associatePhone => {
            const newAssociatePhone = new associate_phone_entity_1.AssociatePhone();
            newAssociatePhone.associate.id = associatePhone.associateId;
            newAssociatePhone.phone = this.cryptography.encrypt(associatePhone.phone);
            newAssociatePhone.createdAt = new Date();
            return newAssociatePhone;
        });
        const response = await this.associatePhoneRepository.save(associatePhones);
        return response.map(associatePhone => ({
            id: associatePhone.id,
            associateId: associatePhone.associate.id,
            phone: this.cryptography.decrypt(associatePhone.phone),
            createdAt: associatePhone.createdAt,
        }));
    }
    async findAllByAssociate(id) {
        const associate = await this.associateRepository.findOne({ where: { id } });
        const associatePhones = await this.associatePhoneRepository.find({ where: { associate: associate } });
        return associatePhones.map(associatePhone => ({
            ...associatePhone,
            phone: this.cryptography.decrypt(associatePhone.phone),
        }));
    }
    async findOne(id) {
        const associatePhone = await this.associatePhoneRepository.findOne({ where: { id } });
        return { ...associatePhone,
            phone: this.cryptography.decrypt(associatePhone.phone)
        };
    }
    async update(id, updateAssociatePhoneDto) {
        const associatePhone = await this.associatePhoneRepository.findOne({ where: { id } });
        associatePhone.associate.id = updateAssociatePhoneDto.associateId;
        associatePhone.phone = this.cryptography.encrypt(updateAssociatePhoneDto.phone);
        associatePhone.updatedAt = new Date();
        const response = await this.associatePhoneRepository.save(associatePhone);
        return {
            id: response.id,
            associateId: response.associate.id,
            phone: this.cryptography.decrypt(response.phone),
            createdAt: response.createdAt,
            updatedAt: response.updatedAt,
        };
    }
    async remove(id) {
        const associatePhone = await this.associatePhoneRepository.findOne({ where: { id } });
        const response = await this.associatePhoneRepository.remove(associatePhone);
        return {
            id: response.id,
            associateId: response.associate.id,
            phone: this.cryptography.decrypt(response.phone),
            createdAt: response.createdAt,
            updatedAt: response.updatedAt,
            deletedAt: response.deletedAt,
        };
    }
};
exports.AssociatePhoneService = AssociatePhoneService;
exports.AssociatePhoneService = AssociatePhoneService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_2.InjectRepository)(associate_phone_entity_1.AssociatePhone)),
    __param(1, (0, typeorm_2.InjectRepository)(associate_entity_1.Associate)),
    __metadata("design:paramtypes", [typeorm_1.Repository,
        typeorm_1.Repository,
        cryptography_1.Cryptography])
], AssociatePhoneService);
//# sourceMappingURL=associate-phone.service.js.map