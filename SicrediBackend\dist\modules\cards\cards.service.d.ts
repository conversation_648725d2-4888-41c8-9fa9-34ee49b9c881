import { Repository } from 'typeorm';
import { Card } from './entities/card.entity';
import { CreateCardDto } from './dto/create-card.dto';
import { Cryptography } from 'src/common/functions/cryptography';
import { CardDto } from './dto/card.dto';
import { CardType } from '../card-types/entities/card-type.entity';
import { Accounts } from '../accounts/entities/account.entity';
export declare class CardsService {
    private cardsRepository;
    private accountRepository;
    private cardTypeRepository;
    private readonly cryptography;
    constructor(cardsRepository: Repository<Card>, accountRepository: Repository<Accounts>, cardTypeRepository: Repository<CardType>, cryptography: Cryptography);
    create(createCardDto: CreateCardDto): Promise<{
        id: number;
        accountId: number;
        holderName: string;
        cardNumber: string;
        expirationDate: string;
        securityCode: string;
        cardTypeId: number;
        createdAt: Date;
    }>;
    findAll(): Promise<Card[]>;
    findOne(id: number): Promise<{
        cardNumber: string;
        securityCode: string;
        id: number;
        accountId: number;
        holderName: string;
        expirationDate: string;
        cardTypeId: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date;
        account: Accounts;
    }>;
    findRecentByAccountId(accountId: number): Promise<{
        cardNumber: string;
        securityCode: string;
        id: number;
        accountId: number;
        holderName: string;
        expirationDate: string;
        cardTypeId: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date;
        account: Accounts;
    }>;
    update(id: number, updateCardDto: CreateCardDto): Promise<Card | {
        id: number;
        accountId: number;
        holderName: string;
        cardNumber: string;
        expirationDate: string;
        securityCode: string;
        cardTypeId: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: number): Promise<Card>;
    createCardsFromBulk(cardDto: CardDto[]): Promise<any>;
}
