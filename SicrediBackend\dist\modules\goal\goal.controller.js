"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const create_goal_dto_1 = require("./dto/create-goal.dto");
const goal_service_1 = require("./goal.service");
const user_decorator_1 = require("../../common/decorators/user.decorator");
const paginated_goal_dto_1 = require("./dto/paginated-goal.dto");
const update_strategy_dto_1 = require("./dto/update-strategy.dto");
const goal_dto_1 = require("./dto/goal.dto");
let GoalController = class GoalController {
    constructor(goalService) {
        this.goalService = goalService;
    }
    async findPaginatedGoals(user, paginationParams) {
        return await this.goalService.findPaginatedGoals(user, paginationParams);
    }
    async findOne(id) {
        return await this.goalService.findOne(+id);
    }
    async create(createGoalDto) {
        return await this.goalService.create(createGoalDto);
    }
    async update(id, updateGoalDto) {
        return await this.goalService.update(+id, updateGoalDto);
    }
    async remove(id) {
        return await this.goalService.remove(+id);
    }
    async createBulk(goalDto) {
        if (!goalDto || goalDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.goalService.createGoalFromBulk(goalDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.GoalController = GoalController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/goal'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Metas com paginação e filtro',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Metas encontrada com sucesso.',
        schema: {
            example: {
                items: [create_goal_dto_1.CreateGoalDto],
                totalItems: 100,
                totalPages: 10,
                currentPage: 1,
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Estratégia encontrada.' }),
    (0, common_1.Get)('paginated'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, paginated_goal_dto_1.PaginatedGoalsDto]),
    __metadata("design:returntype", Promise)
], GoalController.prototype, "findPaginatedGoals", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/goal'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Meta pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Meta encontrada com sucesso.',
        type: create_goal_dto_1.CreateGoalDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Meta não encontrada.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GoalController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/goal'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova Meta' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Meta criada com sucesso.',
        type: create_goal_dto_1.CreateGoalDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_goal_dto_1.CreateGoalDto]),
    __metadata("design:returntype", Promise)
], GoalController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/goal'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Meta' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Meta atualizada com sucesso.',
        type: update_strategy_dto_1.UpdateGoalDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Meta não encontrada.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_strategy_dto_1.UpdateGoalDto]),
    __metadata("design:returntype", Promise)
], GoalController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/goal'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Meta' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Meta removida com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Meta não encontrada.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], GoalController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiExcludeEndpoint)(),
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplas metas em massa',
        description: `
      Este endpoint permite criar novas metas ou atualizar metas já existentes em massa.
      Se uma meta já existir (com base na data inicial e final), seus dados serão atualizados com as informações enviadas.
      Além disso, relaciona a meta aos produtos e carteiras correspondentes.
      **A carteira pode ser encontrada pelo número wallet_number ou pelo número antigo wallet_number_old.**
      As datas devem ser enviadas no formato **dd/mm/yyyy** e serão convertidas automaticamente para o formato ISO 8601.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [goal_dto_1.GoalDto],
        description: 'Array de objetos contendo os dados das metas a serem criadas ou atualizadas.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar metas',
                value: [
                    {
                        initial_date: '01/01/2025',
                        final_date: '31/12/2025',
                        product_code: 'PROD001',
                        wallet_number: 'WLT123',
                        wallet_number_old: 'OLDWLT001',
                        agency_code: 'AG001',
                        value: '150000.00',
                    },
                    {
                        initial_date: '01/06/2025',
                        final_date: '31/12/2025',
                        product_code: 'PROD002',
                        wallet_number: 'WLT456',
                        agency_code: 'AG002',
                        value: '200000.00',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Metas criadas ou atualizadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Goals processed successfully',
                processedGoals: [
                    {
                        initial_date: '2025-01-01T00:00:00.000Z',
                        final_date: '2025-12-31T23:59:59.999Z',
                        product_code: 'PROD001',
                        wallet_number: 'WLT123',
                        agency_code: 'AG001',
                        value: '150000.00',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: initial_date, final_date, product_code, wallet_number, agency_code',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Requisição falhou pois um dos itens não foi encontrado.',
        schema: {
            example: {
                status: 'error',
                message: 'Wallet not found for the given wallet_number: WLT999 or wallet_number_old: OLDWLT999',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas metas foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedGoals: [
                    {
                        initial_date: '2025-06-01T00:00:00.000Z',
                        final_date: '2025-12-31T23:59:59.999Z',
                        product_code: 'PROD003',
                        wallet_number: 'WLT789',
                        agency_code: 'AG003',
                        value: '100000.00',
                    },
                ],
                errors: [
                    {
                        goal: {
                            initial_date: '2025-06-01T00:00:00.000Z',
                            final_date: '2025-12-31T23:59:59.999Z',
                            product_code: 'INVALID_PRD',
                            wallet_number: 'INVALID_WLT',
                            agency_code: 'INVALID_AG',
                            value: '50000.00',
                        },
                        status: 'error',
                        message: 'Product not found for product_code: INVALID_PRD',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], GoalController.prototype, "createBulk", null);
exports.GoalController = GoalController = __decorate([
    (0, common_1.Controller)('api/v1/goal'),
    __metadata("design:paramtypes", [goal_service_1.GoalService])
], GoalController);
//# sourceMappingURL=goal.controller.js.map