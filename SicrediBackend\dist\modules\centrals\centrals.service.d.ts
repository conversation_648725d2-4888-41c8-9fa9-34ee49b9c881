import { CreateCentralDto } from './dto/create-central.dto';
import { UpdateCentralDto } from './dto/update-central.dto';
import { Central } from './entities/central.entity';
import { Repository } from 'typeorm';
import { PaginatedCentralDto } from './dto/paginated-central.dto';
import { CooperativesService } from '../cooperatives/cooperatives.service';
import { FederationsService } from '../federations/federations.service';
import { CentralDto } from './dto/central.dto';
import { User } from '../users/entities/user.entity';
import { Cooperative } from '../cooperatives/entities/cooperative.entity';
export declare class CentralsService {
    private readonly centralRepository;
    private readonly usersRepository;
    private readonly cooperativeRepository;
    private readonly cooperativesService;
    private readonly federationsService;
    constructor(centralRepository: Repository<Central>, usersRepository: Repository<User>, cooperativeRepository: Repository<Cooperative>, cooperativesService: CooperativesService, federationsService: FederationsService);
    create(createCentralDto: CreateCentralDto): Promise<CreateCentralDto>;
    findByName(name: string): Promise<CreateCentralDto | null>;
    findByCodes(codes: string[]): Promise<Central[]>;
    findAll(): Promise<CreateCentralDto[]>;
    findOne(identifier: string | number): Promise<CreateCentralDto>;
    update(id: number, updateData: UpdateCentralDto): Promise<UpdateCentralDto>;
    remove(id: number): Promise<void>;
    findPaginatedCentrals(userHierarchy: number, paginationParams: PaginatedCentralDto): Promise<{
        items: CreateCentralDto[];
        totalItems: number;
        totalPages: number;
        currentPage: number;
    }>;
    findAllByFederation(federationId: number): Promise<CreateCentralDto[]>;
    createCentralFromBulk(centralDto: CentralDto[]): Promise<any>;
    deleteCentralsFromBulk(codes: string[]): Promise<any>;
}
