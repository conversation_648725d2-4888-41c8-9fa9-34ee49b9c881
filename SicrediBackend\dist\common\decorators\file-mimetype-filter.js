"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileMimetypeFilter = fileMimetypeFilter;
const common_1 = require("@nestjs/common");
function fileMimetypeFilter(...mimetypes) {
    return (req, file, callback) => {
        if (mimetypes.some((m) => file.mimetype.includes(m))) {
            callback(null, true);
        }
        else {
            callback(new common_1.UnsupportedMediaTypeException(`File type is not matching: ${mimetypes.join(', ')}`), false);
        }
    };
}
//# sourceMappingURL=file-mimetype-filter.js.map