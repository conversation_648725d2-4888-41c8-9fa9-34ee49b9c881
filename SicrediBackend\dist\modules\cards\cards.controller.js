"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardsController = void 0;
const common_1 = require("@nestjs/common");
const cards_service_1 = require("./cards.service");
const swagger_1 = require("@nestjs/swagger");
const create_card_dto_1 = require("./dto/create-card.dto");
const card_dto_1 = require("./dto/card.dto");
let CardsController = class CardsController {
    constructor(cardsService) {
        this.cardsService = cardsService;
    }
    async findAll() {
        return await this.cardsService.findAll();
    }
    async findOne(id) {
        return await this.cardsService.findOne(id);
    }
    async findByAccountId(accountId) {
        return await this.cardsService.findRecentByAccountId(accountId);
    }
    async createCardsFromBulk(cardDto) {
        if (!Array.isArray(cardDto) || cardDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição deve ser um array de objetos e não pode estar vazio.',
            });
        }
        return this.cardsService.createCardsFromBulk(cardDto);
    }
};
exports.CardsController = CardsController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/cards'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar todos os Cartões' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cartões encontrados com sucesso.',
        type: create_card_dto_1.CreateCardDto,
        isArray: true,
    }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CardsController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/cards'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Cartão pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cartão encontrado com sucesso.',
        type: create_card_dto_1.CreateCardDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Cartão encontrado.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CardsController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/cards'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Cartão pelo ID da Conta com atividade mais recente' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Cartão encontrado com sucesso.',
        type: create_card_dto_1.CreateCardDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Cartão encontrado.' }),
    (0, common_1.Get)('account/:accountId'),
    __param(0, (0, common_1.Param)('accountId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CardsController.prototype, "findByAccountId", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar múltiplos cartões em lote' }),
    (0, swagger_1.ApiBody)({
        type: [card_dto_1.CardDto],
        description: 'Array de objetos contendo os dados dos cartões a serem criados ou atualizados.',
        examples: {
            exemplo1: {
                summary: 'Exemplo de requisição para múltiplos cartões',
                value: [
                    {
                        account_code: '123456',
                        holder_name: 'João da Silva',
                        card_number: '****************',
                        expiration_date: '2026-12-31',
                        card_type_name: 'Visa',
                    },
                    {
                        account_code: '789101',
                        holder_name: 'Maria Oliveira',
                        card_number: '****************',
                        expiration_date: '2025-10-30',
                        card_type_name: 'Mastercard',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Retorna os cartões processados com sucesso e possíveis erros.',
        schema: {
            example: {
                status: 'success',
                message: 'Cards processed successfully',
                processedCards: [
                    {
                        id: 1,
                        account_code: '123456',
                        card_number: '****************',
                        expiration_date: '2026-12-31',
                        holder_name: 'João da Silva',
                        card_type_name: 'Visa',
                    },
                ],
                errors: [],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Erro de validação na requisição',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: account_code, card_number',
            },
        },
    }),
    (0, common_1.Put)('/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], CardsController.prototype, "createCardsFromBulk", null);
exports.CardsController = CardsController = __decorate([
    (0, common_1.Controller)('api/v1/cards'),
    __metadata("design:paramtypes", [cards_service_1.CardsService])
], CardsController);
//# sourceMappingURL=cards.controller.js.map