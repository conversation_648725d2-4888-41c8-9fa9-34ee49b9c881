import { Repository } from 'typeorm';
import { Segment } from 'src/modules/segments/entities/segment.entity';
import { Agency } from 'src/modules/agencies/entities/agency.entity';
import { User } from 'src/modules/users/entities/user.entity';
import { Wallet } from 'src/modules/wallets/entities/wallet.entity';
import { UserWallet } from 'src/modules/user-wallets/entities/user-wallets.entity';
import { AttendanceStatus } from '../attendance-status/entities/attendance-status.entity';
export declare class FiltersService {
    private readonly segmentRepository;
    private readonly agencyRepository;
    private readonly userRepository;
    private readonly walletRepository;
    private readonly userWalletRepository;
    private readonly attendanceStatusRepository;
    constructor(segmentRepository: Repository<Segment>, agencyRepository: Repository<Agency>, userRepository: Repository<User>, walletRepository: Repository<Wallet>, userWalletRepository: Repository<UserWallet>, attendanceStatusRepository: Repository<AttendanceStatus>);
    getSegments(): Promise<Segment[]>;
    getAgencies(cooperativeId?: number, userProfile?: string): Promise<Agency[]>;
    getUsers(agencyId: number, profile: string): Promise<User[]>;
    getWalletsForUser(user: any, dynamicFilters?: {
        walletManagerId?: number;
        assistantId?: number;
        agencyId?: number;
        userProfile?: string;
    }): Promise<Wallet[]>;
    getAllStatus(): Promise<AttendanceStatus[]>;
}
