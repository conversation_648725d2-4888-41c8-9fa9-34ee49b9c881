"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilePermissionsSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const profile_entity_1 = require("../modules/profiles/entities/profile.entity");
const permission_entity_1 = require("../modules/permissions/entities/permission.entity");
const profile_permission_entity_1 = require("../modules/profile-permissions/entities/profile-permission.entity");
let ProfilePermissionsSeeder = class ProfilePermissionsSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const profileRepository = this.dataSource.getRepository(profile_entity_1.Profile);
        const permissionRepository = this.dataSource.getRepository(permission_entity_1.Permission);
        const profilePermissionRepository = this.dataSource.getRepository(profile_permission_entity_1.ProfilePermission);
        const profilePermissionsMapping = [
            {
                profileKey: 'ADMIN',
                permissionKeys: [
                    'USERS_CREATE',
                    'USERS_UPDATE',
                    'USERS_VIEW',
                    'USERS_DELETE',
                    'PROFILES_CREATE',
                    'PROFILES_VIEW',
                    'PROFILES_UPDATE',
                    'PROFILES_DELETE',
                    'WALLETS_VIEW',
                    'WALLETS_CREATE',
                    'WALLETS_DELETE',
                    'WALLETS_UPDATE',
                    'FEDERATIONS_VIEW',
                    'FEDERATIONS_CREATE',
                    'FEDERATIONS_UPDATE',
                    'FEDERATIONS_DELETE',
                    'CENTRALS_VIEW',
                    'CENTRALS_CREATE',
                    'CENTRALS_UPDATE',
                    'CENTRALS_DELETE',
                    'COOPERATIVES_VIEW',
                    'COOPERATIVES_CREATE',
                    'COOPERATIVES_UPDATE',
                    'COOPERATIVES_DELETE',
                    'AGENCIES_VIEW',
                    'AGENCIES_CREATE',
                    'AGENCIES_UPDATE',
                    'AGENCIES_DELETE',
                    'WALLETRANGEVALUES_UPDATE',
                    'WALLETRANGEVALUES_CREATE',
                    'WALLETRANGEVALUES_VIEW',
                    'WALLETRANGEVALUES_DELETE',
                    'ATTENDANCES_VIEW',
                    'ATTENDANCES_CREATE',
                    'ATTENDANCES_UPDATE',
                    'ATTENDANCES_DELETE',
                    'SCHEDULES_VIEW',
                    'SCHEDULES_CREATE',
                    'SCHEDULES_UPDATE',
                    'SCHEDULES_DELETE',
                    'ASSOCIATES_VIEW',
                    'ASSOCIATES_CREATE',
                    'ASSOCIATES_UPDATE',
                    'ASSOCIATES_DELETE',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'WALLET-AGENCY-FILTERS_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_CREATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_UPDATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_DELETE',
                ],
            },
            {
                profileKey: 'FEDERATION',
                permissionKeys: [
                    'CENTRALS_VIEW',
                    'CENTRALS_CREATE',
                    'CENTRALS_UPDATE',
                    'CENTRALS_DELETE',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'WALLET-AGENCY-FILTERS_VIEW',
                ],
            },
            {
                profileKey: 'CENTRAL',
                permissionKeys: [
                    'COOPERATIVES_VIEW',
                    'COOPERATIVES_CREATE',
                    'COOPERATIVES_UPDATE',
                    'COOPERATIVES_DELETE',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'WALLET-AGENCY-FILTERS_VIEW',
                ],
            },
            {
                profileKey: 'COOPERATIVE',
                permissionKeys: [
                    'SCHEDULES_VIEW',
                    'SCHEDULES_CREATE',
                    'SCHEDULES_UPDATE',
                    'SCHEDULES_DELETE',
                    'ASSOCIATES_VIEW',
                    'ASSOCIATES_UPDATE',
                    'PROFILES_VIEW',
                    'USERS_CREATE',
                    'USERS_UPDATE',
                    'USERS_VIEW',
                    'USERS_DELETE',
                    'WALLETS_VIEW',
                    'WALLETS_UPDATE',
                    'WALLETRANGEVALUES_UPDATE',
                    'WALLETRANGEVALUES_CREATE',
                    'WALLETRANGEVALUES_VIEW',
                    'WALLETRANGEVALUES_DELETE',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'WALLET-AGENCY-FILTERS_VIEW',
                    'ATTENDANCES_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_CREATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_UPDATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_DELETE',
                ],
            },
            {
                profileKey: 'AGENCY_MANAGER',
                permissionKeys: [
                    'PROFILES_VIEW',
                    'USERS_CREATE',
                    'USERS_UPDATE',
                    'USERS_VIEW',
                    'USERS_DELETE',
                    'SCHEDULES_VIEW',
                    'SCHEDULES_CREATE',
                    'SCHEDULES_UPDATE',
                    'SCHEDULES_DELETE',
                    'ASSOCIATES_VIEW',
                    'ASSOCIATES_UPDATE',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'WALLETS_VIEW',
                    'WALLETS_UPDATE',
                    'ATTENDANCES_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_CREATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_UPDATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_DELETE',
                    'GOAL_CREATE',
                    'GOAL_UPDATE',
                    'GOAL_VIEW',
                    'GOAL_DELETE',
                ],
            },
            {
                profileKey: 'OPERATIONAL_ADMINISTRATIVE_MANAGER',
                permissionKeys: [
                    'PROFILES_VIEW',
                    'USERS_CREATE',
                    'USERS_UPDATE',
                    'USERS_VIEW',
                    'USERS_DELETE',
                    'SCHEDULES_VIEW',
                    'SCHEDULES_CREATE',
                    'SCHEDULES_UPDATE',
                    'SCHEDULES_DELETE',
                    'ASSOCIATES_VIEW',
                    'ASSOCIATES_UPDATE',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'WALLETS_VIEW',
                    'WALLETS_UPDATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_VIEW',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_CREATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_UPDATE',
                    'ATTENDANCE-PRODUCTS-EFFECTIVE_DELETE',
                ],
            },
            {
                profileKey: 'WALLET_MANAGER',
                permissionKeys: [
                    'USERS_VIEW',
                    'WALLETS_VIEW',
                    'SCHEDULES_VIEW',
                    'SCHEDULES_CREATE',
                    'SCHEDULES_UPDATE',
                    'SCHEDULES_DELETE',
                    'ATTENDANCES_VIEW',
                    'ATTENDANCES_CREATE',
                    'ATTENDANCES_UPDATE',
                    'ATTENDANCES_DELETE',
                    'ASSOCIATES_VIEW',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'BUSINESS_VIEW',
                    'ASSOCIATES_WALLETS_VIEW',
                    'GOAL_VIEW_APP',
                ],
            },
            {
                profileKey: 'ASSISTANT',
                permissionKeys: [
                    'WALLETS_VIEW',
                    'SCHEDULES_VIEW',
                    'SCHEDULES_CREATE',
                    'SCHEDULES_UPDATE',
                    'SCHEDULES_DELETE',
                    'ATTENDANCES_VIEW',
                    'ATTENDANCES_CREATE',
                    'ATTENDANCES_UPDATE',
                    'ATTENDANCES_DELETE',
                    'ASSOCIATES_VIEW',
                    'WALLET-SUMMARY_VIEW',
                    'WALLET-SUMMARY-FILTERS_VIEW',
                    'ASSOCIATES_WALLETS_VIEW',
                ],
            },
        ];
        for (const mapping of profilePermissionsMapping) {
            const profile = await profileRepository.findOne({
                where: { key: mapping.profileKey },
            });
            if (profile) {
                for (const permissionKey of mapping.permissionKeys) {
                    const permission = await permissionRepository.findOne({
                        where: { key: permissionKey },
                    });
                    if (permission) {
                        const existing = await profilePermissionRepository.findOne({
                            where: { profileId: profile.id, permissionId: permission.id },
                        });
                        if (!existing) {
                            const profilePermission = new profile_permission_entity_1.ProfilePermission();
                            profilePermission.profileId = profile.id;
                            profilePermission.permissionId = permission.id;
                            await profilePermissionRepository.save(profilePermission);
                        }
                    }
                    else {
                        console.warn(`Permission with key ${permissionKey} not found.`);
                    }
                }
            }
            else {
                console.warn(`Profile with key ${mapping.profileKey} not found.`);
            }
        }
    }
};
exports.ProfilePermissionsSeeder = ProfilePermissionsSeeder;
exports.ProfilePermissionsSeeder = ProfilePermissionsSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProfilePermissionsSeeder);
//# sourceMappingURL=profile-permissions.seeder.js.map