"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesSeeder = void 0;
const common_1 = require("@nestjs/common");
const profile_entity_1 = require("../modules/profiles/entities/profile.entity");
const typeorm_1 = require("typeorm");
let ProfilesSeeder = class ProfilesSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const profileRepository = this.dataSource.getRepository(profile_entity_1.Profile);
        await this.seedProfile('ADMIN', 'Administrador', 'Perfil de Administrador', 1, profileRepository);
        await this.seedProfile('FEDERATION', 'Federação', 'Perfil de Federação', 2, profileRepository);
        await this.seedProfile('CENTRAL', 'Central', 'Perfil de Central', 3, profileRepository);
        await this.seedProfile('COOPERATIVE', 'Cooperativa', 'Perfil de Cooperativa', 4, profileRepository);
        await this.seedProfile('AGENCY_MANAGER', 'Gerente de Agência', 'Perfil de Gerente de Agência', 5, profileRepository);
        await this.seedProfile('OPERATIONAL_ADMINISTRATIVE_MANAGER', 'Gerente Administrativo Operacional', 'Perfil de Gerente Administrativo Operacional', 5, profileRepository);
        await this.seedProfile('WALLET_MANAGER', 'Gestor de Carteira', 'Perfil de Gestor de Carteira', 6, profileRepository);
        await this.seedProfile('ASSISTANT', 'Assistente', 'Perfil de Assistente', 7, profileRepository);
    }
    async seedProfile(key, name, description, hierarchy, profileRepository) {
        const existing = await profileRepository.findOne({ where: { key } });
        if (!existing) {
            const profile = new profile_entity_1.Profile();
            profile.key = key;
            profile.name = name;
            profile.description = description;
            profile.hierarchy = hierarchy;
            await profileRepository.save(profile);
        }
    }
};
exports.ProfilesSeeder = ProfilesSeeder;
exports.ProfilesSeeder = ProfilesSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProfilesSeeder);
//# sourceMappingURL=profile.seeder.js.map