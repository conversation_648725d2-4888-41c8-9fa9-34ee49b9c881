import { AssociateDetails } from "./entities/associate-details.entity";
import { Repository } from "typeorm";
import { CreateAssociateDetailsDto } from "./dto/create-associate-details.dto";
import { UpdateAssociateDetailsDto } from "./dto/update-associate-details.dto";
import { AssociateDetailsDto } from "./dto/associate-details.dto";
import { Associate } from "../associates/entities/associate.entity";
import { Accounts } from "../accounts/entities/account.entity";
import { CardType } from "../card-types/entities/card-type.entity";
import { Card } from "../cards/entities/card.entity";
import { Cryptography } from "src/common/functions/cryptography";
export declare class AssociateDetailsService {
    private associateDetailsRepository;
    private associateRepository;
    private accountRepository;
    private cardRepository;
    private cardTypeRepository;
    private readonly cryptography;
    constructor(associateDetailsRepository: Repository<AssociateDetails>, associateRepository: Repository<Associate>, accountRepository: Repository<Accounts>, cardRepository: Repository<Card>, cardTypeRepository: Repository<CardType>, cryptography: Cryptography);
    create(createAssociateDetails: CreateAssociateDetailsDto): Promise<{
        id: number;
        associateId: number;
        isRestrict: boolean;
        lcaValue: number;
        depositValue: number;
        missingInstallments: number;
        hasSubscription: boolean;
        termDepositExpiration: Date;
        lcaExpiration: Date;
        lastRegistrationUpdate: Date;
        channelAccess: Date;
        hasFinancialFlow: boolean;
    }>;
    findByAssociateId(id: number): Promise<AssociateDetails>;
    findOne(id: number): Promise<AssociateDetails>;
    update(id: number, updateAssociateDetailsDto: UpdateAssociateDetailsDto): Promise<AssociateDetails | {
        id: number;
        associateId: number;
        isRestrict: boolean;
        isDebtor: boolean;
        lcaValue: number;
        depositValue: number;
        missingInstallments: number;
        hasSubscription: boolean;
        termDepositExpiration: Date;
        lcaExpiration: Date;
        lastRegistrationUpdate: Date;
        channelAccess: Date;
        hasFinancialFlow: boolean;
    }>;
    remove(id: number): Promise<AssociateDetails>;
    createAssociateDetailsFromBulk(dtos: AssociateDetailsDto[]): Promise<any>;
    private processAssociateDetailsBatch;
}
