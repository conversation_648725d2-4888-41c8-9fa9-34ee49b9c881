"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgenciesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const agency_entity_1 = require("./entities/agency.entity");
const typeorm_2 = require("typeorm");
const wallet_range_values_service_1 = require("../wallet-range-values/wallet-range-values.service");
const wallets_service_1 = require("../wallets/wallets.service");
const cooperatives_service_1 = require("../cooperatives/cooperatives.service");
const centrals_service_1 = require("../centrals/centrals.service");
const cooperative_entity_1 = require("../cooperatives/entities/cooperative.entity");
let AgenciesService = class AgenciesService {
    constructor(agencyRepository, cooperativeRepository, walletRangeValuesService, walletService, cooperativeService, centralService) {
        this.agencyRepository = agencyRepository;
        this.cooperativeRepository = cooperativeRepository;
        this.walletRangeValuesService = walletRangeValuesService;
        this.walletService = walletService;
        this.cooperativeService = cooperativeService;
        this.centralService = centralService;
    }
    async create(createAgencyDto) {
        const alreadyRegistered = await this.findByName(createAgencyDto.name);
        if (alreadyRegistered) {
            throw new common_1.ConflictException(`Agency '${createAgencyDto.name}' already registered`);
        }
        const newData = new agency_entity_1.Agency();
        newData.name = createAgencyDto.name;
        newData.address = createAgencyDto.address;
        newData.cooperativeId = createAgencyDto.cooperativeId;
        newData.agencyCode = createAgencyDto.agencyCode;
        const response = await this.agencyRepository.save(newData);
        await this.walletRangeValuesService.createWalletRangeValuesDefaultToAgencies(response.id);
        await this.walletService.createDefaultWalletsByAgency(response.id);
        return {
            id: response.id,
            cooperativeId: response.cooperativeId,
            agencyCode: response.agencyCode,
            name: response.name,
            address: response.address,
        };
    }
    async findByName(name) {
        const found = await this.agencyRepository.findOneBy({
            name,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!found) {
            return null;
        }
        return {
            id: found.id,
            cooperativeId: found.cooperativeId,
            agencyCode: found.agencyCode,
            name: found.name,
            address: found.address,
        };
    }
    async findAll(user) {
        switch (user.profile.key) {
            case 'FEDERATION':
                return await this.findAllByFederation(user.cooperativeId);
                break;
            case 'CENTRAL':
                return await this.findAllByCentral(user.centralId);
                break;
            case 'COOPERATIVE':
                return await this.findAllByIdCooperative(user.cooperativeId);
                break;
            case 'ADMIN':
                const query = this.agencyRepository
                    .createQueryBuilder('agency')
                    .select('agency.id', 'id')
                    .addSelect('agency.name', 'name')
                    .addSelect('agency.address', 'address')
                    .addSelect('agency.agency_code', 'agencyCode')
                    .where('agency.deleted_at IS NULL');
                const data = await query.getRawMany();
                const decryptedData = await Promise.all(data.map(async (value) => {
                    value.address = value.address;
                    return value;
                }));
                return decryptedData;
                break;
        }
    }
    async findOne(identifier) {
        const whereCondition = typeof identifier === "number"
            ? { id: identifier, deletedAt: (0, typeorm_2.IsNull)() }
            : { agencyCode: identifier, deletedAt: (0, typeorm_2.IsNull)() };
        const data = await this.agencyRepository.findOneBy(whereCondition);
        if (!data) {
            throw new common_1.NotFoundException(`Agency with identifier ${identifier} not found`);
        }
        return data;
    }
    async findByCodes(codes) {
        if (!codes || codes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'A lista de códigos não pode estar vazia.',
            });
        }
        const centrals = await this.agencyRepository.find({
            where: { agencyCode: (0, typeorm_2.In)(codes) },
        });
        if (!centrals || centrals.length === 0) {
            throw new common_1.NotFoundException({
                status: 'error',
                message: 'Nenhuma central encontrada para os códigos fornecidos.',
            });
        }
        return centrals;
    }
    async update(id, updateData) {
        await this.findOne(id);
        const data = {
            name: updateData.name,
            agencyCode: updateData.agencyCode,
            cooperativeId: updateData.cooperativeId,
            address: updateData.address,
            updatedAt: new Date(),
        };
        await this.agencyRepository.update(id, data);
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            name: updated.name,
            address: updated.address,
            agencyCode: updated.agencyCode,
            cooperativeId: updated.cooperativeId,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.agencyRepository.update(id, { deletedAt: new Date() });
    }
    async findAllByIdCooperative(id) {
        const query = this.agencyRepository
            .createQueryBuilder('agency')
            .select('agency.id', 'id')
            .addSelect('agency.name', 'name')
            .addSelect('agency.address', 'address')
            .addSelect('agency.agency_code', 'agencyCode')
            .where('agency.deleted_at IS NULL')
            .andWhere('agency.cooperative_id = :id', { id });
        const data = await query.getRawMany();
        return data.map((row) => ({
            id: row.id,
            name: row.name,
            address: row.address,
            agencyCode: row.agencycode,
        }));
    }
    async findPaginatedAgencies(paginationParams, user) {
        const { page = 1, limit = 10, filter, cooperativeId, agencyId, } = paginationParams;
        const queryBuilder = this.agencyRepository
            .createQueryBuilder('agency')
            .innerJoin('agency.cooperative', 'cooperative')
            .innerJoin('cooperative.central', 'central')
            .innerJoin('central.federation', 'federation')
            .select([
            'agency.id',
            'agency.name',
            'agency.address',
            'agency.agencyCode',
            'cooperative.id',
            'cooperative.name',
            'central.id',
            'central.name',
            'federation.id',
            'federation.name',
        ])
            .where('agency.deleted_at IS NULL')
            .andWhere('cooperative.deleted_at IS NULL')
            .andWhere('central.deleted_at IS NULL')
            .andWhere('federation.deleted_at IS NULL');
        if (filter) {
            queryBuilder.andWhere('(agency.name LIKE :filter OR agency.agencyCode LIKE :filter)', { filter: `%${filter}%` });
        }
        if (cooperativeId) {
            queryBuilder.andWhere('cooperative.id = :cooperativeId', {
                cooperativeId,
            });
        }
        if (agencyId) {
            queryBuilder.andWhere('agency.id = :agencyId', { agencyId });
        }
        if (user.cooperativeId) {
            queryBuilder.andWhere('cooperative.id = :cooperativeId', {
                cooperativeId: user.cooperativeId,
            });
        }
        if (user.agencyId) {
            queryBuilder.andWhere('agency.id = :agencyId', {
                agencyId: user.agencyId,
            });
        }
        const totalItems = await queryBuilder.getCount();
        const offset = (page - 1) * limit;
        queryBuilder.take(limit).skip(offset);
        const data = await queryBuilder.getMany();
        const totalPages = Math.ceil(totalItems / limit);
        return {
            items: data,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByCentral(id) {
        const coops = await this.cooperativeService.findAllByCentral(id);
        const agencies = await Promise.all(coops.map(async (coop) => {
            return await this.findAllByIdCooperative(coop.id);
        }));
        return agencies.flat();
    }
    async findAllByFederation(federationId) {
        const centrals = await this.centralService.findAllByFederation(federationId);
        if (!centrals.length) {
            return [];
        }
        const agencies = await Promise.all(centrals.map(async (central) => {
            return await this.findAllByCentral(central.id);
        }));
        return agencies.flat();
    }
    async getAllAgencyCodes() {
        const query = this.agencyRepository
            .createQueryBuilder('agency')
            .select('agency.agency_code', 'agencyCode')
            .where('agency.deleted_at IS NULL');
        const data = await query.getRawMany();
        return data.map((row) => row.agencyCode);
    }
    async findOneUser(id, user) {
        const data = await this.agencyRepository.findOneBy({
            id,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!data) {
            throw new common_1.NotFoundException(`Agency with ID ${id} not found`);
        }
        let response = {
            id: data.id,
            name: data.name,
            address: data.address,
            agencyCode: data.agencyCode,
            cooperativeId: data.cooperativeId,
        };
        if (user.profile.key == 'FEDERATION') {
            const cooperative = await this.cooperativeService.findOne(response.cooperativeId);
            response.centralId = cooperative.centralId;
            response.federationId = user.federationId;
        }
        if (user.profile.key == 'CENTRAL') {
            response.centralId = user.centralId;
        }
        return response;
    }
    async createAgencyFromBulk(agencyDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!agencyDto || agencyDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (agencyDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many agencies in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: agencyDto.length,
            });
        }
        const processedAgencies = [];
        const errors = [];
        for (let i = 0; i < agencyDto.length; i += BATCH_SIZE) {
            const batch = agencyDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.name)
                        missingFields.push(`name (index ${i + index})`);
                    if (!item.agency_code)
                        missingFields.push(`agency_code (index ${i + index})`);
                    if (!item.address)
                        missingFields.push(`address (index ${i + index})`);
                    if (!item.cooperative_code)
                        missingFields.push(`cooperative_code (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const cooperativeCodes = batch.map((item) => item.cooperative_code);
                const cooperatives = await this.cooperativeRepository.find({
                    where: { code: (0, typeorm_2.In)(cooperativeCodes) },
                });
                const cooperativeMap = new Map(cooperatives.map((c) => [c.code, c]));
                const agencyCodes = batch.map((item) => item.agency_code);
                const existingAgencies = await this.agencyRepository.find({
                    where: { agencyCode: (0, typeorm_2.In)(agencyCodes) },
                });
                const agencyMap = new Map(existingAgencies.map((a) => [a.agencyCode, a]));
                const newAgencies = [];
                const updatedAgencies = [];
                for (const item of batch) {
                    const cooperative = cooperativeMap.get(item.cooperative_code);
                    if (!cooperative) {
                        errors.push({
                            agency: item,
                            status: 'error',
                            message: `Cooperative not found for cooperative_code: ${item.cooperative_code}`,
                        });
                        continue;
                    }
                    let agency = agencyMap.get(item.agency_code);
                    if (agency) {
                        if (item.name)
                            agency.name = item.name;
                        if (item.address)
                            agency.address = item.address;
                        if (item.cooperative_code)
                            agency.cooperativeId = cooperative.id;
                        updatedAgencies.push(agency);
                    }
                    else {
                        const newAgency = this.agencyRepository.create({
                            name: item.name,
                            agencyCode: item.agency_code,
                            cooperativeId: cooperative.id,
                            address: item.address,
                        });
                        newAgencies.push(newAgency);
                    }
                }
                await this.agencyRepository.manager.transaction(async (transactionalEntityManager) => {
                    if (newAgencies.length > 0) {
                        await transactionalEntityManager.save(newAgencies);
                    }
                    if (updatedAgencies.length > 0) {
                        await transactionalEntityManager.save(updatedAgencies);
                    }
                });
                processedAgencies.push(...newAgencies.map((a) => ({
                    id: a.id,
                    name: a.name,
                    agency_code: a.agencyCode,
                    cooperative_code: cooperativeMap.get(a.cooperativeId)?.code || null,
                    address: a.address,
                })), ...updatedAgencies.map((a) => ({
                    id: a.id,
                    name: a.name,
                    agency_code: a.agencyCode,
                    cooperative_code: cooperativeMap.get(a.cooperativeId)?.code || null,
                    address: a.address,
                })));
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some agencies had errors' : 'Agencies processed successfully',
            processedAgencies,
            errors,
        };
    }
    async deleteAgenciesFromBulk(agencyCodes) {
        const MAX_BATCH_SIZE = 10;
        if (!agencyCodes || agencyCodes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (agencyCodes.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many agencies in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: agencyCodes.length,
            });
        }
        const processedAgencies = [];
        const errors = [];
        for (const agencyCode of agencyCodes) {
            try {
                const agency = await this.agencyRepository.findOne({ where: { agencyCode } });
                if (!agency) {
                    throw new common_1.NotFoundException(`Agency with code '${agencyCode}' not found.`);
                }
                agency.deletedAt = new Date();
                await this.agencyRepository.save(agency);
                processedAgencies.push({
                    id: agency.id,
                    name: agency.name,
                    agency_code: agency.agencyCode,
                    deleted_at: agency.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    agency_code: agencyCode,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some agencies had errors' : 'Agencies deleted successfully',
            processedAgencies,
            errors,
        };
    }
};
exports.AgenciesService = AgenciesService;
exports.AgenciesService = AgenciesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(agency_entity_1.Agency)),
    __param(1, (0, typeorm_1.InjectRepository)(cooperative_entity_1.Cooperative)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        wallet_range_values_service_1.WalletRangeValuesService,
        wallets_service_1.WalletsService,
        cooperatives_service_1.CooperativesService,
        centrals_service_1.CentralsService])
], AgenciesService);
//# sourceMappingURL=agencies.service.js.map