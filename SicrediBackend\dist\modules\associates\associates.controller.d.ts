import { AssociatesService } from './associates.service';
import { CreateAssociateDto } from './dto/create-associate.dto';
import { UpdateAssociateDto } from './dto/update-associate.dto';
import { PaginatedAssociateDto } from './dto/paginated-associate.dto';
export declare class AssociatesController {
    private readonly associatesService;
    constructor(associatesService: AssociatesService);
    create(createAssociateDto: CreateAssociateDto): Promise<CreateAssociateDto>;
    findAllPaged(user: any, paginationParams: PaginatedAssociateDto): Promise<{
        totalItems: number;
        totalPages: number;
        currentPage: number;
        items: CreateAssociateDto[];
    }>;
    findAll(user: any): Promise<CreateAssociateDto[]>;
    findOne(id: string): Promise<CreateAssociateDto>;
    update(id: string, updateAssociateDto: UpdateAssociateDto): Promise<UpdateAssociateDto>;
    remove(id: string): Promise<void>;
    removeWalletUser(id: number, request: any): Promise<{
        message: string;
    }>;
    createBulk(createAssociatesDto: CreateAssociateDto[]): Promise<any>;
    getWalletsFromAssociates(id: string): Promise<any[]>;
    deleteBulk(body: {
        cpfs: string[];
    }): Promise<any>;
}
