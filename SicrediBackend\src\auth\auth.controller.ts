import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Request,
  Res,
  UnauthorizedException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { AuthResponseDto } from './dto/auth.dto';
import { SignInDto } from './dto/signIn.dto';
import { RefreshTokenDto } from './dto/refreshToken.dto';
import { Unprotected } from 'nest-keycloak-connect';
import { UserDto } from 'src/modules/users/dto/user.dto';
import { Response } from 'express';

@Controller('api/v1/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiTags('private-api/auth')
  @ApiOperation({ summary: 'Login de usuário' })
  @ApiResponse({
    status: 200,
    description: 'Login bem-sucedido.',
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Credenciais inválidas.' })
  @ApiResponse({ status: 400, description: 'Parâmetros inválidos.' })
  @HttpCode(HttpStatus.OK)
  @Unprotected()
  @Post('/login')
  async signIn(
    @Body() signInDto: SignInDto,
    @Res() res: Response,
  ): Promise<any> {
    const response = await this.authService.signIn(
      signInDto.login,
      signInDto.password,
    );
    try {
      res.cookie(
        process.env.NODE_ENV === 'production' ? '__Host-token' : 'token',
        // 'token',
        response.access_token,
        {
          // httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          // secure: false,
          sameSite: 'lax',
          maxAge: 60 * 60 * 1000,
        },
      );
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
    return res.status(200).json({ message: 'Login realizado com sucesso.' });
  }

  @ApiTags('private-api/auth')
  @ApiOperation({ summary: 'Logout de usuário' })
  @ApiResponse({
    status: 200,
    description: 'Logout realizado com sucesso',
    type: AuthResponseDto,
  })
  @HttpCode(HttpStatus.OK)
  @Unprotected()
  @Post('/logout')
  logout(@Res() res: Response) {
    res.cookie(
      // process.env.NODE_ENV === 'production' ? '__Host-token' : 'token',
      'token',
      '',
      {
        // httpOnly: true,
        // secure: process.env.NODE_ENV === 'production',
        secure: false,
        // sameSite: 'strict',
        sameSite: 'lax',
        expires: new Date(0),
      },
    );

    return res.status(200).json({ message: 'Logout realizado com sucesso' });
  }

  @ApiTags('private-api/auth')
  @ApiOperation({ summary: 'Atualiza o token de acesso' })
  @ApiResponse({
    status: 200,
    description: 'Token atualizado com sucesso.',
    schema: { example: { accessToken: 'new-access-token' } },
  })
  @ApiResponse({ status: 401, description: 'Token de atualização inválido.' })
  @HttpCode(HttpStatus.OK)
  @Post('/refresh')
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<{ accessToken: string }> {
    const accessToken = await this.authService.refreshAccessToken(
      refreshTokenDto.refreshToken,
    );
    return { accessToken };
  }

  @ApiTags('private-api/auth')
  @ApiOperation({ summary: 'Buscar usuário pelo Token' })
  @ApiResponse({
    status: 200,
    description: 'Usuário encontrado com sucesso.',
    type: UserDto,
  })
  @ApiResponse({ status: 401, description: 'Credenciais inválidas.' })
  @ApiResponse({ status: 404, description: 'Usuário não encontrado.' })
  @Get('me')
  findByToken(@Request() request): Promise<UserDto> {
    return this.authService.findByToken(request.cookies?.token);
  }
}
