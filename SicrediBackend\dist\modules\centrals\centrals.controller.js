"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentralsController = void 0;
const common_1 = require("@nestjs/common");
const centrals_service_1 = require("./centrals.service");
const create_central_dto_1 = require("./dto/create-central.dto");
const update_central_dto_1 = require("./dto/update-central.dto");
const swagger_1 = require("@nestjs/swagger");
const user_decorator_1 = require("../../common/decorators/user.decorator");
const paginated_central_dto_1 = require("./dto/paginated-central.dto");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
const central_dto_1 = require("./dto/central.dto");
let CentralsController = class CentralsController {
    constructor(centralsService) {
        this.centralsService = centralsService;
    }
    create(createCentralDto) {
        return this.centralsService.create(createCentralDto);
    }
    findAll() {
        return this.centralsService.findAll();
    }
    findPaginatedFederations(user, paginationParams) {
        return this.centralsService.findPaginatedCentrals(user.profile.hierarchy, paginationParams);
    }
    findOne(id) {
        return this.centralsService.findOne(+id);
    }
    update(id, updateCentralDto) {
        return this.centralsService.update(+id, updateCentralDto);
    }
    remove(id) {
        return this.centralsService.remove(+id);
    }
    findAllByFederation(id) {
        return this.centralsService.findAllByFederation(+id);
    }
    async createBulk(centralDto) {
        if (!centralDto || centralDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.centralsService.createCentralFromBulk(centralDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(codes) {
        return this.centralsService.deleteCentralsFromBulk(codes);
    }
};
exports.CentralsController = CentralsController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova Central' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Central criada com sucesso.',
        type: create_central_dto_1.CreateCentralDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_central_dto_1.CreateCentralDto]),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Centrais' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Centrais encontrada com sucesso.',
        type: create_central_dto_1.CreateCentralDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Central encontrada.' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Centrais com paginação e filtro' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Centrais encontrada com sucesso.',
        schema: {
            example: {
                items: [
                    {
                        id: 1,
                        name: 'Central de Exemplo',
                        address: 'Endereço',
                    },
                ],
                totalItems: 100,
                totalPages: 10,
                currentPage: 1,
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Central encontrada.' }),
    (0, common_1.Get)('paginated'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, paginated_central_dto_1.PaginatedCentralDto]),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "findPaginatedFederations", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Central pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Central encontrada com sucesso.',
        type: create_central_dto_1.CreateCentralDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Central não encontrada.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Central' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Central atualizada com sucesso.',
        type: update_central_dto_1.UpdateCentralDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Central não encontrada.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_central_dto_1.UpdateCentralDto]),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Central' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Central removida com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Central não encontrada.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/centrals'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Centrais pelo id da federação' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Centrais encontrada com sucesso.',
        type: create_central_dto_1.CreateCentralDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Central encontrada.' }),
    (0, common_1.Get)("get-by-federation/:federationId"),
    __param(0, (0, common_1.Param)('federationId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CentralsController.prototype, "findAllByFederation", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplas centrais em massa',
        description: `
      Este endpoint permite criar novas centrais ou atualizar centrais já existentes em massa.
      Se uma central já existir (com base no nome), seus dados serão atualizados com as informações enviadas.
      O número máximo permitido de centrais por requisição é 20.000, sendo processadas em lotes de 500.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [central_dto_1.CentralDto],
        description: 'Array de objetos contendo os dados das centrais a serem criadas ou atualizadas.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar centrais',
                value: [
                    {
                        name: 'Central Norte',
                        federation_code: '0001',
                        address: 'Avenida Paulista, 123',
                        central_code: '0001',
                    },
                    {
                        name: 'Central Sul',
                        federation_code: '0002',
                        address: 'Rua das Rosas, 456',
                        central_code: '0002',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Centrais criadas ou atualizadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Centrals processed successfully',
                processedCentrals: [
                    {
                        id: 1,
                        name: 'Central Norte',
                        federation_code: '0001',
                        address: 'Avenida Paulista, 123',
                        central_code: '0001',
                    },
                    {
                        id: 2,
                        name: 'Central Sul',
                        federation_code: '0002',
                        address: 'Rua das Rosas, 456',
                        central_code: '0002',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: name, federation_code, address, central_code',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas centrais foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedCentrals: [
                    {
                        id: 3,
                        name: 'Central Norte',
                        federation_code: '0001',
                        address: 'Rua Central, 789',
                        central_code: '0001',
                    },
                ],
                errors: [
                    {
                        central: {
                            name: 'Central Sul',
                            federation_code: '0002',
                            address: 'Rua Galvão Bueno, 101',
                            central_code: '0002',
                        },
                        status: 'error',
                        message: 'Federation not found for federation_code: 0002',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], CentralsController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar múltiplas centrais em massa',
        description: `
      Este endpoint permite excluir várias centrais ao mesmo tempo.
      Apenas um soft delete será realizado, mantendo os registros no banco.
      O limite é de 20 centrais por requisição.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [String],
        description: 'Array de códigos das centrais a serem deletadas',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar múltiplas centrais',
                value: ['0001', '0002'],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Centrais deletadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Centrals deleted successfully',
                processedCentrals: [
                    {
                        id: 1,
                        code: '0002',
                        name: 'Central Sul',
                        deleted_at: '2024-02-26T14:00:00Z',
                    },
                ],
                errors: [],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Nenhum código enviado ou número de registros excede o limite.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas centrais foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedCentrals: [
                    {
                        id: 2,
                        code: '0001',
                        name: 'Central Norte',
                        deleted_at: '2024-02-26T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        central_code: '0002',
                        status: 'error',
                        message: 'Central not found',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], CentralsController.prototype, "deleteBulk", null);
exports.CentralsController = CentralsController = __decorate([
    (0, permission_decorator_1.Permission)('centrals'),
    (0, common_1.Controller)('/api/v1/centrals'),
    __metadata("design:paramtypes", [centrals_service_1.CentralsService])
], CentralsController);
//# sourceMappingURL=centrals.controller.js.map