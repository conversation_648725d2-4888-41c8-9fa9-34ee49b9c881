"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const event_entity_1 = require("../modules/events/entities/event.entity");
let EventSeeder = class EventSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedEvents();
    }
    async getEvent(description, eventRepository) {
        const existing = await eventRepository.findOne({
            where: { description },
        });
        return existing;
    }
    async saveEvent(existing, description, eventRepository) {
        if (!existing) {
            const event = new event_entity_1.Event();
            event.description = description;
            return await eventRepository.save(event);
        }
        return false;
    }
    async seedEvents() {
        const eventRepository = this.dataSource.getRepository(event_entity_1.Event);
        const events = [
            { description: 'Evento Teste 1' },
            { description: 'Evento Teste 2' },
            { description: 'Evento Teste 3' },
        ];
        for (const eventData of events) {
            const existingEvent = await this.getEvent(eventData.description, eventRepository);
            await this.saveEvent(existingEvent, eventData.description, eventRepository);
        }
    }
};
exports.EventSeeder = EventSeeder;
exports.EventSeeder = EventSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], EventSeeder);
//# sourceMappingURL=event.seeder.js.map