import {
  BadRequestException,
  ConflictException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { In, IsNull, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UpdateUserDto } from './dto/updateUser.dto';
import { CreateUserDto } from './dto/createUser.dto';
import { CreateUserResponse } from './dto/createUserResponse.dto';
import { UserDto } from './dto/user.dto';
import { SearchUserDto } from './dto/searchUser.dto';
import { KeycloakService } from 'src/modules/keycloak/keycloak.service';
import { Cryptography } from 'src/common/functions/cryptography';
import { ProfilePermissionsService } from '../profile-permissions/profile-permissions.service';
import { ProfilesService } from '../profiles/profiles.service';
import { CooperativesService } from '../cooperatives/cooperatives.service';
import { SearchUserAndProfileDto } from './dto/searchUserAndProfile.dto';
import { PaginatedUsersDto } from './dto/paginatedUser.dto';
import { QueryHelper } from 'src/common/functions/queryHelper';
import { CentralsService } from '../centrals/centrals.service';
import { AgenciesService } from '../agencies/agencies.service';
import { FederationsService } from '../federations/federations.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepository: Repository<User>,

    private readonly logger: Logger,

    private readonly keycloak: KeycloakService,

    @Inject(forwardRef(() => ProfilesService))
    private readonly profileService: ProfilesService,

    private readonly cooperativeService: CooperativesService,

    @Inject(forwardRef(() => CentralsService))
    private readonly centralService: CentralsService,

    private readonly agenciesSevice: AgenciesService,

    private readonly profilePermissionsService: ProfilePermissionsService,

    private readonly federationsService: FederationsService,

    private readonly cryptography: Cryptography,
  ) {}

  async update(
    id: number,
    user: UpdateUserDto,
    token: string,
  ): Promise<{ message: string }> {
    const profile = await this.profileService.findByKey(user.profileKey);
    const userToUpdate = await this.findById(id);
    userToUpdate.name = user.name;
    userToUpdate.phone = this.cryptography.encrypt(user.phone);
    userToUpdate.email = this.cryptography.encrypt(user.email);
    userToUpdate.profileId = profile.id;
    userToUpdate.agencyId = user.agencyId;
    userToUpdate.centralId = user.centralId;
    userToUpdate.federationId = user.federationId;
    userToUpdate.cnpj = user.cnpj;
    userToUpdate.cpf = this.cryptography.encrypt(user.cpf);
    userToUpdate.cooperativeId = user.cooperativeId;
    userToUpdate.serviceUnitNumber = user.serviceUnitNumber;
    userToUpdate.birthDate = user.birthDate;
    userToUpdate.geralRegister = user.geralRegister;
    userToUpdate.updatedAt = new Date();

    delete userToUpdate.profile;

    await this.keycloak.updateUser({
      email: user.email,
      firstName: user.name,
      token,
      idKeycloak: userToUpdate.idUserKeycloak,
    });

    await this.usersRepository.save(userToUpdate);

    return { message: 'Atualizado com sucesso!' };
  }

  async create(
    newUser: CreateUserDto,
    token: string,
  ): Promise<CreateUserResponse> {
    let userNewProfile;
    const userAlreadyRegistered = await this.findByUserLogin(newUser.email);

    if (userAlreadyRegistered) {
      this.logger.error(
        `User '${newUser.email}' already registered`,
        { status: HttpStatus.CONFLICT },
        UsersService.name,
      );
      throw new ConflictException(`User '${newUser.email}' already registered`);
    }

    await this.keycloak.createUser({
      userName: newUser.email,
      password: newUser.password,
      email: newUser.email,
      firstName: newUser.name,
      lastName: newUser.lastName,
      token,
    });

    const userList: { id: string; username: string }[] =
      await this.keycloak.usersList({ token });

    const user = userList.find(
      (user: { username }) => user.username === newUser.email,
    );

    try {
      userNewProfile = await this.profileService.findByKey(newUser.profileKey);
      if (!userNewProfile?.id) {
        throw new NotFoundException('Profile not found');
      }
    } catch (error) {
      throw new BadRequestException('Profile not found');
    }

    try {
      const dbUser = new User();
      dbUser.name = newUser.name;
      dbUser.lastName = newUser.lastName;
      dbUser.phone = this.cryptography.encrypt(newUser.phone);
      dbUser.email = this.cryptography.encrypt(newUser.email);
      dbUser.profileId = userNewProfile.id;
      dbUser.idUserKeycloak = user.id;
      dbUser.agencyId = newUser.agencyId;
      dbUser.cooperativeId = newUser.cooperativeId;
      dbUser.centralId = newUser.centralId;
      dbUser.federationId = newUser.federationId;
      dbUser.cnpj = newUser.cnpj;
      dbUser.cpf = this.cryptography.encrypt(newUser.cpf);
      dbUser.serviceUnitNumber = newUser.serviceUnitNumber;
      dbUser.birthDate = newUser.birthDate;
      dbUser.geralRegister = newUser.geralRegister;

      const { id, name, lastName, phone, email } =
        await this.usersRepository.save(dbUser);

      return {
        id,
        name,
        lastName,
        phone: this.cryptography.decrypt(phone),
        email: this.cryptography.decrypt(email),
      };
    } catch (error) {
      if (user) {
        this.keycloak.deleteUser({
          token,
          idKeycloak: user.id,
        });
      }
      throw new BadRequestException('Failed to create user');
    }
  }

  async findByUserLogin(email: string): Promise<CreateUserDto | null> {
    const emailCryptography = this.cryptography.encrypt(email);
    const userFound = await this.usersRepository
      .createQueryBuilder('user')
      .where('user.email = :email', { email: emailCryptography })
      .andWhere('user.deleted_at IS NULL')
      .getOne();

    if (!userFound) {
      return null;
    }
    return {
      id: userFound.id,
      name: userFound.name,
      lastName: userFound.lastName,
      phone: this.cryptography.decrypt(userFound.phone),
      email: this.cryptography.decrypt(userFound.email),
      profileKey: 'admin',
      // profileId: userFound.profileId,
    };
  }

  async findByDocuments(
    cpfList: string[],
    cnpjList: string[],
  ): Promise<User[]> {
    if (
      (!cpfList || cpfList.length === 0) &&
      (!cnpjList || cnpjList.length === 0)
    ) {
      return [];
    }

    return this.usersRepository.find({
      where: [{ cpf: In(cpfList) }, { cnpj: In(cnpjList) }],
    });
  }

  async searchByName(searchUser?: SearchUserDto): Promise<UserDto[]> {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    queryBuilder
      .select(['user.id', 'user.name', 'user.email', 'user.active'])
      .where('user.deleted_at IS NULL');

    if (searchUser.name) {
      queryBuilder.andWhere('user.name LIKE :name', {
        name: `%${searchUser.name}%`,
      });
    }

    const users = await queryBuilder.getMany();

    users.forEach(async (user) => {
      user.email = this.cryptography.decrypt(user.email);
    });

    return users;
  }

  async findById(id: number): Promise<User> {
    const user = await this.usersRepository.find({
      where: {
        id,
        deletedAt: IsNull(),
      },
      relations: {
        profile: true,
      },
    });
    if (!user) {
      this.logger.error(
        `User with ID ${id} not found`,
        { status: HttpStatus.NOT_FOUND },
        UsersService.name,
      );
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    user[0].phone = this.cryptography.decrypt(user[0].phone);
    user[0].email = this.cryptography.decrypt(user[0].email);
    user[0].cpf = this.cryptography.decrypt(user[0].cpf);
    return user[0];
  }

  async remove(id: number, token: string): Promise<void> {
    // Encontrar o usuário pelo ID
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException(`Usuário com ID ${id} não encontrado.`);
    }

    // Inativar o usuário no Keycloak
    try {
      await this.keycloak.inactiveUser({
        token,
        idKeycloak: user.idUserKeycloak,
      });
    } catch (error) {
      throw new BadRequestException('Erro ao desativar usuário no Keycloak.');
    }

    // Atualizar status de exclusão no banco de dados
    await this.usersRepository.update(id, {
      deletedAt: new Date(),
      active: false,
    });
  }

  async photoUpload(id: number, photo: any): Promise<CreateUserResponse> {
    const user = await this.usersRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    user.photo = photo;
    const { name, lastName, phone, email } =
      await this.usersRepository.save(user);
    return { id, name, lastName, phone, email };
  }

  async findByIdKeyCloeker(searchUser?: SearchUserDto): Promise<UserDto> {
    const user = await this.usersRepository.findOneBy({
      idUserKeycloak: searchUser.idUserKeycloak,
    });

    if (!user) {
      this.logger.error(
        `User with keycloak ID ${searchUser.idUserKeycloak} not found`,
        { status: HttpStatus.NOT_FOUND },
        UsersService.name,
      );
      throw new NotFoundException(
        `User with ID ${searchUser.idUserKeycloak} not found`,
      );
    }

    // Decrypt user sensitive data
    user.phone = this.cryptography.decrypt(user.phone);
    user.email = this.cryptography.decrypt(user.email);

    // Fetch permissions associated with the user's profile
    const permissions = await this.profilePermissionsService.findAllByProfileId(
      user.profileId,
    );

    // Fetch hierarchy from the profile using ProfilesService
    const profile: any = await this.profileService.findOne(user.profileId);
    if (!profile) {
      throw new NotFoundException(
        `Profile with ID ${user.profileId} not found`,
      );
    }

    const hierarchy = profile.hierarchy;
    const profileName = profile.name;
    return {
      ...user,
      permissions,
      hierarchy,
      profileName,
      profileKey: profile.key,
    };
  }

  async findAll(): Promise<UserDto[]> {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    queryBuilder
      .select('user.id', 'id')
      .addSelect('user.name', 'name')
      .addSelect('user.last_name', 'lastName')
      .addSelect('user.email', 'email')
      .addSelect('user.phone', 'phone')
      .addSelect('user.profile_id', 'profileId')
      .addSelect(
        `(SELECT profile.name FROM profile WHERE profile.id = user.profile_id)`,
        'profileName',
      )
      .where('user.deleted_at IS NULL');

    const users = await queryBuilder.execute();
    users.forEach(async (user) => {
      user.email = this.cryptography.decrypt(user.email);
      user.phone = this.cryptography.decrypt(user.phone);
    });
    return users;
  }

  async testeCripto(data: any) {
    const teste = this.cryptography.encrypt(JSON.stringify(data));
    return teste;
  }

  async findPaginatedUsers(
    userHierarchy: number,
    paginationParams: PaginatedUsersDto,
  ): Promise<{
    items: UserDto[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }> {
    const { page = 1, limit = 10, filter, agencyId } = paginationParams;

    const queryBuilder = this.usersRepository.createQueryBuilder('user');
    const helper = new QueryHelper(queryBuilder);

    helper
      .select('user.id', 'id')
      .addSelect('user.name', 'name')
      .addSelect('user.email', 'email')
      .addSelect('user.phone', 'phone')
      .addSelect('user.profile_id', 'profileId')
      .addSelect('profile.name', 'profileName')
      .whereHierarchy('>', userHierarchy, 'profile_id')
      .orderBy('user.id', 'ASC');

    helper.andWhere('user.deleted_at IS NULL');

    if (filter) {
      const encryptedFilter = this.cryptography.encrypt(filter);
      helper.andWhere(
        '(LOWER(user.name) LIKE :filter OR LOWER(user.email) LIKE :encryptedFilter)',
        {
          filter: `%${filter.toLowerCase()}%`,
          encryptedFilter: `%${encryptedFilter.toLowerCase()}%`,
        },
      );
    }

    if (agencyId) {
      // Ajuste o campo 'user.agencyId' conforme o nome da coluna real da sua tabela
      helper.andWhere('user.agencyId = :agencyId', { agencyId });
    }

    // Obter contagem total antes de aplicar paginação
    const totalItems = await helper.getQueryBuilder().getCount();

    // Aplicar paginação
    helper.paginate(page, limit);

    // Obter resultados paginados
    const users = await helper.getRawMany();

    // Processar resultados
    const items = users.map((user) => ({
      ...user,
      email: this.cryptography.decrypt(user.email),
      phone: this.cryptography.decrypt(user.phone),
    }));

    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      totalItems,
      totalPages,
      currentPage: page,
    };
  }

  async findAllByAgency(
    id: number,
    filter?: string,
    cpf?: string, // Novo parâmetro para CPF
  ): Promise<UserDto[]> {
    const rules: string[] = ['WALLET_MANAGER', 'ASSISTANT'];
    const profiles = await this.profileService.findAll(1);
    const profilesFiltered = profiles.filter((profile) =>
      rules.includes(profile.key),
    );
    const profileIds = profilesFiltered.map((profile) => profile.id);

    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    queryBuilder
      .select('user.id', 'id')
      .addSelect('user.name', 'name')
      .addSelect('user.lastName', 'lastName')
      .addSelect('user.email', 'email')
      .addSelect('user.phone', 'phone')
      .addSelect('user.profileId', 'profileId')
      .addSelect('profile.key', 'profile')
      .addSelect('profile.key', 'profileKey')
      .addSelect('profile.name', 'profileName')
      .innerJoin('profile', 'profile', 'profile.id = user.profileId')
      .where('user.deletedAt IS NULL')
      .andWhere('user.agencyId = :id', { id })
      .andWhere('user.profileId IN (:...profileIds)', { profileIds });

    // Adicionar filtro por nome ou sobrenome, caso fornecido
    if (filter) {
      queryBuilder.andWhere(
        '(LOWER(user.name) LIKE :filter OR LOWER(user.lastName) LIKE :filter)',
        { filter: `%${filter.toLowerCase()}%` },
      );
    }

    // Adicionar filtro por CPF, caso fornecido
    if (cpf) {
      const normalizeCPF = (cpf: string): string => {
        return cpf.replace(/\D/g, '');
      };

      const normalizedCpf = normalizeCPF(cpf);
      const encryptedCpf = this.cryptography.encrypt(normalizedCpf);
      queryBuilder.andWhere('user.cpf = :encryptedCpf', { encryptedCpf });
    }

    const users = await queryBuilder.execute();

    // Descriptografar campos
    users.forEach((user: any) => {
      user.email = this.cryptography.decrypt(user.email);
      user.phone = this.cryptography.decrypt(user.phone);
    });

    return users;
  }

  async findAllByKeyProfile(data: string[]): Promise<UserDto[]> {
    const profiles = await this.profileService.findAll(1);
    const profilesFiltered = profiles.filter((profile) =>
      data.includes(profile.key),
    );

    const profileIds = profilesFiltered.map((profile) => profile.id);

    const queryBuilder = this.usersRepository.createQueryBuilder('user');
    queryBuilder
      .select([
        'user.id AS id',
        'user.name AS name',
        'user.last_name AS lastName',
        'user.email AS email',
        'user.phone AS phone',
        'user.profile_id AS profileId',
      ])
      .where('user.deleted_at IS NULL')
      .andWhere('user.profile_id IN (:...profileIds)', { profileIds });

    return await queryBuilder.getRawMany();
  }

  async findByKeycloakId(keycloakId: string): Promise<SearchUserAndProfileDto> {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');
    queryBuilder
      .select([
        'user.id AS id',
        'user.name AS name',
        'user.last_name AS lastName',
        'user.email AS email',
        'user.phone AS phone',
        'user.profile_id AS profileId',
        'user.id_user_keycloak AS keycloakId',
        'user.agencyId AS agencyId',
        'user.cooperativeId AS cooperativeId',
        'user.centralId AS centralId',
        'profile.id AS "profileId"',
        'user.federationId AS federationId',
        'profile.name AS "profileName"',
        'profile.key AS "profileKey"',
      ])
      .leftJoin('profile', 'profile', 'profile.id = user.profile_id')
      .where('user.deleted_at IS NULL')
      .andWhere('user.id_user_keycloak = :keycloakId', { keycloakId });

    return await queryBuilder.getRawOne();
  }

  async createUserFromBulk(user: UserDto[], token: string): Promise<any> {
    const MAX_BATCH_SIZE = 500;

    if (user.length > MAX_BATCH_SIZE) {
      return {
        status: 'error',
        message: `Too many users in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
        received: user.length,
      };
    }

    const userCreateOrUpdate: UserDto[] = [];
    const errors = [];

    for (const userDto of user) {
      try {
        const missingFields = [];

        if (!userDto.profileKey) missingFields.push('profileKey');
        if (!userDto.cooperative_name) missingFields.push('cooperative_name');
        if (!userDto.agency_code) missingFields.push('agency_code');
        if (!userDto.central_name) missingFields.push('central_name');
        if (!userDto.federation_name) missingFields.push('federation_name');

        if (missingFields.length > 0) {
          throw new Error(
            `Missing required fields in JSON: ${missingFields.join(', ')}`,
          );
        }

        const userNewProfile = await this.profileService.findByKey(
          userDto.profileKey,
        );
        const cooperative = await this.cooperativeService.findOne(
          userDto.cooperative_name,
        );
        const agencies = await this.agenciesSevice.findOne(userDto.agency_code);
        const central = await this.centralService.findOne(userDto.central_name);
        const federation = await this.federationsService.findOne(
          userDto.federation_name,
        );

        const missingEntities = [];

        if (!userNewProfile)
          missingEntities.push(
            'Profile does not exist for the given profileKey',
          );
        if (!cooperative)
          missingEntities.push(
            'Cooperative not found for the given cooperative_name',
          );
        if (!agencies)
          missingEntities.push('Agency not found for the given agency_code');
        if (!central)
          missingEntities.push('Central not found for the given central_name');
        if (!federation)
          missingEntities.push(
            'Federation not found for the given federation_name',
          );

        if (missingEntities.length > 0) {
          throw new Error(missingEntities.join('. '));
        }

        const encryptedEmail = this.cryptography.encrypt(userDto.email);
        let existingUser = await this.usersRepository.findOne({
          where: { email: encryptedEmail },
        });

        if (existingUser) {
          if (userDto.phone)
            existingUser.phone = this.cryptography.encrypt(userDto.phone);
          if (userDto.name) existingUser.name = userDto.name;
          if (userDto.lastName) existingUser.lastName = userDto.lastName;
          if (userDto.active !== undefined)
            existingUser.active = userDto.active;
          if (userDto.cnpj) existingUser.cnpj = userDto.cnpj;
          if (userDto.serviceUnitNumber)
            existingUser.serviceUnitNumber = userDto.serviceUnitNumber;
          if (userDto.birthDate) existingUser.birthDate = userDto.birthDate;
          if (userDto.cpf)
            existingUser.cpf = this.cryptography.encrypt(userDto.cpf);
          if (userDto.profileKey) existingUser.profileId = userNewProfile.id;
          if (userDto.agency_code) existingUser.agencyId = agencies.id;
          if (userDto.central_name) existingUser.centralId = central.id;
          if (userDto.federation_name)
            existingUser.federationId = federation.id;
          if (userDto.cooperative_name)
            existingUser.cooperativeId = cooperative.id;

          const updatedUser = await this.usersRepository.save(existingUser);
          userCreateOrUpdate.push(updatedUser);
          continue;
        }

        const userList: { id: string; username: string }[] =
          await this.keycloak.usersList({ token });
        const userKeyCloak = userList.find(
          (user: { username }) => user.username === userDto.email,
        );

        if (userKeyCloak) {
          errors.push({
            user: userDto,
            status: 'error',
            message: 'User already exists in Keycloak',
          });
          continue;
        }

        await this.keycloak.createUser({
          userName: userDto.email,
          password: userDto.password,
          email: userDto.email,
          firstName: userDto.name,
          lastName: userDto.lastName,
          token,
        });

        const userEntity = new User();
        userEntity.phone = this.cryptography.encrypt(userDto.phone);
        userEntity.name = userDto.name;
        userEntity.lastName = userDto.lastName;
        userEntity.email = encryptedEmail;
        userEntity.profileId = userNewProfile.id;
        userEntity.active = userDto.active;
        userEntity.photo = userDto.phone;
        userEntity.agencyId = agencies.id;
        userEntity.centralId = central.id;
        userEntity.federationId = federation.id;
        userEntity.cnpj = userDto.cnpj;
        userEntity.cpf = this.cryptography.encrypt(userDto.cpf);
        userEntity.cooperativeId = cooperative.id;
        userEntity.serviceUnitNumber = userDto.serviceUnitNumber;
        userEntity.birthDate = userDto.birthDate;

        const savedUser = await this.usersRepository.save(userEntity);
        userCreateOrUpdate.push(savedUser);
      } catch (error) {
        errors.push({
          user: userDto,
          status: 'error',
          message: error.message || 'Unexpected error occurred',
        });
      }
    }

    return {
      processedUsers: userCreateOrUpdate,
      errors,
    };
  }

  async findByDocument(document: string): Promise<SearchUserAndProfileDto> {
    const queryBuilder = this.usersRepository.createQueryBuilder('user');

    queryBuilder
      .select([
        'user.id AS id',
        'user.name AS name',
        'user.last_name AS lastName',
        'user.email AS email',
        'user.phone AS phone',
        'user.profile_id AS profileId',
        'user.id_user_keycloak AS keycloakId',
        'user.agencyId AS agencyId',
        'user.cooperativeId AS cooperativeId',
        'user.centralId AS centralId',
        'user.federationId AS federationId',
      ])
      .where('user.deleted_at IS NULL')
      .andWhere('(user.cpf = :document OR user.cnpj = :document)', {
        document,
      });

    return await queryBuilder.getRawOne();
  }

  async notificationToken(
    notificationToken: string,
    user: any,
  ): Promise<{ message: string }> {
    const findUser = await this.findById(user.id);

    if (findUser) {
      await this.usersRepository.update({ id: user.id }, { notificationToken });
    } else {
      throw new NotFoundException('Usuário não encontrado');
    }

    return { message: 'Atualizado com sucesso!' };
  }
}
