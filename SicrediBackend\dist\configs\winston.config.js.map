{"version": 3, "file": "winston.config.js", "sourceRoot": "", "sources": ["../../src/configs/winston.config.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAGsB;AACtB,iDAAmC;AACnC,qCAAmC;AAEtB,QAAA,aAAa,GAAyB;IACjD,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM;IACjC,KAAK,EAAE,SAAS;IAChB,UAAU,EAAE;QACV,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;YAC7B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,wBAA0B,CAAC,MAAM,CAAC,QAAQ,EAAE,CAC7C;SACF,CAAC;QACF,IAAI,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC;YACrC,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,wBAAwB;YAClC,WAAW,EAAE,eAAe;YAC5B,aAAa,EAAE,IAAI;YACnB,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,KAAK;YACf,OAAO,EAAE,kBAAkB;SAC5B,CAAC;QACF,IAAI,OAAO,CAAC,UAAU,CAAC,eAAe,CAAC;YACrC,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,wBAAwB;YAClC,WAAW,EAAE,eAAe;YAC5B,aAAa,EAAE,IAAI;YACnB,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,KAAK;YACf,OAAO,EAAE,YAAY;SACtB,CAAC;KACH;CACF,CAAC"}