"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const nest_winston_1 = require("nest-winston");
const winston_config_1 = require("./configs/winston.config");
const config_1 = require("@nestjs/config");
const payload_cryptography_1 = require("./common/functions/payload-cryptography");
const decryption_interceptor_1 = require("./common/interceptors/decryption.interceptor");
const encryption_interceptor_1 = require("./common/interceptors/encryption.interceptor");
const permissions_guard_1 = require("./guards/permissions.guard");
const permissions_service_1 = require("./modules/permissions/permissions.service");
const users_service_1 = require("./modules/users/users.service");
const profile_permissions_service_1 = require("./modules/profile-permissions/profile-permissions.service");
const profiles_service_1 = require("./modules/profiles/profiles.service");
const express = __importStar(require("express"));
const all_exceptions_filter_1 = require("./common/filters/all-exceptions.filter");
const cookie_parser_1 = __importDefault(require("cookie-parser"));
async function bootstrap() {
    const logger = nest_winston_1.WinstonModule.createLogger(winston_config_1.winstonConfig);
    const app = await core_1.NestFactory.create(app_module_1.AppModule, { logger });
    app.use(express.json({ limit: '50mb' }));
    app.use(express.urlencoded({ limit: '50mb', extended: true }));
    app.useGlobalPipes(new common_1.ValidationPipe({ transform: true }));
    const configService = app.get(config_1.ConfigService);
    const config = new swagger_1.DocumentBuilder()
        .setTitle('API Documentation')
        .setDescription('Documentação da API Sicredi')
        .setVersion('1.0')
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api-docs', app, document);
    const servers = configService.get('SERVER_LIST_URL').toString().split(',');
    app.useGlobalInterceptors(new decryption_interceptor_1.DecryptionInterceptor(new payload_cryptography_1.PayloadCryptography()));
    app.useGlobalInterceptors(new encryption_interceptor_1.EncryptionInterceptor(new payload_cryptography_1.PayloadCryptography()));
    app.use((0, cookie_parser_1.default)());
    app.enableCors({
        origin: true,
        methods: ['POST', 'PATCH', 'DELETE', 'GET'],
        credentials: true,
    });
    const reflector = app.get(core_1.Reflector);
    const permissionsService = app.get(permissions_service_1.PermissionsService);
    const usersService = app.get(users_service_1.UsersService);
    const profilePermissionsService = app.get(profile_permissions_service_1.ProfilePermissionsService);
    const profileService = app.get(profiles_service_1.ProfilesService);
    app.useGlobalFilters(new all_exceptions_filter_1.AllExceptionsFilter());
    app.useGlobalGuards(new permissions_guard_1.PermissionsGuard(reflector, permissionsService, profilePermissionsService, usersService, profileService));
    await app.listen(configService.get('PORT') ?? 3000);
}
bootstrap();
//# sourceMappingURL=main.js.map