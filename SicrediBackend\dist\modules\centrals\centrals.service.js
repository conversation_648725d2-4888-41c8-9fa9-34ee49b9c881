"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentralsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const central_entity_1 = require("./entities/central.entity");
const typeorm_2 = require("typeorm");
const cooperatives_service_1 = require("../cooperatives/cooperatives.service");
const federations_service_1 = require("../federations/federations.service");
const user_entity_1 = require("../users/entities/user.entity");
const cooperative_entity_1 = require("../cooperatives/entities/cooperative.entity");
let CentralsService = class CentralsService {
    constructor(centralRepository, usersRepository, cooperativeRepository, cooperativesService, federationsService) {
        this.centralRepository = centralRepository;
        this.usersRepository = usersRepository;
        this.cooperativeRepository = cooperativeRepository;
        this.cooperativesService = cooperativesService;
        this.federationsService = federationsService;
    }
    async create(createCentralDto) {
        const alreadyRegistered = await this.findByName(createCentralDto.name);
        if (alreadyRegistered) {
            throw new common_1.ConflictException(`Central '${createCentralDto.name}' already registered`);
        }
        const newData = new central_entity_1.Central();
        newData.name = createCentralDto.name;
        newData.address = createCentralDto.address;
        newData.federationId = createCentralDto.federationId;
        const response = await this.centralRepository.save(newData);
        return {
            id: response.id,
            federationId: response.federationId,
            name: response.name,
            address: response.address,
        };
    }
    async findByName(name) {
        const found = await this.centralRepository.findOneBy({
            name,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!found) {
            return null;
        }
        return {
            id: found.id,
            federationId: found.federationId,
            name: found.name,
            address: found.address,
        };
    }
    async findByCodes(codes) {
        if (!codes || codes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'A lista de códigos não pode estar vazia.',
            });
        }
        const centrals = await this.centralRepository.find({
            where: { code: (0, typeorm_2.In)(codes) },
        });
        if (!centrals || centrals.length === 0) {
            throw new common_1.NotFoundException({
                status: 'error',
                message: 'Nenhuma central encontrada para os códigos fornecidos.',
            });
        }
        return centrals;
    }
    async findAll() {
        const data = await this.centralRepository
            .createQueryBuilder('central')
            .select([
            'central.id as id',
            'central.name as name',
            'central.address as address',
            'federation.name as federationName',
        ])
            .addSelect('central.federation_id', 'federationId')
            .innerJoin('federation', 'federation', 'federation.id = central.federation_id')
            .where('central.deleted_at IS NULL')
            .execute();
        return data;
    }
    async findOne(identifier) {
        const whereCondition = typeof identifier === "number"
            ? { id: identifier, deletedAt: (0, typeorm_2.IsNull)() }
            : { code: identifier, deletedAt: (0, typeorm_2.IsNull)() };
        const data = await this.centralRepository.findOneBy(whereCondition);
        if (!data) {
            throw new common_1.NotFoundException(`Central with identifier ${identifier} not found`);
        }
        return { ...data };
    }
    async update(id, updateData) {
        await this.findOne(id);
        const data = {
            federationId: updateData.federationId,
            name: updateData.name,
            address: updateData.address,
            updatedAt: new Date(),
        };
        await this.centralRepository.update(id, data);
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            federationId: updated.federationId,
            name: updated.name,
            address: updated.address,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.centralRepository.update(id, { deletedAt: new Date() });
    }
    async findPaginatedCentrals(userHierarchy, paginationParams) {
        const { page = 1, limit = 10, filter, federationId } = paginationParams;
        const queryBuilder = this.centralRepository
            .createQueryBuilder('central')
            .innerJoin('central.federation', 'federation')
            .select([
            'central.id',
            'central.name',
            'central.address',
            'federation.id',
            'federation.name',
        ])
            .where('central.deleted_at IS NULL')
            .andWhere('federation.deleted_at IS NULL');
        if (filter) {
            queryBuilder.andWhere('central.name LIKE :filter', {
                filter: `%${filter}%`,
            });
        }
        if (federationId) {
            queryBuilder.andWhere('federation.id = :federationId', { federationId });
        }
        const totalItems = await queryBuilder.getCount();
        const offset = (page - 1) * limit;
        queryBuilder.take(limit).skip(offset);
        const data = await queryBuilder.getMany();
        const totalPages = Math.ceil(totalItems / limit);
        return {
            items: data,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByFederation(federationId) {
        const centrals = await this.centralRepository.find({
            where: { federationId, deletedAt: null },
        });
        const centralsDto = centrals.map((central) => ({
            id: central.id,
            name: central.name,
            address: central.address,
            federationId: central.federationId,
        }));
        return centralsDto;
    }
    async createCentralFromBulk(centralDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!centralDto || centralDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (centralDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many centrals in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: centralDto.length,
            });
        }
        const processedCentrals = [];
        const errors = [];
        for (let i = 0; i < centralDto.length; i += BATCH_SIZE) {
            const batch = centralDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.name)
                        missingFields.push(`name (index ${i + index})`);
                    if (!item.address)
                        missingFields.push(`address (index ${i + index})`);
                    if (!item.federation_code)
                        missingFields.push(`federation_code (index ${i + index})`);
                    if (!item.central_code)
                        missingFields.push(`central_code (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const federationCodes = [...new Set(batch.map((item) => item.federation_code))];
                const federations = await this.federationsService.findByCodes(federationCodes);
                const federationMap = new Map(federations.map((f) => [f.code, f]));
                const centralCodes = batch.map((item) => item.central_code);
                const existingCentrals = await this.centralRepository.find({
                    where: { code: (0, typeorm_2.In)(centralCodes) },
                });
                const centralMap = new Map(existingCentrals.map((c) => [c.code, c]));
                const newCentrals = [];
                const updatedCentrals = [];
                for (const item of batch) {
                    const federation = federationMap.get(item.federation_code);
                    if (!federation) {
                        errors.push({
                            central: item,
                            status: 'error',
                            message: `Federation not found for federation_code: ${item.federation_code}`,
                        });
                        continue;
                    }
                    let central = centralMap.get(item.central_code);
                    if (central) {
                        if (item.name)
                            central.name = item.name;
                        if (item.address)
                            central.address = item.address;
                        central.federationId = federation.id;
                        updatedCentrals.push(central);
                    }
                    else {
                        const newCentral = this.centralRepository.create({
                            name: item.name,
                            federationId: federation.id,
                            address: item.address,
                            code: item.central_code,
                        });
                        newCentrals.push(newCentral);
                    }
                }
                await this.centralRepository.manager.transaction(async (transactionalEntityManager) => {
                    if (newCentrals.length > 0) {
                        await transactionalEntityManager.save(newCentrals);
                    }
                    if (updatedCentrals.length > 0) {
                        await transactionalEntityManager.save(updatedCentrals);
                    }
                });
                processedCentrals.push(...newCentrals.map((c) => ({
                    id: c.id,
                    name: c.name,
                    federation_code: federationMap.get(c.federationId)?.code || null,
                    address: c.address,
                })), ...updatedCentrals.map((c) => ({
                    id: c.id,
                    name: c.name,
                    federation_code: federationMap.get(c.federationId)?.code || null,
                    address: c.address,
                })));
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some centrals had errors' : 'Centrals processed successfully',
            processedCentrals,
            errors,
        };
    }
    async deleteCentralsFromBulk(codes) {
        const MAX_BATCH_SIZE = 10;
        if (!codes || codes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (codes.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many centrals in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: codes.length,
            });
        }
        const processedCentrals = [];
        const errors = [];
        for (const code of codes) {
            try {
                const central = await this.centralRepository.findOne({ where: { code } });
                if (!central) {
                    throw new common_1.NotFoundException(`Central with code '${code}' not found.`);
                }
                const linkedUsers = await this.usersRepository.find({
                    where: { centralId: central.id },
                    select: ['id', 'name'],
                });
                if (linkedUsers.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete central '${code}' because it is linked to ${linkedUsers.length} user(s).`,
                        linked_users: linkedUsers,
                    });
                }
                const linkedCooperatives = await this.cooperativeRepository.find({
                    where: { centralId: central.id },
                    select: ['code', 'name'],
                });
                if (linkedCooperatives.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete central '${code}' because it is linked to ${linkedCooperatives.length} cooperative(s).`,
                        linked_cooperatives: linkedCooperatives,
                    });
                }
                central.deletedAt = new Date();
                await this.centralRepository.save(central);
                processedCentrals.push({
                    id: central.id,
                    code: central.code,
                    name: central.name,
                    deleted_at: central.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    central_code: code,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some centrals had errors' : 'Centrals deleted successfully',
            processedCentrals,
            errors,
        };
    }
};
exports.CentralsService = CentralsService;
exports.CentralsService = CentralsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(central_entity_1.Central)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(cooperative_entity_1.Cooperative)),
    __param(3, (0, common_1.Inject)((0, common_1.forwardRef)(() => cooperatives_service_1.CooperativesService))),
    __param(4, (0, common_1.Inject)((0, common_1.forwardRef)(() => federations_service_1.FederationsService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        cooperatives_service_1.CooperativesService,
        federations_service_1.FederationsService])
], CentralsService);
//# sourceMappingURL=centrals.service.js.map