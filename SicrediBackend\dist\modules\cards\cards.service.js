"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const card_entity_1 = require("./entities/card.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const card_type_entity_1 = require("../card-types/entities/card-type.entity");
const account_entity_1 = require("../accounts/entities/account.entity");
let CardsService = class CardsService {
    constructor(cardsRepository, accountRepository, cardTypeRepository, cryptography) {
        this.cardsRepository = cardsRepository;
        this.accountRepository = accountRepository;
        this.cardTypeRepository = cardTypeRepository;
        this.cryptography = cryptography;
    }
    async create(createCardDto) {
        const newCard = new card_entity_1.Card();
        newCard.accountId = createCardDto.accountId;
        newCard.holderName = createCardDto.holderName;
        newCard.cardNumber = this.cryptography.encrypt(createCardDto.cardNumber);
        newCard.expirationDate = createCardDto.expirationDate;
        newCard.securityCode = this.cryptography.encrypt(createCardDto.securityCode);
        newCard.cardTypeId = createCardDto.cardTypeId;
        newCard.createdAt = new Date();
        const response = await this.cardsRepository.save(newCard);
        return {
            id: response.id,
            accountId: response.accountId,
            holderName: response.holderName,
            cardNumber: this.cryptography.decrypt(response.cardNumber),
            expirationDate: response.expirationDate,
            securityCode: this.cryptography.decrypt(response.securityCode),
            cardTypeId: response.cardTypeId,
            createdAt: response.createdAt,
        };
    }
    async findAll() {
        const cards = await this.cardsRepository.find();
        return cards;
    }
    async findOne(id) {
        const card = await this.cardsRepository.findOne({ where: { id } });
        return {
            ...card,
            cardNumber: this.cryptography.decrypt(card.cardNumber),
            securityCode: this.cryptography.decrypt(card.securityCode),
        };
    }
    async findRecentByAccountId(accountId) {
        const cards = await this.cardsRepository.find({
            where: { accountId },
            order: {
                createdAt: 'DESC'
            }
        });
        if (cards.length > 0) {
            const card = cards[0];
            return {
                ...card,
                cardNumber: this.cryptography.decrypt(card.cardNumber),
                securityCode: this.cryptography.decrypt(card.securityCode),
            };
        }
        return null;
    }
    async update(id, updateCardDto) {
        const card = await this.cardsRepository.findOne({ where: { id } });
        if (!card) {
            return card;
        }
        card.accountId = updateCardDto.accountId;
        card.holderName = updateCardDto.holderName;
        card.cardNumber = this.cryptography.encrypt(updateCardDto.cardNumber);
        card.expirationDate = updateCardDto.expirationDate;
        card.securityCode = this.cryptography.encrypt(updateCardDto.securityCode);
        card.cardTypeId = updateCardDto.cardTypeId;
        card.updatedAt = new Date();
        const response = await this.cardsRepository.save(card);
        return {
            id: response.id,
            accountId: response.accountId,
            holderName: response.holderName,
            cardNumber: this.cryptography.decrypt(response.cardNumber),
            expirationDate: response.expirationDate,
            securityCode: this.cryptography.decrypt(response.securityCode),
            cardTypeId: response.cardTypeId,
            createdAt: response.createdAt,
            updatedAt: response.updatedAt,
        };
    }
    async remove(id) {
        const card = await this.cardsRepository.findOne({ where: { id } });
        if (!card) {
            return card;
        }
        await this.cardsRepository.update(id, { deletedAt: new Date() });
    }
    async createCardsFromBulk(cardDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!cardDto || cardDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (cardDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many cards in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: cardDto.length,
            });
        }
        const processedCards = [];
        const errors = [];
        for (let i = 0; i < cardDto.length; i += BATCH_SIZE) {
            const batch = cardDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.account_code)
                        missingFields.push(`account_code (index ${i + index})`);
                    if (!item.holder_name)
                        missingFields.push(`holder_name (index ${i + index})`);
                    if (!item.card_number)
                        missingFields.push(`card_number (index ${i + index})`);
                    if (!item.expiration_date)
                        missingFields.push(`expiration_date (index ${i + index})`);
                    if (!item.card_type_name)
                        missingFields.push(`card_type_name (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const accountCodes = batch.map((item) => item.account_code);
                const cardNumbers = batch.map((item) => item.card_number);
                const cardTypeNames = batch.map((item) => item.card_type_name);
                const [accounts, existingCards, existingCardTypes] = await Promise.all([
                    this.accountRepository.find({ where: { code: (0, typeorm_2.In)(accountCodes) } }),
                    this.cardsRepository.find({ where: { cardNumber: (0, typeorm_2.In)(cardNumbers) } }),
                    this.cardTypeRepository.find({ where: { name: (0, typeorm_2.In)(cardTypeNames) } }),
                ]);
                const accountMap = new Map(accounts.map((a) => [a.code, a]));
                const cardMap = new Map(existingCards.map((c) => [c.cardNumber, c]));
                const cardTypeMap = new Map(existingCardTypes.map((ct) => [ct.name, ct]));
                const newCards = [];
                const updatedCards = [];
                for (const item of batch) {
                    const account = accountMap.get(item.account_code);
                    let cardType = cardTypeMap.get(item.card_type_name);
                    let card = cardMap.get(item.card_number);
                    if (!account) {
                        errors.push({
                            card: item,
                            status: 'error',
                            message: `Account not found for account_code: ${item.account_code}`,
                        });
                        continue;
                    }
                    if (!cardType) {
                        cardType = this.cardTypeRepository.create({ name: item.card_type_name });
                        await this.cardTypeRepository.save(cardType);
                        cardTypeMap.set(item.card_type_name, cardType);
                    }
                    if (card) {
                        card.holderName = item.holder_name;
                        card.expirationDate = item.expiration_date;
                        card.cardTypeId = cardType.id;
                        updatedCards.push(card);
                    }
                    else {
                        const newCard = this.cardsRepository.create({
                            accountId: account.id,
                            cardNumber: item.card_number,
                            expirationDate: item.expiration_date,
                            holderName: item.holder_name,
                            cardTypeId: cardType.id,
                        });
                        newCards.push(newCard);
                    }
                }
                await this.cardsRepository.manager.transaction(async (transactionalEntityManager) => {
                    if (newCards.length > 0) {
                        await transactionalEntityManager.save(newCards);
                    }
                    if (updatedCards.length > 0) {
                        await transactionalEntityManager.save(updatedCards);
                    }
                });
                processedCards.push(...newCards.map((c) => ({
                    id: c.id,
                    account_code: accountMap.get(c.accountId)?.code,
                    card_number: c.cardNumber,
                    expiration_date: c.expirationDate,
                    holder_name: c.holderName,
                    card_type_name: cardTypeMap.get(c.cardTypeId)?.name,
                })), ...updatedCards.map((c) => ({
                    id: c.id,
                    account_code: accountMap.get(c.accountId)?.code,
                    card_number: c.cardNumber,
                    expiration_date: c.expirationDate,
                    holder_name: c.holderName,
                    card_type_name: cardTypeMap.get(c.cardTypeId)?.name,
                })));
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some cards had errors' : 'Cards processed successfully',
            processedCards,
            errors,
        };
    }
};
exports.CardsService = CardsService;
exports.CardsService = CardsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(card_entity_1.Card)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.Accounts)),
    __param(2, (0, typeorm_1.InjectRepository)(card_type_entity_1.CardType)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        cryptography_1.Cryptography])
], CardsService);
//# sourceMappingURL=cards.service.js.map