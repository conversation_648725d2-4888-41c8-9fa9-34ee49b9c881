"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const goal_service_1 = require("./goal.service");
const goal_controller_1 = require("./goal.controller");
const goal_entity_1 = require("./entities/goal.entity");
const goal_product_wallet_entity_1 = require("../goal-product-wallet/entities/goal-product-wallet.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const agency_entity_1 = require("../agencies/entities/agency.entity");
const product_entity_1 = require("../products/entities/product.entity");
const user_entity_1 = require("../users/entities/user.entity");
let GoalModule = class GoalModule {
};
exports.GoalModule = GoalModule;
exports.GoalModule = GoalModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([goal_entity_1.Goal, user_entity_1.User, goal_product_wallet_entity_1.GoalProductWallet, wallet_entity_1.Wallet, product_entity_1.Product, agency_entity_1.Agency]),
        ],
        controllers: [goal_controller_1.GoalController],
        providers: [goal_service_1.GoalService],
        exports: [goal_service_1.GoalService],
    })
], GoalModule);
//# sourceMappingURL=goal.module.js.map