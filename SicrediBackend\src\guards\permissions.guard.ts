import {
  CanActivate,
  ExecutionContext,
  Injectable,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionsService } from 'src/modules/permissions/permissions.service';
import { ProfilePermissionsService } from 'src/modules/profile-permissions/profile-permissions.service';
import { UsersService } from 'src/modules/users/users.service';
import { ProfilesService } from 'src/modules/profiles/profiles.service'; // Importa o ProfilesService

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly permissionsService: PermissionsService,
    private readonly profilePermissionsService: ProfilePermissionsService,
    private readonly usersService: UsersService,
    private readonly profilesService: ProfilesService,
  ) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const method = request.method.toLowerCase();

    let resource = this.reflector.get<string>(
      'resource',
      context.getHandler(),
    );

    if (!resource) {
      resource = this.reflector.get<string>('resource', context.getClass());
    }

    if (!resource) {
      console.log('Resource not defined, allowing access');
      return true;
    }

    const operation = this.mapHttpMethodToOperation(method);
    const permissionKey = `${resource.toUpperCase()}_${operation.toUpperCase()}`;

    const keycloakId = request.user?.sub;
    if (!keycloakId) {
      throw new ForbiddenException('Access denied: user not authenticated');
    }

    const user = await this.usersService.findByKeycloakId(keycloakId);
    if (!user) {
      throw new ForbiddenException('Access denied: user not found');
    }

    // Busca o perfil associado ao usuário para obter a hierarquia
    const profile = await this.profilesService.findOne(user.profileId);
    if (!profile) {
      throw new ForbiddenException('Access denied: user profile not found');
    }

    // Adicione as informações ao objeto request
    request.user = {
      id: user.id,
      email: user.email,
      agencyId: (user as any).agencyid,
      cooperativeId: (user as any).cooperativeid,
      centralId: (user as any).centralid,
      federationId: (user as any).federationid,
      profile: { id: user.profileId, hierarchy: profile.hierarchy, key: profile.key },
    };

    // Verifica se o método possui o decorador @NoPermission()
    const skipPermissionCheck = this.reflector.get<boolean>(
      'noPermission',
      context.getHandler(),
    );

    if (skipPermissionCheck) {
      return true; // Permite o acesso sem verificar permissões
    }

    const permissions = await this.permissionsService.findByKey(permissionKey);
    if (!permissions || permissions.length === 0) {
      throw new ForbiddenException(
        `Access denied: permission '${permissionKey}' not found`,
      );
    }

    const hasPermission = await (async () => {
      for (const permission of permissions) {
        const profilePermission =
          await this.profilePermissionsService.findByProfileIdAndPermissionId(
            user.profileId,
            permission.id,
          );
        if (profilePermission) {
          return true;
        }
      }
      return false;
    })();

    if (!hasPermission) {
      throw new ForbiddenException('Access denied: insufficient permissions');
    }

    return true;
  }

  private mapHttpMethodToOperation(method: string): string {
    const map = {
      get: 'view',
      post: 'create',
      patch: 'update',
      put: 'update',
      delete: 'delete',
    };
    return map[method] || 'unknown';
  }
}