import { Module } from '@nestjs/common';
import { ProfilePermissionsService } from './profile-permissions.service';
import { ProfilePermissionsController } from './profile-permissions.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProfilePermission } from './entities/profile-permission.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ProfilePermission])],
  controllers: [ProfilePermissionsController],
  providers: [ProfilePermissionsService],
})
export class ProfilePermissionsModule {}
