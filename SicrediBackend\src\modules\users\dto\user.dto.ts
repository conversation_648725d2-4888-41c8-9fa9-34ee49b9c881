import { ApiProperty } from '@nestjs/swagger';
import { ItemDetailUserDto } from './itemDetail.user.dto';

export class UserDto {
  @ApiProperty({ example: 1, description: 'ID do usuário' })
  id: number;

  @ApiProperty({ example: true, description: 'Usuário ativo' })
  active: boolean;

  @ApiProperty({ example: '<PERSON>', description: 'Nome do usuário' })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Endereço de e-mail',
  })
  email?: string;

  @ApiProperty({ example: 1, description: 'ID de perfil de usuário' })
  profileId?: number;

  @ApiProperty({ example: 'ADMIN', description: 'Key de perfil de usuário' })
  profileKey?: string;

  @ApiProperty({ example: '99999999999', description: 'Telefone do usuário' })
  phone?: string;

  @ApiProperty({
    example: ['CREATE_TABLE', 'UPDATE_RECORD'],
    description: 'Array de possíveis permissões do usuário',
  })
  permissions?: string[];

  @ApiProperty({
    example: 'Administrador',
    description: 'Nome do perfil do usuário',
  })
  profileName?: string;

  @ApiProperty({
    example: 1,
    description: 'Hierarquia do perfil do usuário',
  })
  hierarchy?: number;

  @ApiProperty({
    example: 1,
    description: 'Código da agencia relacionada ao usuario',
  })
  agency_code?: string;

  @ApiProperty({
    example: 1,
    description: 'Nome da central relacionada ao usuario',
  })
  central_name?: string;

  @ApiProperty({
    example: 1,
    description: 'Nome da cooperativa relacionada ao usuario',
  })
  cooperative_name?: string;

  @ApiProperty({
    example: 1,
    description: 'Nome da federação relacionada ao usuario',
  })
  federation_name?: string;

  @ApiProperty({
    example: '03535566000106',
    description: 'cnpj',
  })
  cnpj?: string;

  @ApiProperty({
    example: '02366478062',
    description: 'cpf',
  })
  cpf?: string;

  @ApiProperty({
    example: '',
    description: 'serviceUnitNumber',
  })
  serviceUnitNumber?: string;

  @ApiProperty({
    example: '02/12/2024',
    description: 'birthDate',
  })
  birthDate?: Date;

  @ApiProperty({
    example: 'Santos',
    description: 'lastName',
  })
  lastName?: string;

  @ApiProperty({
    example: 'password123',
    description: 'password',
  })
  password?: string;

  @ApiProperty({
    example: 'idKeyCloak',
    description: 'idKeyCloak',
  })
  idKeyCloak?: string;

}
