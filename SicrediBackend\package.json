{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch --insert-brk", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/axios": "^3.0.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.4.5", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "axios": "^1.7.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "expo-server-sdk": "^3.14.0", "jwt-decode": "^4.0.0", "moment": "^2.30.1", "mysql2": "^3.11.3", "nest-keycloak-connect": "^1.10.1", "nest-winston": "^1.9.7", "node-forge": "^1.3.1", "pg": "^8.13.0", "rate-limiter-flexible": "^6.2.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "winston": "^3.15.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/node-forge": "^1.3.11", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.9.0", "@typescript-eslint/parser": "^8.9.0", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}