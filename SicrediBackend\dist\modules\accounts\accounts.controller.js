"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const create_account_dto_1 = require("./dto/create-account.dto");
const accounts_service_1 = require("./accounts.service");
let AccountsController = class AccountsController {
    constructor(accountsService) {
        this.accountsService = accountsService;
    }
    async createBulk(accountDto) {
        if (!accountDto || accountDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.accountsService.createAccountsFromBulk(accountDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(body) {
        if (!body.accountCodes || body.accountCodes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.accountsService.deleteAccountsFromBulk(body.accountCodes);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async findOne(id) {
        return await this.accountsService.findOne(id);
    }
};
exports.AccountsController = AccountsController;
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar contas em massa',
        description: `
    Este endpoint permite **criar novas contas** ou **atualizar contas já existentes em massa**.
    - Se uma conta já existir **(com base no campo \`code\`)**, os dados serão atualizados com as informações enviadas.
    - O CPF ou CNPJ do associado deve ser informado para vinculação da conta.
    - A requisição pode conter até **20.000 registros** por chamada, sendo processados em lotes de 500.
  `,
    }),
    (0, swagger_1.ApiBody)({
        type: [create_account_dto_1.CreateAccountDto],
        description: 'Array de objetos contendo os dados das contas a serem criadas ou atualizadas.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar múltiplas contas',
                value: [
                    {
                        code: '0001',
                        associate_cpf: '***********',
                        agency_code: '0001',
                        account_type_key: 'INDIVIDUAL',
                        paymentKey: true,
                        lastAccountActivity: '2024-10-01T12:00:00.000Z',
                        automaticDebit: true
                    },
                    {
                        code: '0002',
                        associate_cnpj: '**************',
                        agency_code: '0002',
                        account_type_key: 'LEGAL_ENTITY',
                        paymentKey: true,
                        lastAccountActivity: '2024-09-15T10:30:00.000Z',
                        automaticDebit: false
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Contas criadas ou atualizadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Accounts processed successfully',
                processedAccounts: [
                    {
                        id: 1,
                        code: '0001',
                        agency_code: '0001',
                        associate_cpf: '***********',
                        associate_cnpj: null,
                        account_type_key: 'INDIVIDUAL',
                        paymentKey: true,
                        lastAccountActivity: '2024-10-01T12:00:00.000Z',
                        automaticDebit: true
                    },
                    {
                        id: 2,
                        code: '0002',
                        agency_code: '0002',
                        associate_cpf: null,
                        associate_cnpj: '**************',
                        account_type_key: 'LEGAL_ENTITY',
                        paymentKey: true,
                        lastAccountActivity: '2024-09-15T10:30:00.000Z',
                        automaticDebit: false
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: code, agency_code, associate_cpf or associate_cnpj, account_type_key',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas contas foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedAccounts: [
                    {
                        id: 1,
                        code: '0001',
                        agency_code: '0001',
                        associate_cpf: '***********',
                        associate_cnpj: null,
                        account_type_key: 'INDIVIDUAL',
                        paymentKey: true,
                        lastAccountActivity: '2024-10-01T12:00:00.000Z',
                        automaticDebit: true
                    },
                ],
                errors: [
                    {
                        account: {
                            code: '0003',
                            associate_cnpj: 'INVALID_CNPJ',
                            agency_code: '0003',
                            account_type_key: 'LEGAL_ENTITY',
                            paymentKey: true,
                            lastAccountActivity: '2024-09-20T08:45:00.000Z',
                            automaticDebit: false
                        },
                        status: 'error',
                        message: 'Associate not found for given associate_cpf/associate_cnpj',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AccountsController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir múltiplas contas',
        description: `
          Este endpoint permite a exclusão de múltiplas contas em uma única requisição.
          Caso a conta esteja vinculada a carteiras ou registros de atendimentos, a exclusão será impedida e um erro será retornado.
          A exclusão é feita via *soft delete*.
        `,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de códigos das contas a serem excluídas',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar contas',
                value: {
                    accountCodes: ['AC001', 'AC002'],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Contas excluídas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Accounts deleted successfully',
                processedAccounts: [
                    {
                        id: 1,
                        code: 'AC001',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas contas foram excluídas, mas outras apresentaram erro.',
        schema: {
            example: {
                status: 'partial_success',
                message: 'Some accounts had errors',
                processedAccounts: [
                    {
                        id: 1,
                        code: 'AC001',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        account_code: 'AC002',
                        status: 'error',
                        message: "Cannot delete account 'AC002' because it is linked to 3 wallet(s).",
                        linked_wallets: [
                            { walletId: 10 },
                            { walletId: 11 },
                            { walletId: 12 },
                        ],
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccountsController.prototype, "deleteBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Conta por ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta encontrada com sucesso.',
        type: create_account_dto_1.CreateAccountDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Conta encontrada.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AccountsController.prototype, "findOne", null);
exports.AccountsController = AccountsController = __decorate([
    (0, common_1.Controller)('/api/v1/accounts'),
    __metadata("design:paramtypes", [accounts_service_1.AccountsService])
], AccountsController);
//# sourceMappingURL=accounts.controller.js.map