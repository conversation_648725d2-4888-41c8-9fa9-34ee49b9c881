"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const profile_entity_1 = require("../../profiles/entities/profile.entity");
const typeorm_1 = require("typeorm");
const wallet_entity_1 = require("../../wallets/entities/wallet.entity");
const user_wallets_entity_1 = require("../../user-wallets/entities/user-wallets.entity");
const attendance_entity_1 = require("../../attendances/entities/attendance.entity");
const agency_entity_1 = require("../../agencies/entities/agency.entity");
const cooperative_entity_1 = require("../../cooperatives/entities/cooperative.entity");
const central_entity_1 = require("../../centrals/entities/central.entity");
let User = class User {
};
exports.User = User;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], User.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 50, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "phone", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 255, nullable: false }),
    __metadata("design:type", String)
], User.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'last_name', type: 'varchar', length: 255, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "lastName", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 250, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'profile_id', type: 'integer', nullable: true }),
    __metadata("design:type", Number)
], User.prototype, "profileId", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => attendance_entity_1.Attendance, (attendance) => attendance.attendant),
    __metadata("design:type", Array)
], User.prototype, "attendances", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: true }),
    __metadata("design:type", Boolean)
], User.prototype, "active", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "photo", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'notification_token',
        type: 'varchar',
        length: 300,
        nullable: true,
    }),
    __metadata("design:type", String)
], User.prototype, "notificationToken", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'id_user_keycloak',
        type: 'varchar',
        length: 300,
        nullable: true,
    }),
    __metadata("design:type", String)
], User.prototype, "idUserKeycloak", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'agency_id',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], User.prototype, "agencyId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'central_id',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], User.prototype, "centralId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'federation_id',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], User.prototype, "federationId", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "cnpj", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 300, nullable: true }),
    __metadata("design:type", String)
], User.prototype, "cpf", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'cooperative_id',
        type: 'integer',
        nullable: true,
    }),
    __metadata("design:type", Number)
], User.prototype, "cooperativeId", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'service_unit_number',
        type: 'varchar',
        length: 300,
        nullable: true,
    }),
    __metadata("design:type", String)
], User.prototype, "serviceUnitNumber", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'birth_date',
        type: 'timestamp with time zone',
        nullable: true,
    }),
    __metadata("design:type", Date)
], User.prototype, "birthDate", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'geral_register',
        type: 'varchar',
        length: 300,
        nullable: true,
    }),
    __metadata("design:type", String)
], User.prototype, "geralRegister", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'created_at',
        type: 'timestamp with time zone',
        nullable: false,
        default: () => 'CURRENT_TIMESTAMP',
    }),
    __metadata("design:type", Date)
], User.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'updated_at',
        type: 'timestamp with time zone',
        nullable: true,
    }),
    __metadata("design:type", Date)
], User.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({
        name: 'deleted_at',
        type: 'timestamp with time zone',
        nullable: true,
    }),
    __metadata("design:type", Date)
], User.prototype, "deletedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => profile_entity_1.Profile, (profile) => profile.users),
    (0, typeorm_1.JoinColumn)({ name: 'profile_id' }),
    __metadata("design:type", profile_entity_1.Profile)
], User.prototype, "profile", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => wallet_entity_1.Wallet, (wallet) => wallet.user),
    __metadata("design:type", Array)
], User.prototype, "wallet", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => user_wallets_entity_1.UserWallet, (userWallet) => userWallet.user),
    __metadata("design:type", Array)
], User.prototype, "userWallets", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => agency_entity_1.Agency, (agency) => agency.users),
    (0, typeorm_1.JoinColumn)({ name: 'agency_id' }),
    __metadata("design:type", agency_entity_1.Agency)
], User.prototype, "agency", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => cooperative_entity_1.Cooperative, (cooperative) => cooperative.users),
    (0, typeorm_1.JoinColumn)({ name: 'cooperative_id' }),
    __metadata("design:type", cooperative_entity_1.Cooperative)
], User.prototype, "cooperative", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => central_entity_1.Central, (central) => central.cooperatives),
    (0, typeorm_1.JoinColumn)({ name: 'central_id' }),
    __metadata("design:type", central_entity_1.Central)
], User.prototype, "central", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)()
], User);
//# sourceMappingURL=user.entity.js.map