"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const auth_dto_1 = require("./dto/auth.dto");
const signIn_dto_1 = require("./dto/signIn.dto");
const refreshToken_dto_1 = require("./dto/refreshToken.dto");
const nest_keycloak_connect_1 = require("nest-keycloak-connect");
const user_dto_1 = require("../modules/users/dto/user.dto");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async signIn(signInDto, res) {
        const response = await this.authService.signIn(signInDto.login, signInDto.password);
        try {
            res.cookie(process.env.NODE_ENV === 'production' ? '__Host-token' : 'token', response.access_token, {
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                maxAge: 60 * 60 * 1000,
            });
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        return res.status(200).json({ message: 'Login realizado com sucesso.' });
    }
    logout(res) {
        res.cookie('token', '', {
            secure: false,
            sameSite: 'lax',
            expires: new Date(0),
        });
        return res.status(200).json({ message: 'Logout realizado com sucesso' });
    }
    async refreshToken(refreshTokenDto) {
        const accessToken = await this.authService.refreshAccessToken(refreshTokenDto.refreshToken);
        return { accessToken };
    }
    findByToken(request) {
        return this.authService.findByToken(request.cookies?.token);
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Login de usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Login bem-sucedido.',
        type: auth_dto_1.AuthResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Parâmetros inválidos.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, nest_keycloak_connect_1.Unprotected)(),
    (0, common_1.Post)('/login'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [signIn_dto_1.SignInDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signIn", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Logout de usuário' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Logout realizado com sucesso',
        type: auth_dto_1.AuthResponseDto,
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, nest_keycloak_connect_1.Unprotected)(),
    (0, common_1.Post)('/logout'),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AuthController.prototype, "logout", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualiza o token de acesso' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Token atualizado com sucesso.',
        schema: { example: { accessToken: 'new-access-token' } },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Token de atualização inválido.' }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.Post)('/refresh'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [refreshToken_dto_1.RefreshTokenDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar usuário pelo Token' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Usuário encontrado com sucesso.',
        type: user_dto_1.UserDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Usuário não encontrado.' }),
    (0, common_1.Get)('me'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "findByToken", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('api/v1/auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map