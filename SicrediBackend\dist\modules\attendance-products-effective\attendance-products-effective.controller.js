"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceProductsEffectiveController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const attendance_products_effective_service_1 = require("./attendance-products-effective.service");
const update_attendance_products_effective_dto_1 = require("./dto/update-attendance-products-effective.dto");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
const attendance_product_effective_dto_1 = require("./dto/attendance-product-effective.dto");
let AttendanceProductsEffectiveController = class AttendanceProductsEffectiveController {
    constructor(service) {
        this.service = service;
    }
    async create(dto) {
        if (Array.isArray(dto)) {
            return this.service.createBatch(dto);
        }
        return this.service.create(dto);
    }
    findAllProducts() {
        return this.service.findAllProducts();
    }
    findAllAttendanceProducts() {
        return this.service.findAllAttendanceProducts();
    }
    findAll() {
        return this.service.findAll();
    }
    findOne(id) {
        return this.service.findOne(+id);
    }
    update(id, dto) {
        return this.service.update(+id, dto);
    }
    remove(id) {
        return this.service.remove(+id);
    }
    async createBulk(attendanceProductEffectiveDto) {
        if (!attendanceProductEffectiveDto || attendanceProductEffectiveDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.service.createProductEffectiveFromBulk(attendanceProductEffectiveDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.AttendanceProductsEffectiveController = AttendanceProductsEffectiveController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Cria um novo Efetivado (individual ou em lote)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Criado com sucesso.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AttendanceProductsEffectiveController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca lista de Produtos' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'OK' }),
    (0, common_1.Get)('products'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AttendanceProductsEffectiveController.prototype, "findAllProducts", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca lista de Produtos do Atendimento' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'OK' }),
    (0, common_1.Get)('attendance-products'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AttendanceProductsEffectiveController.prototype, "findAllAttendanceProducts", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca lista de Efetivados' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'OK' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AttendanceProductsEffectiveController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca um Efetivado por ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'OK' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Não encontrado' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AttendanceProductsEffectiveController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualiza um Efetivado existente' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_attendance_products_effective_dto_1.UpdateAttendanceProductEffectiveDto]),
    __metadata("design:returntype", void 0)
], AttendanceProductsEffectiveController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-products-effective'),
    (0, swagger_1.ApiOperation)({ summary: 'Remove (soft delete) um Efetivado pelo ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Removido com sucesso' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Não encontrado' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AttendanceProductsEffectiveController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiExcludeEndpoint)(),
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar registros de produtos efetivos em massa',
        description: `
    Este endpoint permite **criar novos registros** ou **atualizar registros já existentes em massa**.
    **Se um registro já existir (com base nos campos: código da conta, código do produto, CPF/CNPJ do associado e CPF/CNPJ do usuário), seus dados serão atualizados**.
    A requisição pode conter até **20.000 registros** por chamada, sendo processados em lotes de 500.
  `,
    }),
    (0, swagger_1.ApiBody)({
        type: [attendance_product_effective_dto_1.AttendanceProductEffectiveDto],
        description: 'Array de objetos contendo os dados dos produtos efetivos a serem criados ou atualizados.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar registros',
                value: [
                    {
                        account_code: 'ACC123',
                        product_code: 'PROD456',
                        associate_cpf: '***********',
                        user_cpf: '***********',
                        product_value: '150.00',
                        quantity: 2,
                        value_or_quantity: 1,
                    },
                    {
                        account_code: 'ACC789',
                        product_code: 'PROD999',
                        associate_cnpj: '**************',
                        user_cnpj: '**************',
                        product_value: '300.00',
                        quantity: 5,
                        value_or_quantity: 0,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Registros criados ou atualizados com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Records processed successfully',
                processedRecords: [
                    {
                        account_code: 'ACC123',
                        product_code: 'PROD456',
                        associate_cpf: '***********',
                        user_cpf: '***********',
                        product_value: '150.00',
                        quantity: 2,
                        value_or_quantity: 1,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: account_code, product_code, associate_cpf or associate_cnpj, user_cpf or user_cnpj',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Requisição falhou pois um dos itens não foi encontrado.',
        schema: {
            example: {
                status: 'error',
                message: "Product not found for the given product_code: PROD999",
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns registros foram processados com sucesso, mas outros tiveram erros.',
        schema: {
            example: {
                processedRecords: [
                    {
                        account_code: 'ACC789',
                        product_code: 'PROD999',
                        associate_cnpj: '**************',
                        user_cnpj: '**************',
                        product_value: '300.00',
                        quantity: 5,
                        value_or_quantity: 0,
                    },
                ],
                errors: [
                    {
                        record: {
                            account_code: 'INVALID_ACC',
                            product_code: 'INVALID_PROD',
                            associate_cpf: '***********',
                            user_cpf: '***********',
                            product_value: '500.00',
                            quantity: 1,
                            value_or_quantity: 1,
                        },
                        status: 'error',
                        message: 'Account not found for the given account_code: INVALID_ACC',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AttendanceProductsEffectiveController.prototype, "createBulk", null);
exports.AttendanceProductsEffectiveController = AttendanceProductsEffectiveController = __decorate([
    (0, permission_decorator_1.Permission)('attendance-products-effective'),
    (0, common_1.Controller)('api/v1/attendance-products-effective'),
    __metadata("design:paramtypes", [attendance_products_effective_service_1.AttendanceProductsEffectiveService])
], AttendanceProductsEffectiveController);
//# sourceMappingURL=attendance-products-effective.controller.js.map