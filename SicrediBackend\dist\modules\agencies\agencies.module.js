"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgenciesModule = void 0;
const common_1 = require("@nestjs/common");
const agencies_service_1 = require("./agencies.service");
const agencies_controller_1 = require("./agencies.controller");
const typeorm_1 = require("@nestjs/typeorm");
const agency_entity_1 = require("./entities/agency.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const wallet_range_values_module_1 = require("../wallet-range-values/wallet-range-values.module");
const wallets_module_1 = require("../wallets/wallets.module");
const cooperatives_module_1 = require("../cooperatives/cooperatives.module");
const centrals_module_1 = require("../centrals/centrals.module");
const cooperative_entity_1 = require("../cooperatives/entities/cooperative.entity");
let AgenciesModule = class AgenciesModule {
};
exports.AgenciesModule = AgenciesModule;
exports.AgenciesModule = AgenciesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([agency_entity_1.Agency, cooperative_entity_1.Cooperative]),
            (0, common_1.forwardRef)(() => wallet_range_values_module_1.WalletRangeValuesModule),
            (0, common_1.forwardRef)(() => wallets_module_1.WalletsModule),
            cooperatives_module_1.CooperativesModule,
            centrals_module_1.CentralsModule
        ],
        controllers: [agencies_controller_1.AgenciesController],
        providers: [agencies_service_1.AgenciesService, cryptography_1.Cryptography],
        exports: [agencies_service_1.AgenciesService, typeorm_1.TypeOrmModule],
    })
], AgenciesModule);
//# sourceMappingURL=agencies.module.js.map