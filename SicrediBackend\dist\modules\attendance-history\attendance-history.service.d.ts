import { CreateAttendanceHistoryDto } from './dto/create-attendance-history.dto';
import { UpdateAttendanceHistoryDto } from './dto/update-attendance-history.dto';
import { AttendanceHistory } from './entities/attendance-history.entity';
import { Repository } from 'typeorm';
import { ResponseAttendanceDto } from './dto/response-attendance.dto';
export declare class AttendanceHistoryService {
    private readonly attendanceHistoryRepository;
    constructor(attendanceHistoryRepository: Repository<AttendanceHistory>);
    create(createAttendanceHistoryDto: CreateAttendanceHistoryDto): Promise<{
        id: number;
        description: string;
        name: string;
        associateId: number;
        attendantId: number;
        statusId: number;
    }>;
    findAllByAssociate(id: number): Promise<ResponseAttendanceDto[]>;
    findOne(id: number): Promise<AttendanceHistory>;
    update(id: number, updateAttendanceHistoryDto: UpdateAttendanceHistoryDto): Promise<{
        id: number;
        description: string;
        attendanceId: number;
        associateId: number;
        attendantId: number;
        statusId: number;
    }>;
    remove(id: number): Promise<void>;
}
