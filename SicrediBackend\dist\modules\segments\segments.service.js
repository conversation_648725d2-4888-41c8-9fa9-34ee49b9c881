"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SegmentsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const segment_entity_1 = require("./entities/segment.entity");
const wallet_range_value_entity_1 = require("../wallet-range-values/entities/wallet-range-value.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
let SegmentsService = class SegmentsService {
    constructor(segmentRepository, walletsRepository, walletRangeValueRepository) {
        this.segmentRepository = segmentRepository;
        this.walletsRepository = walletsRepository;
        this.walletRangeValueRepository = walletRangeValueRepository;
    }
    async create(createSegmentDto) {
        const alreadyRegistered = await this.findByName(createSegmentDto.name);
        if (alreadyRegistered) {
            throw new common_1.ConflictException(`Central '${createSegmentDto.name}' already registered`);
        }
        const newData = new segment_entity_1.Segment();
        newData.name = createSegmentDto.name;
        newData.number = createSegmentDto.number;
        newData.numberOld = createSegmentDto.numberOld;
        const response = await this.segmentRepository.save(newData);
        return {
            id: response.id,
            name: response.name,
            number: response.number,
            numberOld: response.numberOld,
        };
    }
    async findByName(name) {
        const found = await this.segmentRepository
            .createQueryBuilder("segment")
            .where("LOWER(segment.name) LIKE LOWER(:name)", { name: `%${name}%` })
            .andWhere("segment.deletedAt IS NULL")
            .getOne();
        if (!found) {
            return null;
        }
        if (!found) {
            return null;
        }
        return {
            id: found.id,
            name: found.name,
            number: found.number,
            numberOld: found.numberOld,
        };
    }
    async findAll() {
        return await this.segmentRepository
            .createQueryBuilder('segment')
            .select([
            'segment.id as "id"',
            'segment.name as "name"',
            'segment.number as "number"',
            'segment.number_old as numberOld',
        ])
            .where('segment.deleted_at IS NULL')
            .execute();
    }
    async findOne(identifier) {
        const whereCondition = typeof identifier === "number"
            ? { id: identifier, deletedAt: (0, typeorm_2.IsNull)() }
            : { code: identifier, deletedAt: (0, typeorm_2.IsNull)() };
        const data = await this.segmentRepository.findOneBy(whereCondition);
        if (!data) {
            throw new common_1.NotFoundException(`Segment with identifier ${identifier} not found`);
        }
        return data;
    }
    async findByCodes(codes) {
        if (!codes || codes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'A lista de códigos não pode estar vazia.',
            });
        }
        const centrals = await this.segmentRepository.find({
            where: { code: (0, typeorm_2.In)(codes) },
        });
        if (!centrals || centrals.length === 0) {
            throw new common_1.NotFoundException({
                status: 'error',
                message: 'Nenhuma central encontrada para os códigos fornecidos.',
            });
        }
        return centrals;
    }
    async update(id, updateSegmentDto) {
        await this.findOne(id);
        await this.segmentRepository.update(id, {
            name: updateSegmentDto.name,
            number: updateSegmentDto.number,
            numberOld: updateSegmentDto.numberOld,
            updatedAt: new Date(),
        });
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            name: updated.name,
            number: updated.number,
            numberOld: updated.numberOld,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.segmentRepository.update(id, { deletedAt: new Date() });
    }
    async createSegmentFromBulk(segmentDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!segmentDto || segmentDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (segmentDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many segments in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: segmentDto.length,
            });
        }
        const processedSegments = [];
        const errors = [];
        for (let i = 0; i < segmentDto.length; i += BATCH_SIZE) {
            const batch = segmentDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.name)
                        missingFields.push(`name (index ${i + index})`);
                    if (!item.code)
                        missingFields.push(`code (index ${i + index})`);
                    if (!item.number)
                        missingFields.push(`number (index ${i + index})`);
                    if (!item.numberOld)
                        missingFields.push(`numberOld (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const segmentCodes = batch.map((item) => item.code);
                const existingSegments = await this.segmentRepository.find({
                    where: { code: (0, typeorm_2.In)(segmentCodes) },
                });
                const segmentMap = new Map(existingSegments.map((s) => [s.code, s]));
                const newSegments = [];
                const updatedSegments = [];
                for (const item of batch) {
                    let segment = segmentMap.get(item.code);
                    if (segment) {
                        if (item.name)
                            segment.name = item.name;
                        if (item.number)
                            segment.number = item.number;
                        if (item.numberOld !== undefined)
                            segment.numberOld = item.numberOld;
                        updatedSegments.push(segment);
                    }
                    else {
                        const newSegment = this.segmentRepository.create({
                            name: item.name,
                            number: item.number,
                            code: item.code,
                            numberOld: item.numberOld,
                        });
                        newSegments.push(newSegment);
                    }
                }
                await this.segmentRepository.manager.transaction(async (transactionalEntityManager) => {
                    if (newSegments.length > 0) {
                        await transactionalEntityManager.save(newSegments);
                    }
                    if (updatedSegments.length > 0) {
                        await transactionalEntityManager.save(updatedSegments);
                    }
                });
                processedSegments.push(...newSegments.map((s) => ({
                    id: s.id,
                    name: s.name,
                    code: s.code,
                    number: s.number,
                    numberOld: s.numberOld,
                })), ...updatedSegments.map((s) => ({
                    id: s.id,
                    name: s.name,
                    code: s.code,
                    number: s.number,
                    numberOld: s.numberOld,
                })));
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some segments had errors' : 'Segments processed successfully',
            processedSegments,
            errors,
        };
    }
    async deleteSegmentsFromBulk(codes) {
        const MAX_BATCH_SIZE = 20;
        if (!codes || codes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (codes.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many segments in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: codes.length,
            });
        }
        const processedSegments = [];
        const errors = [];
        for (const code of codes) {
            try {
                const segment = await this.segmentRepository.findOne({ where: { code } });
                if (!segment) {
                    throw new common_1.NotFoundException(`Segment with code '${code}' not found.`);
                }
                const linkedWalletRanges = await this.walletRangeValueRepository.find({
                    where: { segmentId: segment.id },
                    select: ['code', 'name'],
                });
                if (linkedWalletRanges.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete segment '${code}' because it is linked to ${linkedWalletRanges.length} wallet range(s).`,
                        linked_wallet_ranges: linkedWalletRanges,
                    });
                }
                const linkedWallets = await this.walletsRepository.find({
                    where: { segmentId: segment.id },
                    select: ['number', 'name'],
                });
                if (linkedWallets.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete segment '${code}' because it is linked to ${linkedWallets.length} wallet(s).`,
                        linked_wallets: linkedWallets,
                    });
                }
                segment.deletedAt = new Date();
                await this.segmentRepository.save(segment);
                processedSegments.push({
                    id: segment.id,
                    code: segment.code,
                    name: segment.name,
                    deleted_at: segment.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    segment_code: code,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some segments had errors' : 'Segments deleted successfully',
            processedSegments,
            errors,
        };
    }
};
exports.SegmentsService = SegmentsService;
exports.SegmentsService = SegmentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(segment_entity_1.Segment)),
    __param(1, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __param(2, (0, typeorm_1.InjectRepository)(wallet_range_value_entity_1.WalletRangeValue)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], SegmentsService);
//# sourceMappingURL=segments.service.js.map