"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceHistoryService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const attendance_history_entity_1 = require("./entities/attendance-history.entity");
const typeorm_2 = require("typeorm");
let AttendanceHistoryService = class AttendanceHistoryService {
    constructor(attendanceHistoryRepository) {
        this.attendanceHistoryRepository = attendanceHistoryRepository;
    }
    async create(createAttendanceHistoryDto) {
        const newData = new attendance_history_entity_1.AttendanceHistory();
        newData.description = createAttendanceHistoryDto.description;
        newData.name = createAttendanceHistoryDto.name;
        newData.attendanceId = createAttendanceHistoryDto.attendanceId;
        newData.associateId = createAttendanceHistoryDto.associateId;
        newData.attendantId = createAttendanceHistoryDto.attendantId;
        newData.statusId = createAttendanceHistoryDto.statusId;
        const response = await this.attendanceHistoryRepository.save(newData);
        return {
            id: response.id,
            description: response.description,
            name: response.name,
            associateId: response.associateId,
            attendantId: response.attendantId,
            statusId: response.statusId,
        };
    }
    async findAllByAssociate(id) {
        const data = await this.attendanceHistoryRepository
            .createQueryBuilder('attendance_history')
            .select('attendance_history.attendance_id', 'id')
            .addSelect('attendance_history.name', 'name')
            .addSelect('attendance_history.description', 'description')
            .addSelect('attendance_history.created_at', 'createdAt')
            .addSelect('status.description', 'statusDescription')
            .addSelect('status.key', 'statusKey')
            .innerJoin('attendance_status', 'status', 'status.id = attendance_history.status_id')
            .where('attendance_history.deleted_at IS NULL')
            .andWhere('attendance_history.associate_id = :id', { id })
            .orderBy('attendance_history.created_at', 'DESC')
            .execute();
        const attendancesWithProducts = await Promise.all(data.map(async (attendance) => {
            const products = await this.attendanceHistoryRepository
                .createQueryBuilder('attendance_history')
                .select(['product.id as id', 'product.name as name'])
                .innerJoin('attendance_product', 'attendance_product', 'attendance_product.attendance_id = attendance_history.id')
                .innerJoin('product', 'product', 'product.id = attendance_product.product_id')
                .where('attendance_history.id = :id', { id: attendance.id })
                .execute();
            return {
                ...attendance,
                products,
            };
        }));
        return attendancesWithProducts;
    }
    async findOne(id) {
        const data = await this.attendanceHistoryRepository.findOneBy({
            id,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!data) {
            throw new common_1.NotFoundException(`Attendance with ID ${id} not found`);
        }
        return data;
    }
    async update(id, updateAttendanceHistoryDto) {
        await this.findOne(id);
        await this.attendanceHistoryRepository.update(id, {
            description: updateAttendanceHistoryDto.description,
            attendanceId: updateAttendanceHistoryDto.attendanceId,
            associateId: updateAttendanceHistoryDto.associateId,
            attendantId: updateAttendanceHistoryDto.attendantId,
            statusId: updateAttendanceHistoryDto.statusId,
            updatedAt: new Date(),
        });
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            description: updated.description,
            attendanceId: updated.attendanceId,
            associateId: updated.associateId,
            attendantId: updated.attendantId,
            statusId: updated.statusId,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.attendanceHistoryRepository.update(id, {
            deletedAt: new Date(),
        });
    }
};
exports.AttendanceHistoryService = AttendanceHistoryService;
exports.AttendanceHistoryService = AttendanceHistoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(attendance_history_entity_1.AttendanceHistory)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], AttendanceHistoryService);
//# sourceMappingURL=attendance-history.service.js.map