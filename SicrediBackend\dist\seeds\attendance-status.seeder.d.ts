import { OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class AttendanceStatusSeeder implements OnModuleInit {
    private readonly dataSource;
    constructor(dataSource: DataSource);
    onModuleInit(): Promise<void>;
    private getAttendanceStatus;
    private saveAttendanceStatus;
    private seedInProgress;
    private seedCompleted;
    private SeedScheduled;
    private SeedNoContact;
}
