"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const product_entity_1 = require("../modules/products/entities/product.entity");
let ProductSeeder = class ProductSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedConsortium();
        await this.seedContractingCreditCardIncrease();
        await this.seedContractingSpecialCheckIncrease();
        await this.seedCommercialCreditCoObligations();
        await this.seedAutomaticDebit();
        await this.seedTermDeposit();
        await this.seedLCA();
        await this.seedSalaryPortability();
        await this.seedSavings();
        await this.seedHomeInsurance();
    }
    async getProduct(name, ProductRepository) {
        const existing = await ProductRepository.findOne({
            where: { name },
        });
        return existing;
    }
    async saveProduct(existing, name, description, valueOrQuantity, ProductRepository) {
        if (!existing) {
            const product = new product_entity_1.Product();
            product.name = name;
            product.description = description;
            product.valueOrQuantity = valueOrQuantity;
            return await ProductRepository.save(product);
        }
        return false;
    }
    async seedConsortium() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Consórcio', productRepository);
        await this.saveProduct(existingCreate, 'Consórcio', '', 0, productRepository);
    }
    async seedContractingCreditCardIncrease() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Contratação Majoração de cartão', productRepository);
        await this.saveProduct(existingCreate, 'Contratação Majoração de cartão', '', 1, productRepository);
    }
    async seedContractingSpecialCheckIncrease() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Contratação Majoração de cheque especial', productRepository);
        await this.saveProduct(existingCreate, 'Contratação Majoração de cheque especial', '', 1, productRepository);
    }
    async seedCommercialCreditCoObligations() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Crédito Comercial Coobrigações', productRepository);
        await this.saveProduct(existingCreate, 'Crédito Comercial Coobrigações', '', 0, productRepository);
    }
    async seedAutomaticDebit() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Débito Automático', productRepository);
        await this.saveProduct(existingCreate, 'Débito Automático', '', 1, productRepository);
    }
    async seedTermDeposit() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Depósito à Prazo', productRepository);
        await this.saveProduct(existingCreate, 'Depósito à Prazo', '', 1, productRepository);
    }
    async seedLCA() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('LCA', productRepository);
        await this.saveProduct(existingCreate, 'LCA', '', 0, productRepository);
    }
    async seedSalaryPortability() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Portabilidade Salarial', productRepository);
        await this.saveProduct(existingCreate, 'Portabilidade Salarial', '', 1, productRepository);
    }
    async seedSavings() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Poupança', productRepository);
        await this.saveProduct(existingCreate, 'Poupança', '', 1, productRepository);
    }
    async seedHomeInsurance() {
        const productRepository = this.dataSource.getRepository(product_entity_1.Product);
        const existingCreate = await this.getProduct('Seguro Residencial', productRepository);
        await this.saveProduct(existingCreate, 'Seguro Residencial', '', 0, productRepository);
    }
};
exports.ProductSeeder = ProductSeeder;
exports.ProductSeeder = ProductSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProductSeeder);
//# sourceMappingURL=product.seeder.js.map