"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountTypeController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const create_account_type_dto_1 = require("./dto/create-account-type.dto");
const account_types_service_1 = require("./account-types.service");
let AccountTypeController = class AccountTypeController {
    constructor(accountTypeService) {
        this.accountTypeService = accountTypeService;
    }
    async createBulk(accountTypeDto) {
        if (!accountTypeDto || accountTypeDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.accountTypeService.createAccountTypeFromBulk(accountTypeDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(body) {
        if (!body.keys || body.keys.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.accountTypeService.deleteAccountTypesFromBulk(body.keys);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.AccountTypeController = AccountTypeController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplos tipos de conta em massa',
        description: `
            Este endpoint permite criar novos tipos de conta ou atualizar tipos já existentes.
            Se um tipo de conta já existir (com base no nome), seus dados serão atualizados.
        `,
    }),
    (0, swagger_1.ApiBody)({
        type: [create_account_type_dto_1.CreateAccountTypeDto],
        description: 'Array de objetos contendo os dados dos tipos de conta a serem criados ou atualizados.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar tipos de conta',
                value: [
                    {
                        name: 'PESSOA FÍSICA',
                        key: 'INDIVIDUAL'
                    },
                    {
                        name: 'PESSOA JURÍDICA',
                        key: 'LEGAL_ENTITY'
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Tipos de conta criados ou atualizados com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Account types processed successfully',
                processedAccountTypes: [
                    {
                        id: 1,
                        name: 'PESSOA FÍSICA',
                        key: 'INDIVIDUAL'
                    },
                    {
                        id: 2,
                        name: 'PESSOA JURÍDICA',
                        key: 'LEGAL_ENTITY'
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: name, key',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AccountTypeController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir múltiplos tipos de conta',
        description: `
            Este endpoint permite a exclusão de múltiplos tipos de conta em uma única requisição.
            Se um tipo de conta estiver vinculado a alguma conta, a exclusão será impedida.
            A exclusão é feita via *soft delete*.
        `,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de chaves dos tipos de conta a serem excluídos',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar tipos de conta',
                value: {
                    keys: ['PF', 'PJ'],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Tipos de conta excluídos com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Account types deleted successfully',
                processedAccountTypes: [
                    {
                        id: 1,
                        name: 'Pessoa Física',
                        key: 'PF',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns tipos de conta foram excluídos, mas outros apresentaram erro.',
        schema: {
            example: {
                status: 'partial_success',
                message: 'Some account types had errors',
                processedAccountTypes: [
                    {
                        id: 1,
                        name: 'Pessoa Física',
                        key: 'PF',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        account_type_key: 'PJ',
                        status: 'error',
                        message: "Cannot delete account type 'PJ' because it is linked to 3 account(s).",
                        linked_accounts: [
                            { id: 10, code: 'AC001' },
                            { id: 11, code: 'AC002' },
                            { id: 12, code: 'AC003' },
                        ],
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccountTypeController.prototype, "deleteBulk", null);
exports.AccountTypeController = AccountTypeController = __decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, common_1.Controller)('/api/v1/account-types'),
    __metadata("design:paramtypes", [account_types_service_1.AccountTypeService])
], AccountTypeController);
//# sourceMappingURL=account-types.controller.js.map