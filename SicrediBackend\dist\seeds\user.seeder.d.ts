import { DataSource } from 'typeorm';
import { KeycloakService } from 'src/modules/keycloak/keycloak.service';
import { ConfigService } from '@nestjs/config';
import { Cryptography } from 'src/common/functions/cryptography';
export declare class UserSeeder {
    private readonly configService;
    private readonly dataSource;
    private readonly keycloak;
    private readonly cryptography;
    private readonly keycloakClientSecret;
    constructor(configService: ConfigService, dataSource: DataSource, keycloak: KeycloakService, cryptography: Cryptography);
    executeSeed(): Promise<void>;
    private seedUsers;
    private createKeycloakUser;
}
