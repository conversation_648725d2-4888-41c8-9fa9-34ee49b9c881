"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CooperativeSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const central_entity_1 = require("../modules/centrals/entities/central.entity");
const cooperative_entity_1 = require("../modules/cooperatives/entities/cooperative.entity");
let CooperativeSeeder = class CooperativeSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedCooperative();
    }
    async getCentralByName(name, centralRepository) {
        return await centralRepository.findOne({ where: { name: name } });
    }
    async getCooperative(name, address, cooperativeRepository) {
        return await cooperativeRepository.findOne({
            where: { name, address },
        });
    }
    async saveCooperative(existing, central, cooperativeRepository) {
        if (!existing) {
            const cooperative = new cooperative_entity_1.Cooperative();
            cooperative.centralId = central.id;
            cooperative.central = central;
            cooperative.name = 'Cooperativa Central Exemplo';
            cooperative.address = 'Rua Exemplo, 456 - Centro';
            return await cooperativeRepository.save(cooperative);
        }
        return false;
    }
    async executeSeed() {
        await this.seedCooperative();
    }
    async seedCooperative() {
        const cooperativeRepository = this.dataSource.getRepository(cooperative_entity_1.Cooperative);
        const centralRepository = this.dataSource.getRepository(central_entity_1.Central);
        const central = await this.getCentralByName('Central Exemplo', centralRepository);
        if (!central) {
            console.error('Central with name "Central Exemplo" does not exist. Please create it first.');
            return;
        }
        const existingCooperative = await this.getCooperative('Cooperativa Central Exemplo', 'Rua Exemplo, 456 - Centro', cooperativeRepository);
        await this.saveCooperative(existingCooperative, central, cooperativeRepository);
    }
};
exports.CooperativeSeeder = CooperativeSeeder;
exports.CooperativeSeeder = CooperativeSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], CooperativeSeeder);
//# sourceMappingURL=cooperative.seeder.js.map