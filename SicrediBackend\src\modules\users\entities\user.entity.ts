import { Profile } from 'src/modules/profiles/entities/profile.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Wallet } from '../../wallets/entities/wallet.entity';
import { UserWallet } from '../../user-wallets/entities/user-wallets.entity';
import { Attendance } from 'src/modules/attendances/entities/attendance.entity';
import { Agency } from 'src/modules/agencies/entities/agency.entity';
import { Cooperative } from 'src/modules/cooperatives/entities/cooperative.entity';
import { Central } from 'src/modules/centrals/entities/central.entity';

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 50, nullable: true })
  phone: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ name: 'last_name', type: 'varchar', length: 255, nullable: true })
  lastName: string;

  @Column({ type: 'varchar', length: 250, nullable: true })
  email: string;

  @Column({ name: 'profile_id', type: 'integer', nullable: true })
  profileId: number;

  @OneToMany(() => Attendance, (attendance) => attendance.attendant)
  attendances: Attendance[];

  @Column({ type: 'boolean', default: true })
  active: boolean;

  @Column({ type: 'varchar', length: 300, nullable: true })
  photo: string;

  @Column({
    name: 'notification_token',
    type: 'varchar',
    length: 300,
    nullable: true,
  })
  notificationToken: string;

  @Column({
    name: 'id_user_keycloak',
    type: 'varchar',
    length: 300,
    nullable: true,
  })
  idUserKeycloak: string;

  @Column({
    name: 'agency_id',
    type: 'integer',
    nullable: true,
  })
  agencyId: number;

  @Column({
    name: 'central_id',
    type: 'integer',
    nullable: true,
  })
  centralId: number;

  @Column({
    name: 'federation_id',
    type: 'integer',
    nullable: true,
  })
  federationId: number;

  @Column({ type: 'varchar', length: 300, nullable: true })
  cnpj: string;

  @Column({ type: 'varchar', length: 300, nullable: true })
  cpf: string;

  @Column({
    name: 'cooperative_id',
    type: 'integer',
    nullable: true,
  })
  cooperativeId: number;

  @Column({
    name: 'service_unit_number',
    type: 'varchar',
    length: 300,
    nullable: true,
  })
  serviceUnitNumber: string;

  @Column({
    name: 'birth_date',
    type: 'timestamp with time zone',
    nullable: true,
  })
  birthDate: Date;

  @Column({
    name: 'geral_register',
    type: 'varchar',
    length: 300,
    nullable: true,
  })
  geralRegister: string;

  @Column({
    name: 'created_at',
    type: 'timestamp with time zone',
    nullable: false,
    default: () => 'CURRENT_TIMESTAMP',
  })
  createdAt: Date;

  @Column({
    name: 'updated_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  updatedAt: Date;

  @Column({
    name: 'deleted_at',
    type: 'timestamp with time zone',
    nullable: true,
  })
  deletedAt: Date;

  @ManyToOne(() => Profile, (profile) => profile.users)
  @JoinColumn({ name: 'profile_id' })
  profile: Profile;

  @OneToMany(() => Wallet, (wallet) => wallet.user)
  wallet: Wallet[];

  @OneToMany(() => UserWallet, (userWallet) => userWallet.user)
  userWallets: UserWallet[];

  @ManyToOne(() => Agency, (agency) => agency.users)
  @JoinColumn({ name: 'agency_id' })
  agency: Agency;

  @ManyToOne(() => Cooperative, (cooperative) => cooperative.users)
  @JoinColumn({ name: 'cooperative_id' })
  cooperative: Cooperative;

  @ManyToOne(() => Central, (central) => central.cooperatives)
  @JoinColumn({ name: 'central_id' })
  central: Central; // <--- Cooperative -> Central
}
