import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
export declare function ApiFile(fieldName?: string, required?: boolean, localOptions?: MulterOptions): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
export declare function ApiImageFile(fileName?: string, required?: boolean): <TFunction extends Function, Y>(target: TFunction | object, propertyKey?: string | symbol, descriptor?: TypedPropertyDescriptor<Y>) => void;
