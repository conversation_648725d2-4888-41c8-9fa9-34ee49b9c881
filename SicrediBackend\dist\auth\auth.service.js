"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
const axios_1 = require("@nestjs/axios");
const jwt_decode_1 = require("jwt-decode");
const users_service_1 = require("./../modules/users/users.service");
const typeorm_1 = require("typeorm");
const attendance_entity_1 = require("../modules/attendances/entities/attendance.entity");
const date_fns_1 = require("date-fns");
let AuthService = class AuthService {
    constructor(configService, http, usersService, dataSource) {
        this.configService = configService;
        this.http = http;
        this.usersService = usersService;
        this.dataSource = dataSource;
        this.keycloakUrl = this.configService.get('KEYCLOAK_URL');
        this.keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        this.keycloakClientId =
            this.configService.get('KEYCLOAK_CLIENT_ID');
        this.keycloakClientSecret = this.configService.get('KEYCLOAK_CLIENT_SECRET');
    }
    async signIn(login, password) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/realms/${this.keycloakRealm}/protocol/openid-connect/token`, new URLSearchParams({
                client_id: this.keycloakClientId,
                client_secret: this.keycloakClientSecret,
                grant_type: 'password',
                username: login,
                password,
            })));
            if (status != 200) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            if (!data.access_token) {
                throw new common_1.UnauthorizedException('Invalid Access Token');
            }
            const decodedToken = (0, jwt_decode_1.jwtDecode)(data.access_token);
            if (!decodedToken?.sub) {
                throw new common_1.UnauthorizedException('Invalid Access Token');
            }
            try {
                const user = await this.usersService.findByIdKeyCloeker({
                    idUserKeycloak: decodedToken.sub,
                });
                if (user.profileKey === 'WALLET_MANAGER' ||
                    user.profileKey === 'ASSISTANT') {
                    const queryRunner = this.dataSource.createQueryRunner();
                    const todayStart = (0, date_fns_1.startOfDay)(new Date());
                    const todayEnd = (0, date_fns_1.endOfDay)(new Date());
                    const attendanceCount = await queryRunner.manager
                        .getRepository(attendance_entity_1.Attendance)
                        .createQueryBuilder('attendance')
                        .where('attendance.attendant_id = :id', { id: user.id })
                        .andWhere('attendance.created_at BETWEEN :start AND :end', {
                        start: todayStart,
                        end: todayEnd,
                    })
                        .getCount();
                    data.user = {
                        ...user,
                        hasAttendedToday: attendanceCount > 0,
                    };
                }
                else {
                    data.user = user;
                }
            }
            catch (error) {
                console.log(error);
            }
            return data;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
    async refreshAccessToken(refreshToken) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/realms/${this.keycloakRealm}/protocol/openid-connect/token`, new URLSearchParams({
                client_id: this.keycloakClientId,
                client_secret: this.keycloakClientSecret,
                grant_type: 'refresh_token',
                refresh_token: refreshToken,
            })));
            if (status != 200) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            return data;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
    async validateToken(token) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.get(`${this.keycloakUrl}/realms/${this.keycloakRealm}/protocol/openid-connect/userinfo`, {
                headers: {
                    Authorization: `Bearer ${token}`,
                },
            }));
            if (status !== 200) {
                throw new common_1.UnauthorizedException('Invalid token');
            }
            return data;
        }
        catch (e) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    async findByToken(token) {
        try {
            const decodedToken = (0, jwt_decode_1.jwtDecode)(token);
            if (!decodedToken?.sub) {
                throw new common_1.UnauthorizedException('Invalid Access Token');
            }
            const user = await this.usersService.findByIdKeyCloeker({
                idUserKeycloak: decodedToken.sub,
            });
            return user;
        }
        catch (e) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService,
        users_service_1.UsersService,
        typeorm_1.DataSource])
], AuthService);
//# sourceMappingURL=auth.service.js.map