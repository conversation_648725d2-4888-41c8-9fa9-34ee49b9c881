"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const profile_entity_1 = require("./entities/profile.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const queryHelper_1 = require("../../common/functions/queryHelper");
let ProfilesService = class ProfilesService {
    constructor(profileRepository, cryptography) {
        this.profileRepository = profileRepository;
        this.cryptography = cryptography;
    }
    async create(createProfileDto) {
        const alreadyRegistered = await this.findByName(createProfileDto.name);
        if (alreadyRegistered) {
            throw new common_1.ConflictException(`Central '${createProfileDto.name}' already registered`);
        }
        const newData = new profile_entity_1.Profile();
        newData.name = createProfileDto.name;
        newData.description = createProfileDto.description;
        newData.email = this.cryptography.encrypt(createProfileDto.email);
        newData.hierarchy = createProfileDto.hierarchy;
        const response = await this.profileRepository.save(newData);
        return {
            id: response.id,
            name: response.name,
            description: response.description,
            email: this.cryptography.decrypt(response.email),
            hierarchy: response.hierarchy,
        };
    }
    async findByName(name) {
        const found = await this.profileRepository.findOneBy({
            name,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!found) {
            return null;
        }
        return {
            id: found.id,
            name: found.name,
            description: found.description,
            email: this.cryptography.decrypt(found.email),
            hierarchy: found.hierarchy,
        };
    }
    async findByKey(key) {
        const found = await this.profileRepository.findOneBy({
            key,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!found) {
            return null;
        }
        return {
            id: found.id,
            name: found.name,
            description: found.description,
            email: this.cryptography.decrypt(found.email),
            hierarchy: found.hierarchy,
        };
    }
    async findAll(userHierarchy) {
        const data = await new queryHelper_1.QueryHelper(this.profileRepository.createQueryBuilder('profile'))
            .select([
            'profile.id',
            'profile.name',
            'profile.description',
            'profile.email',
            'profile.key',
            'profile.hierarchy',
        ])
            .whereHierarchy('>', userHierarchy)
            .getQueryBuilder()
            .getMany();
        const decryptedData = data.map((value) => {
            return {
                ...value,
                email: this.cryptography.decrypt(value.email),
            };
        });
        return decryptedData;
    }
    async findOne(id) {
        const data = await this.profileRepository.findOneBy({
            id,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!data) {
            throw new common_1.NotFoundException(`Central with ID ${id} not found`);
        }
        return { ...data, email: this.cryptography.decrypt(data.email) };
    }
    async update(id, updateData) {
        await this.findOne(id);
        const data = {
            name: updateData.name,
            description: updateData.description,
            email: this.cryptography.encrypt(updateData.email),
            updatedAt: new Date(),
        };
        await this.profileRepository.update(id, data);
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            name: updated.name,
            description: updated.description,
            email: updated.email,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.profileRepository.update(id, { deletedAt: new Date() });
    }
    async createProfileFromBulk(profileDto) {
        const MAX_BATCH_SIZE = 500;
        if (profileDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many profiles in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: profileDto.length,
            });
        }
        const processedProfiles = [];
        const errors = [];
        for (const item of profileDto) {
            try {
                const missingFields = [];
                if (!item.name)
                    missingFields.push('name');
                if (!item.description)
                    missingFields.push('description');
                if (!item.key)
                    missingFields.push('key');
                if (!item.hierarchy)
                    missingFields.push('hierarchy');
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                let existingProfile = await this.profileRepository.findOne({ where: { key: item.key } });
                if (existingProfile) {
                    if (item.name)
                        existingProfile.name = item.name;
                    if (item.description)
                        existingProfile.description = item.description;
                    if (item.email)
                        existingProfile.email = item.email;
                    if (item.hierarchy)
                        existingProfile.hierarchy = item.hierarchy;
                    const updatedProfile = await this.profileRepository.save(existingProfile);
                    processedProfiles.push({
                        id: updatedProfile.id,
                        name: updatedProfile.name,
                        description: updatedProfile.description,
                        key: updatedProfile.key,
                        email: updatedProfile.email,
                        hierarchy: updatedProfile.hierarchy,
                    });
                }
                else {
                    const newProfile = new profile_entity_1.Profile();
                    newProfile.name = item.name;
                    newProfile.description = item.description;
                    newProfile.key = item.key;
                    newProfile.email = item.email;
                    newProfile.hierarchy = item.hierarchy;
                    const savedProfile = await this.profileRepository.save(newProfile);
                    processedProfiles.push({
                        id: savedProfile.id,
                        name: savedProfile.name,
                        description: savedProfile.description,
                        key: savedProfile.key,
                        email: savedProfile.email,
                        hierarchy: savedProfile.hierarchy,
                    });
                }
            }
            catch (error) {
                errors.push({
                    profile: item,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some profiles had errors' : 'Profiles processed successfully',
            processedProfiles,
            errors,
        };
    }
};
exports.ProfilesService = ProfilesService;
exports.ProfilesService = ProfilesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(profile_entity_1.Profile)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        cryptography_1.Cryptography])
], ProfilesService);
//# sourceMappingURL=profiles.service.js.map