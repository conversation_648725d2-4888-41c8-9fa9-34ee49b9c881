"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceHistoryModule = void 0;
const common_1 = require("@nestjs/common");
const attendance_history_service_1 = require("./attendance-history.service");
const attendance_history_controller_1 = require("./attendance-history.controller");
const typeorm_1 = require("@nestjs/typeorm");
const attendance_history_entity_1 = require("./entities/attendance-history.entity");
let AttendanceHistoryModule = class AttendanceHistoryModule {
};
exports.AttendanceHistoryModule = AttendanceHistoryModule;
exports.AttendanceHistoryModule = AttendanceHistoryModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([attendance_history_entity_1.AttendanceHistory])],
        controllers: [attendance_history_controller_1.AttendanceHistoryController],
        providers: [attendance_history_service_1.AttendanceHistoryService],
    })
], AttendanceHistoryModule);
//# sourceMappingURL=attendance-history.module.js.map