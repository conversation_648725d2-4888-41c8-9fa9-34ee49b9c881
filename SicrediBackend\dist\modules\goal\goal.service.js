"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoalService = void 0;
const common_1 = require("@nestjs/common");
const goal_entity_1 = require("./entities/goal.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const goal_product_wallet_entity_1 = require("../goal-product-wallet/entities/goal-product-wallet.entity");
const user_entity_1 = require("../users/entities/user.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const agency_entity_1 = require("../agencies/entities/agency.entity");
const product_entity_1 = require("../products/entities/product.entity");
let GoalService = class GoalService {
    constructor(goalRepository, userRepository, walletRepository, goalProductWalletRepository, agencyRepository, productRepository, dataSource) {
        this.goalRepository = goalRepository;
        this.userRepository = userRepository;
        this.walletRepository = walletRepository;
        this.goalProductWalletRepository = goalProductWalletRepository;
        this.agencyRepository = agencyRepository;
        this.productRepository = productRepository;
        this.dataSource = dataSource;
    }
    async findPaginatedGoals(user, paginationParams) {
        const loggedUser = await this.userRepository.findOneBy({
            idUserKeycloak: user.sub,
        });
        if (!loggedUser) {
            throw new common_1.NotFoundException('User not found');
        }
        const { page = 1, limit = 10 } = paginationParams;
        const isPagination = page && limit;
        const validPage = isPagination ? Math.max(1, Number(page)) : 1;
        const validLimit = isPagination ? Math.max(1, Number(limit)) : null;
        const offset = isPagination && validLimit ? (validPage - 1) * validLimit : 0;
        const queryBuilder = this.goalRepository
            .createQueryBuilder('goal')
            .select('goal.id', 'id')
            .addSelect('goal.start', 'start')
            .addSelect('goal.end', 'end')
            .distinct(true)
            .leftJoin('goal_product_wallet', 'goalProductWallets', 'goalProductWallets.goalId = goal.id')
            .leftJoin('wallet', 'wallet', 'wallet.id = goalProductWallets.wallet_id')
            .where('goal.deleted_at IS NULL')
            .andWhere('wallet.agency_id = :userAgencyId', {
            userAgencyId: user.agencyId,
        })
            .orderBy('goal.id', 'ASC');
        if (isPagination && validLimit) {
            queryBuilder.offset(offset).limit(validLimit);
        }
        const totalItems = await queryBuilder.getCount();
        const results = await queryBuilder.getRawMany();
        const totalPages = Math.ceil(totalItems / limit);
        return {
            items: results,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findOne(id) {
        const data = await this.goalRepository.findOneBy({
            id,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!data) {
            throw new common_1.NotFoundException(`Goal with ID ${id} not found`);
        }
        const queryRunner = this.dataSource.createQueryRunner();
        const goalProducts = await queryRunner.manager
            .getRepository(goal_product_wallet_entity_1.GoalProductWallet)
            .createQueryBuilder('goal_product_wallet')
            .select('goal_product_wallet.id', 'id')
            .addSelect('goal_product_wallet.goal_id', 'goalId')
            .addSelect('goal_product_wallet.product_id', 'productId')
            .addSelect('product.name', 'productName')
            .addSelect('wallet.id', 'walletId')
            .addSelect('wallet.name', 'walletName')
            .addSelect('goal_product_wallet.value', 'value')
            .addSelect('product.value_or_quantity', 'valueOrQuantity')
            .addSelect('goal_product_wallet.quantity', 'quantity')
            .innerJoin('product', 'product', 'goal_product_wallet.product_id = product.id and goal_product_wallet.deleted_at IS NULL')
            .leftJoin('wallet', 'wallet', 'goal_product_wallet.wallet_id = wallet.id and wallet.deleted_at IS NULL')
            .where('goal_product_wallet.goal_id = :goalId', { goalId: id })
            .execute();
        return { ...data, productWallets: goalProducts };
    }
    async create(createGoalDto) {
        const queryRunner = this.dataSource.createQueryRunner();
        let response;
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const newData = new goal_entity_1.Goal();
            newData.start = createGoalDto.start;
            newData.end = createGoalDto.end;
            response = await queryRunner.manager.save(newData);
            const goalProducts = await Promise.all(createGoalDto.productWallets.map(async (item) => {
                const newProductWallet = new goal_product_wallet_entity_1.GoalProductWallet();
                newProductWallet.goalId = response.id;
                newProductWallet.productId = item.productId;
                newProductWallet.value = item.value;
                newProductWallet.quantity = item.quantity;
                newProductWallet.walletId = item.walletId;
                const productWallets = await queryRunner.manager.save(newProductWallet);
                return {
                    id: productWallets.id,
                    goalId: productWallets.goalId,
                    productId: productWallets.productId,
                    value: productWallets.value,
                    quantity: productWallets.quantity,
                    walletId: productWallets.walletId,
                };
            }));
            response.productWallets = goalProducts;
            await queryRunner.commitTransaction();
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw new common_1.BadRequestException('Erro in transaction');
        }
        finally {
            await queryRunner.release();
        }
        return {
            id: response.id,
            start: response.start,
            end: response.end,
            productWallets: response.productWallets,
        };
    }
    async update(id, updateGoalDto) {
        const existingGoal = await this.findOne(id);
        if (!existingGoal) {
            throw new common_1.NotFoundException(`Goal with ID ${id} not found`);
        }
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();
        try {
            const goal = await queryRunner.manager.preload(goal_entity_1.Goal, {
                id,
            });
            goal.start = updateGoalDto.start;
            goal.end = updateGoalDto.end;
            goal.updatedAt = new Date();
            await queryRunner.manager.save(goal);
            const idListProducts = [];
            const goalProducts = await Promise.all(updateGoalDto.productWallets.map(async (item) => {
                let existingGoalProduct;
                if (item.id) {
                    existingGoalProduct = await queryRunner.manager.preload(goal_product_wallet_entity_1.GoalProductWallet, {
                        id: item.id,
                    });
                }
                else {
                    existingGoalProduct = new goal_product_wallet_entity_1.GoalProductWallet();
                }
                existingGoalProduct.goalId = goal.id;
                existingGoalProduct.productId = item.productId;
                existingGoalProduct.walletId = item.walletId;
                existingGoalProduct.value = item.value;
                existingGoalProduct.quantity = item.quantity;
                let updatedGoalProduct;
                existingGoalProduct.updatedAt = new Date();
                updatedGoalProduct =
                    await queryRunner.manager.save(existingGoalProduct);
                idListProducts.push(updatedGoalProduct.id);
                return {
                    id: updatedGoalProduct.id,
                    goalId: updatedGoalProduct.goalId,
                    productId: updatedGoalProduct.productId,
                    value: updatedGoalProduct.value,
                    quantity: updatedGoalProduct.quantity,
                    walletId: updatedGoalProduct.walletId,
                };
            }));
            const originalProducts = await queryRunner.manager
                .getRepository(goal_product_wallet_entity_1.GoalProductWallet)
                .createQueryBuilder('goalProducts')
                .select('goalProducts.id', 'id')
                .where('goalProducts.goal_id = :goalId', {
                goalId: id,
            })
                .execute();
            await Promise.all(originalProducts.map(async (product) => {
                if (!idListProducts.includes(product.id)) {
                    const goalProducts = await queryRunner.manager.preload(goal_product_wallet_entity_1.GoalProductWallet, {
                        id: product.id,
                    });
                    await queryRunner.manager.save(goal_product_wallet_entity_1.GoalProductWallet, {
                        ...goalProducts,
                        deletedAt: new Date(),
                    });
                }
            }));
            await queryRunner.commitTransaction();
            const response = await this.findOne(id);
            response.productWallets = goalProducts;
            return response;
        }
        catch (error) {
            await queryRunner.rollbackTransaction();
            throw new common_1.BadRequestException('Erro in transaction');
        }
        finally {
            await queryRunner.release();
        }
    }
    async remove(id) {
        await this.findOne(id);
        await this.goalRepository.update(id, { deletedAt: new Date() });
    }
    async createGoalFromBulk(goalDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!goalDto || goalDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (goalDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many goals in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: goalDto.length,
            });
        }
        const processedGoals = [];
        const errors = [];
        const parseDate = (dateStr) => {
            if (dateStr instanceof Date) {
                return dateStr;
            }
            const [day, month, year] = dateStr.split('/').map(Number);
            return new Date(year, month - 1, day);
        };
        for (let i = 0; i < goalDto.length; i += BATCH_SIZE) {
            const batch = goalDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.initial_date)
                        missingFields.push(`initial_date (index ${i + index})`);
                    if (!item.final_date)
                        missingFields.push(`final_date (index ${i + index})`);
                    if (!item.product_code)
                        missingFields.push(`product_code (index ${i + index})`);
                    if (!item.wallet_number && !item.wallet_number_old) {
                        missingFields.push(`wallet_number ou wallet_number_old (index ${i + index})`);
                    }
                    if (!item.agency_code)
                        missingFields.push(`agency_code (index ${i + index})`);
                    if (!item.value)
                        missingFields.push(`value (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const transformedBatch = batch.map((item) => ({
                    ...item,
                    initial_date: parseDate(item.initial_date),
                    final_date: parseDate(item.final_date),
                }));
                const agencyCodes = transformedBatch.map((item) => item.agency_code);
                const productCodes = transformedBatch.map((item) => item.product_code);
                const walletNumbers = transformedBatch.map((item) => item.wallet_number).filter(Boolean);
                const walletNumbersOld = transformedBatch.map((item) => item.wallet_number_old).filter(Boolean);
                const [agencies, wallets, walletsOld, products, existingGoals] = await Promise.all([
                    this.agencyRepository.find({ where: { agencyCode: (0, typeorm_2.In)(agencyCodes) } }),
                    this.walletRepository.find({ where: { number: (0, typeorm_2.In)(walletNumbers) } }),
                    this.walletRepository.find({ where: { numberOld: (0, typeorm_2.In)(walletNumbersOld) } }),
                    this.productRepository.find({ where: { code: (0, typeorm_2.In)(productCodes) } }),
                    this.goalRepository.find({
                        where: transformedBatch.map((item) => ({
                            start: item.initial_date,
                            end: item.final_date,
                        })),
                    }),
                ]);
                const agencyMap = new Map(agencies.map((a) => [a.agencyCode, a]));
                const walletMap = new Map(wallets.map((w) => [w.number, w]));
                const walletOldMap = new Map(walletsOld.map((w) => [w.numberOld, w]));
                const productMap = new Map(products.map((p) => [p.code, p]));
                const newGoals = [];
                const updatedGoals = [];
                const gpwDrafts = [];
                for (const item of transformedBatch) {
                    const agency = agencyMap.get(item.agency_code);
                    const wallet = walletMap.get(item.wallet_number) || walletOldMap.get(item.wallet_number_old);
                    const product = productMap.get(item.product_code);
                    if (!agency || !wallet || !product) {
                        errors.push({
                            goal: item,
                            status: 'error',
                            message: [
                                !product ? `Product not found for product_code: ${item.product_code}` : '',
                                !wallet
                                    ? `Wallet not found (wallet_number: ${item.wallet_number}, wallet_number_old: ${item.wallet_number_old})`
                                    : '',
                                !agency ? `Agency not found (agency_code: ${item.agency_code})` : '',
                            ]
                                .filter(Boolean)
                                .join('. '),
                        });
                        continue;
                    }
                    let goal = existingGoals.find((g) => g.start.getTime() === item.initial_date.getTime() && g.end.getTime() === item.final_date.getTime());
                    if (!goal) {
                        goal = this.goalRepository.create({
                            start: item.initial_date,
                            end: item.final_date,
                        });
                        newGoals.push(goal);
                    }
                    else {
                        goal.start = item.initial_date;
                        goal.end = item.final_date;
                        updatedGoals.push(goal);
                    }
                    gpwDrafts.push(item);
                    processedGoals.push({
                        initial_date: item.initial_date,
                        final_date: item.final_date,
                        product_code: item.product_code,
                        wallet_number: item.wallet_number,
                        wallet_number_old: item.wallet_number_old,
                        agency_code: item.agency_code,
                        value: item.value,
                    });
                }
                await this.goalRepository.manager.transaction(async (manager) => {
                    if (newGoals.length > 0) {
                        await manager.save(newGoals);
                    }
                    if (updatedGoals.length > 0) {
                        await manager.save(updatedGoals);
                    }
                    const goalProductWalletsToSave = [];
                    for (const draft of gpwDrafts) {
                        const goal = [...newGoals, ...updatedGoals, ...existingGoals].find((g) => g.start.getTime() === draft.initial_date.getTime() &&
                            g.end.getTime() === draft.final_date.getTime());
                        if (!goal)
                            continue;
                        const product = productMap.get(draft.product_code);
                        const wallet = walletMap.get(draft.wallet_number) || walletOldMap.get(draft.wallet_number_old);
                        if (!product || !wallet)
                            continue;
                        let gpw = await manager.findOne(goal_product_wallet_entity_1.GoalProductWallet, {
                            where: {
                                goalId: goal.id,
                                productId: product.id,
                                walletId: wallet.id,
                            },
                        });
                        if (!gpw) {
                            gpw = this.goalProductWalletRepository.create({
                                goalId: goal.id,
                                productId: product.id,
                                walletId: wallet.id,
                                value: draft.value.toString(),
                            });
                        }
                        else {
                            gpw.value = draft.value.toString();
                        }
                        goalProductWalletsToSave.push(gpw);
                    }
                    if (goalProductWalletsToSave.length > 0) {
                        await manager.save(goalProductWalletsToSave);
                    }
                });
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some goals had errors' : 'Goals processed successfully',
            processedGoals,
            errors,
        };
    }
};
exports.GoalService = GoalService;
exports.GoalService = GoalService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(goal_entity_1.Goal)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __param(3, (0, typeorm_1.InjectRepository)(goal_product_wallet_entity_1.GoalProductWallet)),
    __param(4, (0, typeorm_1.InjectRepository)(agency_entity_1.Agency)),
    __param(5, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.DataSource])
], GoalService);
//# sourceMappingURL=goal.service.js.map