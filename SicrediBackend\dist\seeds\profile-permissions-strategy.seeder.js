"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfilePermissionsStrategySeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const profile_entity_1 = require("../modules/profiles/entities/profile.entity");
const permission_entity_1 = require("../modules/permissions/entities/permission.entity");
const profile_permission_entity_1 = require("../modules/profile-permissions/entities/profile-permission.entity");
let ProfilePermissionsStrategySeeder = class ProfilePermissionsStrategySeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const profileRepository = this.dataSource.getRepository(profile_entity_1.Profile);
        const permissionRepository = this.dataSource.getRepository(permission_entity_1.Permission);
        const profilePermissionRepository = this.dataSource.getRepository(profile_permission_entity_1.ProfilePermission);
        const profilePermissionsMapping = [
            {
                profileKey: 'ADMIN',
                permissionKeys: [
                    'STRATEGY_CREATE',
                    'STRATEGY_UPDATE',
                    'STRATEGY_VIEW',
                    'STRATEGY_DELETE',
                ],
            },
            {
                profileKey: 'COOPERATIVE',
                permissionKeys: [
                    'STRATEGY_CREATE',
                    'STRATEGY_UPDATE',
                    'STRATEGY_VIEW',
                    'STRATEGY_DELETE',
                ],
            },
            {
                profileKey: 'AGENCY_MANAGER',
                permissionKeys: [
                    'STRATEGY_CREATE',
                    'STRATEGY_UPDATE',
                    'STRATEGY_VIEW',
                    'STRATEGY_DELETE',
                ],
            },
        ];
        for (const mapping of profilePermissionsMapping) {
            await this.assignPermissionsToProfile(mapping.profileKey, mapping.permissionKeys, profileRepository, permissionRepository, profilePermissionRepository);
        }
    }
    async assignPermissionsToProfile(profileKey, permissionKeys, profileRepository, permissionRepository, profilePermissionRepository) {
        const profile = await profileRepository.findOne({
            where: { key: profileKey },
        });
        if (profile) {
            for (const permissionKey of permissionKeys) {
                const permission = await permissionRepository.findOne({
                    where: { key: permissionKey },
                });
                if (permission) {
                    const existing = await profilePermissionRepository.findOne({
                        where: { profileId: profile.id, permissionId: permission.id },
                    });
                    if (!existing) {
                        const profilePermission = new profile_permission_entity_1.ProfilePermission();
                        profilePermission.profileId = profile.id;
                        profilePermission.permissionId = permission.id;
                        await profilePermissionRepository.save(profilePermission);
                    }
                }
            }
        }
    }
};
exports.ProfilePermissionsStrategySeeder = ProfilePermissionsStrategySeeder;
exports.ProfilePermissionsStrategySeeder = ProfilePermissionsStrategySeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ProfilePermissionsStrategySeeder);
//# sourceMappingURL=profile-permissions-strategy.seeder.js.map