"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const event_entity_1 = require("./entities/event.entity");
let EventsService = class EventsService {
    constructor(eventRepository) {
        this.eventRepository = eventRepository;
    }
    async create(createEventDto) {
        const newData = new event_entity_1.Event();
        newData.description = createEventDto.description;
        const response = await this.eventRepository.save(newData);
        return {
            id: response.id,
            description: response.description,
        };
    }
    async findAll() {
        return await this.eventRepository
            .createQueryBuilder('event')
            .select(['event.id', 'event.description'])
            .where('event.deleted_at IS NULL')
            .getMany();
    }
    async findOne(id) {
        const data = await this.eventRepository.findOneBy({
            id,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!data) {
            throw new common_1.NotFoundException(`Event with ID ${id} not found`);
        }
        return data;
    }
    async update(id, updateEventDto) {
        await this.findOne(id);
        await this.eventRepository.update(id, {
            description: updateEventDto.description,
            updatedAt: new Date(),
        });
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            description: updated.description,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.eventRepository.update(id, { deletedAt: new Date() });
    }
};
exports.EventsService = EventsService;
exports.EventsService = EventsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(event_entity_1.Event)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], EventsService);
//# sourceMappingURL=events.service.js.map