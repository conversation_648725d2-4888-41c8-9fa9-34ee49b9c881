import { OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class ProductSeeder implements OnModuleInit {
    private readonly dataSource;
    constructor(dataSource: DataSource);
    onModuleInit(): Promise<void>;
    private getProduct;
    private saveProduct;
    private seedConsortium;
    private seedContractingCreditCardIncrease;
    private seedContractingSpecialCheckIncrease;
    private seedCommercialCreditCoObligations;
    private seedAutomaticDebit;
    private seedTermDeposit;
    private seedLCA;
    private seedSalaryPortability;
    private seedSavings;
    private seedHomeInsurance;
}
