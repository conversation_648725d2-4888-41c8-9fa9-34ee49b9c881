"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletsController = void 0;
const common_1 = require("@nestjs/common");
const wallets_service_1 = require("./wallets.service");
const create_wallet_dto_1 = require("./dto/create-wallet.dto");
const update_wallet_dto_1 = require("./dto/update-wallet.dto");
const swagger_1 = require("@nestjs/swagger");
const search_wallet_dto_1 = require("./dto/search-wallet.dto");
const wallet_entity_1 = require("./entities/wallet.entity");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
const user_decorator_1 = require("../../common/decorators/user.decorator");
const find_all_users_and_wallet_range_response_dto_1 = require("./dto/find-all-users-and-wallet-range-response.dto");
const wallet_dto_1 = require("./dto/wallet.dto");
const response_account_dto_1 = require("../accounts/dto/response-account.dto");
let WalletsController = class WalletsController {
    constructor(walletsService) {
        this.walletsService = walletsService;
    }
    create(createWalletDto) {
        return this.walletsService.create(createWalletDto);
    }
    findAll() {
        return this.walletsService.findAll();
    }
    findAllToUpdateWallet(id, req) {
        return this.walletsService.findAllToUpdateWallet(+id, req);
    }
    findAllToCreateWallet(user) {
        return this.walletsService.findAllToCreateWallet(user);
    }
    update(id, updateWalletDto) {
        return this.walletsService.update(+id, updateWalletDto);
    }
    remove(id) {
        return this.walletsService.remove(+id);
    }
    findNumber(searchWalletDto) {
        return this.walletsService.findNumberWallet(searchWalletDto);
    }
    findAllWalletsByCooperativeIdPaged(id, page = 1, pageSize = 10) {
        return this.walletsService.findAllByCooperativeIdPaged(+id, page, pageSize);
    }
    findAllWalletsByCooperativeId(id, searchWalletDto) {
        return this.walletsService.findAllByCooperativeId(+id);
    }
    findAllWalletsByAgencyId(id, searchWalletDto) {
        return this.walletsService.findAllByAgencyId(+id);
    }
    findAllWalletsByUserId(id) {
        return this.walletsService.findAllByUserId(+id);
    }
    findAllPaged(user, page = 1, pageSize = 10) {
        return this.walletsService.findAllPaged(user, page, pageSize);
    }
    findAllByUserIdPaged(user, id, page = 1, pageSize = 10) {
        return this.walletsService.findAllByUserIdPaged(user, +id, page, pageSize);
    }
    findAllByAgencyIdPaged(id, page = 1, pageSize = 10) {
        return this.walletsService.findAllByAgencyIdPaged(+id, page, pageSize);
    }
    findAllUsersAndwalletRangesByAgencyId(id) {
        return this.walletsService.findAllUsersAndWalletRangeValuesByAgency(+id);
    }
    findAllByCentralIdPaged(id, page = 1, pageSize = 10) {
        return this.walletsService.findAllByCentralIdPaged(+id);
    }
    findAllByFederationIdPaged(id, page = 1, pageSize = 10) {
        return this.walletsService.findAllByFederationIdPaged(+id);
    }
    findAllPagedAndFiltered(user, page = 1, pageSize = 10, segmentId, walletRangeId) {
        return this.walletsService.findAllPagedAndFiltered(user, page, pageSize, segmentId ? +segmentId : undefined, walletRangeId ? +walletRangeId : undefined);
    }
    findAllByAssociateAndAgency(user, associateId) {
        return this.walletsService.findAllByAssociateAndAgency(associateId, user);
    }
    findOne(id) {
        return this.walletsService.findOne(+id);
    }
    async createBulk(walletDto) {
        if (!walletDto || walletDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.walletsService.createWalletFromBulk(walletDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(body) {
        if (!body.walletsData || body.walletsData.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.walletsService.deleteWalletsFromBulk(body.walletsData);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    findAccountsByAgencyId(id) {
        return this.walletsService.findAllAccountsByAgencyId(id);
    }
};
exports.WalletsController = WalletsController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova Carteira' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Carteira criada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_wallet_dto_1.CreateWalletDto]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Carteiras' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar wallet, associates, agency e usuarios aptos para administrar a carteira de cada segmento',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Listas para a edição de Carteira buscada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Nenhuma Lista para a edição de Carteira encontrada.',
    }),
    (0, common_1.Get)('to-update-wallet/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllToUpdateWallet", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar segments e ultima carteira de cada segmento',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Listas para a Criação de Carteira buscada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Nenhuma Lista para a Criação de Carteira encontrada.',
    }),
    (0, common_1.Get)('to-create-wallet'),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllToCreateWallet", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Carteira' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Carteira atualizada com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Carteira não encontrada.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_wallet_dto_1.UpdateWalletDto]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Carteira' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Carteira removida com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Carteira não encontrada.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Descobre o numero da carteira' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Número disponível para uso!',
        type: update_wallet_dto_1.UpdateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)('search-number'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [search_wallet_dto_1.SearchWalletDto]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findNumber", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Busca todas as carteiras pelo id da cooperativa paginado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Listas de Carteiras buscada com sucesso.',
        type: update_wallet_dto_1.UpdateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Get)('find-by-cooperative-paged/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllWalletsByCooperativeIdPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca todas as carteiras pelo id da cooperativa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Listas de Carteiras buscada com sucesso.',
        type: update_wallet_dto_1.UpdateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Get)('find-by-cooperative/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllWalletsByCooperativeId", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca todas as carteiras pelo id da Agencia' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Listas de Carteiras buscada com sucesso.',
        type: update_wallet_dto_1.UpdateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Get)('find-by-agency/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllWalletsByAgencyId", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Busca todas as carteiras pelo id do responsável' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Listas de Carteiras buscada com sucesso.',
        type: update_wallet_dto_1.UpdateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Get)('find-by-user/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllWalletsByUserId", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Carteiras paginada' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('paged'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)('page', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('pageSize', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Carteiras por id de usuario paginada',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('find-by-user-paged/:id'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Query)('page')),
    __param(3, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllByUserIdPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Carteiras por id de agencia paginada',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: [create_wallet_dto_1.CreateWalletDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('find-by-agency-paged/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllByAgencyIdPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Usuários e faixas de renda por id da agencia',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista encontrada com sucesso.',
        type: find_all_users_and_wallet_range_response_dto_1.FindAllUsersAndWalletRangeResponseDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Nenhuma faixa ou usuário encontrada.',
    }),
    (0, common_1.Get)('find-all-users-and-wallet-range-by-agency/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllUsersAndwalletRangesByAgencyId", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Carteiras por id de central paginada',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: [create_wallet_dto_1.CreateWalletDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('find-by-central-id-paged/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllByCentralIdPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Carteiras por id de federação paginada',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: [create_wallet_dto_1.CreateWalletDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('find-by-federation-id-paged/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('pageSize')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllByFederationIdPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Carteiras paginada e filtrado se o usuario for central ou federação',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('paged-and-filtered'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)('page', common_1.ParseIntPipe)),
    __param(2, (0, common_1.Query)('pageSize', common_1.ParseIntPipe)),
    __param(3, (0, common_1.Query)('segmentId')),
    __param(4, (0, common_1.Query)('walletRangeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, Number, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllPagedAndFiltered", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Carteiras por associado e agencia',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Carteiras encontrada com sucesso.',
        type: create_wallet_dto_1.CreateWalletDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, common_1.Get)('find-associate-and-agency'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)('associateId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAllByAssociateAndAgency", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Carteira pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Carteira encontrada com sucesso.',
        type: wallet_entity_1.Wallet,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Carteira não encontrada.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplas carteiras em massa',
        description: `
      Este endpoint permite criar novas carteiras ou atualizar carteiras já existentes em massa.
      Se uma carteira já existir (com base no número da carteira), seus dados serão atualizados com as informações enviadas.
      O número máximo permitido de carteiras por requisição é 20.000, sendo processadas em lotes de 500.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [wallet_dto_1.WalletDto],
        description: 'Array de objetos contendo os dados das carteiras a serem criadas ou atualizadas.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar carteiras',
                value: [
                    {
                        name: 'Carteira 1',
                        number: '0001',
                        segment_code: '0001',
                        agency_code: '0001',
                        limit: 5000,
                        user_cpf: '12345678900',
                    },
                    {
                        name: 'Carteira 2',
                        number: '0002',
                        segment_code: '0002',
                        agency_code: '0002',
                        limit: 1200,
                        user_cnpj: '12345678000199',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Carteiras criadas ou atualizadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Wallets processed successfully',
                processedWallets: [
                    {
                        id: 1,
                        name: 'Carteira 1',
                        segment_code: '0001',
                        agency_code: '0001',
                        limit: 5000,
                    },
                    {
                        id: 2,
                        name: 'Carteira 2',
                        segment_code: '0002',
                        agency_code: '0002',
                        limit: 1200,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: name, wallet_range_code, segment_code, agency_code',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas carteiras foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedWallets: [
                    {
                        id: 3,
                        name: 'Carteira 3',
                        segment_code: '0003',
                        agency_code: '0003',
                    },
                ],
                errors: [
                    {
                        wallet: {
                            name: 'Carteira Inválida',
                            number: '0005',
                        },
                        status: 'error',
                        message: 'Segment not found for segment_code: 0001',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], WalletsController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir múltiplas carteiras',
        description: `
      Este endpoint permite a exclusão de múltiplas carteiras em uma única requisição.
      Caso a carteira esteja vinculada a contas ou metas, a exclusão será impedida e um erro será retornado.
      A exclusão é feita via *soft delete* e exige a chave composta (*wallet_number* + *agency_code*).
    `,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de carteiras a serem excluídas, informando o número da carteira e o código da agência.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar carteiras',
                value: {
                    walletsData: [
                        { wallet_number: '01.01.01', agency_code: 'AG001' },
                        { wallet_number: '02.02.02', agency_code: 'AG002' },
                    ],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Carteiras excluídas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Wallets deleted successfully',
                processedWallets: [
                    {
                        id: 1,
                        number: '01.01.01',
                        name: 'Carteira XPTO',
                        agency_code: 'AG001',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas carteiras foram excluídas, mas outras apresentaram erro.',
        schema: {
            example: {
                status: 'partial_success',
                message: 'Some wallets had errors',
                processedWallets: [
                    {
                        id: 1,
                        number: '01.01.01',
                        name: 'Carteira XPTO',
                        agency_code: 'AG001',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        wallet_number: '02.02.02',
                        agency_code: 'AG002',
                        status: 'error',
                        message: "Cannot delete wallet '02.02.02' because it is linked to 3 account(s).",
                        linked_accounts: [
                            { id: 10, code: 'AC001' },
                            { id: 11, code: 'AC002' },
                            { id: 12, code: 'AC003' },
                        ],
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WalletsController.prototype, "deleteBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/wallets'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Contas pelo ID da Agencia' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta encontrada com sucesso.',
        type: response_account_dto_1.ResponseAccountDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Conta não encontrada.' }),
    (0, common_1.Get)('accounts/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], WalletsController.prototype, "findAccountsByAgencyId", null);
exports.WalletsController = WalletsController = __decorate([
    (0, permission_decorator_1.Permission)('WALLETS'),
    (0, common_1.Controller)('/api/v1/wallets'),
    __metadata("design:paramtypes", [wallets_service_1.WalletsService])
], WalletsController);
//# sourceMappingURL=wallets.controller.js.map