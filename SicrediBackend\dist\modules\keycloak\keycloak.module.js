"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeycloakModule = void 0;
const common_1 = require("@nestjs/common");
const keycloak_service_1 = require("./keycloak.service");
const typeorm_1 = require("@nestjs/typeorm");
const keycloak_entity_1 = require("./entities/keycloak.entity");
const axios_1 = require("@nestjs/axios");
let KeycloakModule = class KeycloakModule {
};
exports.KeycloakModule = KeycloakModule;
exports.KeycloakModule = KeycloakModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([keycloak_entity_1.Keycloak]), axios_1.HttpModule],
        providers: [keycloak_service_1.KeycloakService],
    })
], KeycloakModule);
//# sourceMappingURL=keycloak.module.js.map