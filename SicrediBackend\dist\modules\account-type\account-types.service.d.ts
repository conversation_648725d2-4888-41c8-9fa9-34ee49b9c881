import { Repository } from 'typeorm';
import { AccountType } from './entities/account-type.entity';
import { CreateAccountTypeDto } from './dto/create-account-type.dto';
import { Accounts } from '../accounts/entities/account.entity';
export declare class AccountTypeService {
    private readonly accountTypeRepository;
    private readonly accountRepository;
    constructor(accountTypeRepository: Repository<AccountType>, accountRepository: Repository<Accounts>);
    createAccountTypeFromBulk(accountTypeDto: CreateAccountTypeDto[]): Promise<any>;
    deleteAccountTypesFromBulk(keys: string[]): Promise<any>;
    findOne(key: string): Promise<CreateAccountTypeDto>;
    findByKeys(keys: string[]): Promise<AccountType[]>;
}
