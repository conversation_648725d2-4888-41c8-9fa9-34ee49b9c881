import { GoalProductWalletModule } from './modules/goal-product-wallet/goal-product-wallet.module';
import { GoalModule } from './modules/goal/goal.module';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './modules/users/users.module';
import { AuthModule } from './auth/auth.module';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './configs/database.module';
import { FederationsModule } from './modules/federations/federations.module';
import { AuditLogsModule } from './modules/audit-logs/audit-logs.module';
import { WinstonModule } from 'nest-winston';
import { winstonConfig } from './configs/winston.config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { LoggerInterceptor } from './common/interceptors/logger.interceptor';
import { CentralsModule } from './modules/centrals/centrals.module';
import { CooperativesModule } from './modules/cooperatives/cooperatives.module';
import { AgenciesModule } from './modules/agencies/agencies.module';
import { SegmentsModule } from './modules/segments/segments.module';
import { PermissionsModule } from './modules/permissions/permissions.module';
import { ProfilesModule } from './modules/profiles/profiles.module';
import { ProfilePermissionsModule } from './modules/profile-permissions/profile-permissions.module';
import { UserAgenciesModule } from './modules/user-agencies/user-agencies.module';
import { WalletsModule } from './modules/wallets/wallets.module';
import { ProductsModule } from './modules/products/products.module';
import { EventsModule } from './modules/events/events.module';
import { AssociatesModule } from './modules/associates/associates.module';
import { AssociateAgencyAccountsModule } from './modules/associate-agency-accounts/associate-agency-accounts.module';
import { AttendanceStatusModule } from './modules/attendance-status/attendance-status.module';
import { AttendancesModule } from './modules/attendances/attendances.module';
import { AttendanceProductsModule } from './modules/attendance-products/attendance-products.module';
import { AttendanceProductsEffectiveModule } from './modules/attendance-products-effective/attendance-products-effective.module';
import { SchedulesModule } from './modules/schedules/schedules.module';
import {
  AuthGuard,
  KeycloakConnectModule,
  ResourceGuard,
  RoleGuard,
} from 'nest-keycloak-connect';
import { KeycloakModule } from './modules/keycloak/keycloak.module';
import { WalletRangeValuesModule } from './modules/wallet-range-values/wallet-range-values.module';
import { StrategyModule } from './modules/strategies/strategies.module';
import { AttendanceHistoryModule } from './modules/attendance-history/attendance-history.module';
import { StrategyProductsModule } from './modules/strategy-products/strategy-products.module';
import { PropensityProductsModule } from './modules/propensity-products/propensity-products.module';
import { UserWalletsModule } from './modules/user-wallets/user-wallets.module';
import { WalletSummaryModule } from './modules/wallet-summary/wallet-summary.module';
import { FiltersModule } from './modules/filters/filters.module';
import { AttendanceSummaryModule } from './modules/attendance-summary/attendance-summary.module';
import { AccountsModule } from './modules/accounts/accounts.module';
import { AccountWalletsModule } from './modules/account-wallets/account-wallets.module';
import { AccountTypeModule } from './modules/account-type/account-types.module';
import { AssociateDetailsModule } from './modules/associate-details/associate-details.module';
import { CardsModule } from './modules/cards/cards.module';
import { NotificationsModule } from './modules/notifications/notifications.module';
import { ScheduleModule } from '@nestjs/schedule';
import { AssociatePhoneModule } from './modules/associate-phone/associate-phone.module';
import { RateLimitMiddleware } from './common/middleware/rate-limit.middleware';
import { GlobalRateLimitMiddleware } from './common/middleware/global-rate-limit.middleware';

@Module({
  imports: [
    GoalProductWalletModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    KeycloakConnectModule.register({
      authServerUrl: process.env.KEYCLOAK_URL,
      realm: process.env.KEYCLOAK_REALM,
      clientId: process.env.KEYCLOAK_CLIENT_ID,
      secret: process.env.KEYCLOAK_CLIENT_SECRET,
      cookieKey: 'token',
    }),
    ScheduleModule.forRoot(),
    WinstonModule.forRoot(winstonConfig),
    AuthModule,
    UsersModule,
    GoalModule,
    DatabaseModule,
    FederationsModule,
    AuditLogsModule,
    CentralsModule,
    CooperativesModule,
    AgenciesModule,
    SegmentsModule,
    PermissionsModule,
    ProfilesModule,
    ProfilePermissionsModule,
    UserAgenciesModule,
    WalletsModule,
    ProductsModule,
    EventsModule,
    AssociatesModule,
    AssociateAgencyAccountsModule,
    AttendanceStatusModule,
    AttendancesModule,
    AttendanceProductsModule,
    AttendanceProductsEffectiveModule,
    SchedulesModule,
    KeycloakModule,
    WalletRangeValuesModule,
    StrategyModule,
    UserWalletsModule,
    AttendanceHistoryModule,
    StrategyProductsModule,
    PropensityProductsModule,
    WalletSummaryModule,
    FiltersModule,
    AttendanceSummaryModule,
    AccountsModule,
    AccountWalletsModule,
    AccountTypeModule,
    AssociateDetailsModule,
    CardsModule,
    NotificationsModule,
    AssociatePhoneModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggerInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: ResourceGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RoleGuard,
    },
  ],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(GlobalRateLimitMiddleware)
      .forRoutes('*')
      .apply(RateLimitMiddleware)
      .forRoutes('api/v1/auth/login');
  }
}
