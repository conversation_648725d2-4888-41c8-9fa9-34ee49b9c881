"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FiltersModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const filters_controller_1 = require("./filters.controller");
const filters_service_1 = require("./filters.service");
const segment_entity_1 = require("../segments/entities/segment.entity");
const agency_entity_1 = require("../agencies/entities/agency.entity");
const user_entity_1 = require("../users/entities/user.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const user_wallets_entity_1 = require("../user-wallets/entities/user-wallets.entity");
const attendance_status_entity_1 = require("../attendance-status/entities/attendance-status.entity");
let FiltersModule = class FiltersModule {
};
exports.FiltersModule = FiltersModule;
exports.FiltersModule = FiltersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                segment_entity_1.Segment,
                agency_entity_1.Agency,
                user_entity_1.User,
                wallet_entity_1.Wallet,
                user_wallets_entity_1.UserWallet,
                attendance_status_entity_1.AttendanceStatus,
            ]),
        ],
        controllers: [filters_controller_1.FiltersController],
        providers: [filters_service_1.FiltersService],
        exports: [filters_service_1.FiltersService],
    })
], FiltersModule);
//# sourceMappingURL=filters.module.js.map