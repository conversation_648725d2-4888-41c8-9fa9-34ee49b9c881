"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryHelper = void 0;
const common_1 = require("@nestjs/common");
class QueryHelper {
    constructor(query) {
        this.query = query;
    }
    addCondition(condition, parameters) {
        const hasWhere = this.query.expressionMap.wheres.length > 0;
        if (hasWhere) {
            this.query.andWhere(condition, parameters);
        }
        else {
            this.query.where(condition, parameters);
        }
        return this;
    }
    whereHierarchy(operator, hierarchy, identifier, allowedHierarchies, alias = 'profile.hierarchy') {
        if (allowedHierarchies && !allowedHierarchies.includes(hierarchy)) {
            throw new common_1.ForbiddenException('Unauthorized: User hierarchy not allowed');
        }
        if (identifier === 'profile_id') {
            this.ensureJoin('profile', `profile.id = ${this.getTableAlias()}.${identifier}`);
        }
        else if (identifier === 'user_id') {
            this.ensureJoin('user', `user.id = ${this.getTableAlias()}.${identifier}`);
            this.ensureJoin('profile', 'profile.id = user.profile_id');
            this.addCondition('user.deleted_at IS NULL');
        }
        return this.addCondition(`${alias} ${operator} :hierarchy`, { hierarchy });
    }
    paginate(page, limit) {
        const offset = (page - 1) * limit;
        this.query.limit(limit).offset(offset);
        return this;
    }
    distinct(distinct = true) {
        this.query.distinct(distinct);
        return this;
    }
    ensureJoin(joinTableAlias, joinCondition, joinType = 'innerJoin') {
        const exists = this.query.expressionMap.joinAttributes.some((join) => join.alias.name === joinTableAlias);
        if (!exists) {
            this.query[joinType](joinTableAlias, joinTableAlias, joinCondition);
        }
        return this;
    }
    getTableAlias() {
        return this.query.expressionMap.mainAlias?.name || 'main';
    }
    select(selection, alias) {
        if (Array.isArray(selection)) {
            selection.forEach((sel) => this.query.addSelect(sel));
        }
        else if (alias) {
            this.query.select(selection, alias);
        }
        else {
            this.query.select(selection);
        }
        return this;
    }
    addSelect(selection, alias) {
        this.query.addSelect(selection, alias);
        return this;
    }
    where(where, parameters) {
        this.query.where(where, parameters);
        return this;
    }
    andWhere(where, parameters) {
        this.query.andWhere(where, parameters);
        return this;
    }
    orderBy(parameters, order = 'ASC') {
        this.query.orderBy(parameters, order);
        return this;
    }
    async getRawMany() {
        return this.query.getRawMany();
    }
    async getCount() {
        return this.query.getCount();
    }
    getQueryBuilder() {
        return this.query;
    }
}
exports.QueryHelper = QueryHelper;
//# sourceMappingURL=queryHelper.js.map