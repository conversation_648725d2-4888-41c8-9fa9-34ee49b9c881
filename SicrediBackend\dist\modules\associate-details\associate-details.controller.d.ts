import { AssociateDetailsService } from './associate-details.service';
import { CreateAssociateDetailsDto } from './dto/create-associate-details.dto';
import { UpdateAssociateDetailsDto } from './dto/update-associate-details.dto';
import { AssociateDetailsDto } from './dto/associate-details.dto';
export declare class AssociateDetailsController {
    private readonly associateDetailsService;
    constructor(associateDetailsService: AssociateDetailsService);
    create(createAssociateDetailsDto: CreateAssociateDetailsDto): Promise<{
        id: number;
        associateId: number;
        isRestrict: boolean;
        lcaValue: number;
        depositValue: number;
        missingInstallments: number;
        hasSubscription: boolean;
        termDepositExpiration: Date;
        lcaExpiration: Date;
        lastRegistrationUpdate: Date;
        channelAccess: Date;
        hasFinancialFlow: boolean;
    }>;
    findOne(id: number): Promise<import("./entities/associate-details.entity").AssociateDetails>;
    findByAssociateId(id: number): Promise<import("./entities/associate-details.entity").AssociateDetails>;
    update(id: number, updateAssociateDetailsDto: UpdateAssociateDetailsDto): Promise<import("./entities/associate-details.entity").AssociateDetails | {
        id: number;
        associateId: number;
        isRestrict: boolean;
        isDebtor: boolean;
        lcaValue: number;
        depositValue: number;
        missingInstallments: number;
        hasSubscription: boolean;
        termDepositExpiration: Date;
        lcaExpiration: Date;
        lastRegistrationUpdate: Date;
        channelAccess: Date;
        hasFinancialFlow: boolean;
    }>;
    remove(id: number): Promise<import("./entities/associate-details.entity").AssociateDetails>;
    createBulk(associateDetailsDto: AssociateDetailsDto[]): Promise<any>;
}
