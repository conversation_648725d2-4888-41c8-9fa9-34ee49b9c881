"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceStatusSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const attendance_status_entity_1 = require("../modules/attendance-status/entities/attendance-status.entity");
let AttendanceStatusSeeder = class AttendanceStatusSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedInProgress();
        await this.seedCompleted();
        await this.SeedScheduled();
        await this.SeedNoContact();
    }
    async getAttendanceStatus(key, attendanceStatusRepository) {
        const existing = await attendanceStatusRepository.findOne({
            where: { key },
        });
        return existing;
    }
    async saveAttendanceStatus(existing, description, key, attendanceStatusRepository) {
        if (!existing) {
            const product = new attendance_status_entity_1.AttendanceStatus();
            product.description = description;
            product.key = key;
            return await attendanceStatusRepository.save(product);
        }
        return false;
    }
    async seedInProgress() {
        const attendanceStatusRepository = this.dataSource.getRepository(attendance_status_entity_1.AttendanceStatus);
        const existingCreate = await this.getAttendanceStatus('IN_PROGRESS', attendanceStatusRepository);
        await this.saveAttendanceStatus(existingCreate, 'Em Andamento', 'IN_PROGRESS', attendanceStatusRepository);
    }
    async seedCompleted() {
        const attendanceStatusRepository = this.dataSource.getRepository(attendance_status_entity_1.AttendanceStatus);
        const existingCreate = await this.getAttendanceStatus('COMPLETED', attendanceStatusRepository);
        await this.saveAttendanceStatus(existingCreate, 'Concluído', 'COMPLETED', attendanceStatusRepository);
    }
    async SeedScheduled() {
        const attendanceStatusRepository = this.dataSource.getRepository(attendance_status_entity_1.AttendanceStatus);
        const existingCreate = await this.getAttendanceStatus('SCHEDULED', attendanceStatusRepository);
        await this.saveAttendanceStatus(existingCreate, 'Agendado', 'SCHEDULED', attendanceStatusRepository);
    }
    async SeedNoContact() {
        const attendanceStatusRepository = this.dataSource.getRepository(attendance_status_entity_1.AttendanceStatus);
        const existingCreate = await this.getAttendanceStatus('NO_CONTACT', attendanceStatusRepository);
        await this.saveAttendanceStatus(existingCreate, 'Sem Contato', 'NO_CONTACT', attendanceStatusRepository);
    }
};
exports.AttendanceStatusSeeder = AttendanceStatusSeeder;
exports.AttendanceStatusSeeder = AttendanceStatusSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AttendanceStatusSeeder);
//# sourceMappingURL=attendance-status.seeder.js.map