"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiFile = ApiFile;
exports.ApiImageFile = ApiImageFile;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const file_mimetype_filter_1 = require("./file-mimetype-filter");
function ApiFile(fieldName = 'file', required = false, localOptions) {
    return (0, common_1.applyDecorators)((0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)(fieldName, localOptions)), (0, swagger_1.ApiConsumes)('multipart/form-data'), (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: required ? [fieldName] : [],
            properties: {
                [fieldName]: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    }));
}
function ApiImageFile(fileName = 'image', required = false) {
    return ApiFile(fileName, required, {
        fileFilter: (0, file_mimetype_filter_1.fileMimetypeFilter)('image'),
    });
}
//# sourceMappingURL=file.decorator.js.map