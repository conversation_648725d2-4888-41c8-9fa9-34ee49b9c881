"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserSeeder = void 0;
const common_1 = require("@nestjs/common");
const user_entity_1 = require("../modules/users/entities/user.entity");
const typeorm_1 = require("typeorm");
const keycloak_service_1 = require("../modules/keycloak/keycloak.service");
const config_1 = require("@nestjs/config");
const cryptography_1 = require("../common/functions/cryptography");
const profile_entity_1 = require("../modules/profiles/entities/profile.entity");
let UserSeeder = class UserSeeder {
    constructor(configService, dataSource, keycloak, cryptography) {
        this.configService = configService;
        this.dataSource = dataSource;
        this.keycloak = keycloak;
        this.cryptography = cryptography;
        this.keycloakClientSecret = this.configService.get('KEYCLOAK_CLIENT_SECRET');
    }
    async executeSeed() {
        await this.seedUsers();
    }
    async seedUsers() {
        const userRepository = this.dataSource.getRepository(user_entity_1.User);
        const profileRepository = this.dataSource.getRepository(profile_entity_1.Profile);
        const email = this.cryptography.encrypt('<EMAIL>');
        const existingUser = await userRepository.findOne({
            where: { email: email },
        });
        if (!existingUser) {
            const profile = await profileRepository.findOneBy({ key: 'ADMIN' });
            const user = new user_entity_1.User();
            user.name = 'Admin User';
            user.email = email;
            user.active = true;
            user.profileId = profile.id;
            await userRepository.save(user);
            this.createKeycloakUser(userRepository, user);
        }
        else {
            this.createKeycloakUser(userRepository, existingUser);
        }
    }
    async createKeycloakUser(userRepository, user) {
        if (this.keycloakClientSecret && user?.idUserKeycloak === null) {
            const { data } = await this.keycloak.adminToken();
            await this.keycloak.createUser({
                userName: '<EMAIL>',
                password: 'admin',
                email: '<EMAIL>',
                firstName: 'admin',
                lastName: 'admin',
                token: data.access_token,
            });
            const userList = await this.keycloak.usersList({ token: data.access_token });
            const userAdmin = userList.find((user) => user.username === '<EMAIL>');
            user.idUserKeycloak = userAdmin.id;
            await userRepository.save(user);
        }
    }
};
exports.UserSeeder = UserSeeder;
exports.UserSeeder = UserSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        typeorm_1.DataSource,
        keycloak_service_1.KeycloakService,
        cryptography_1.Cryptography])
], UserSeeder);
//# sourceMappingURL=user.seeder.js.map