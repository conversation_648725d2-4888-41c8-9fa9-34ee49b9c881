"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const wallet_entity_1 = require("./entities/wallet.entity");
const segments_service_1 = require("../segments/segments.service");
const wallet_range_values_service_1 = require("../wallet-range-values/wallet-range-values.service");
const associates_service_1 = require("../associates/associates.service");
const user_wallets_service_1 = require("../user-wallets/user-wallets.service");
const users_service_1 = require("../users/users.service");
const agencies_service_1 = require("../agencies/agencies.service");
const cryptography_1 = require("../../common/functions/cryptography");
const account_entity_1 = require("../accounts/entities/account.entity");
const account_wallets_entity_1 = require("../account-wallets/entities/account-wallets.entity");
const goal_product_wallet_entity_1 = require("../goal-product-wallet/entities/goal-product-wallet.entity");
const agency_entity_1 = require("../agencies/entities/agency.entity");
const wallet_range_value_entity_1 = require("../wallet-range-values/entities/wallet-range-value.entity");
const user_entity_1 = require("../users/entities/user.entity");
const segment_entity_1 = require("../segments/entities/segment.entity");
let WalletsService = class WalletsService {
    constructor(walletRepository, accountsRepository, accountWalletsRepository, goalProductWalletRepository, agenciesRepository, walletRangeValueRepository, segmentService, segmentRepository, userRepository, walletRangeService, associatesService, userWalletsService, usersService, agenciesService, cryptography) {
        this.walletRepository = walletRepository;
        this.accountsRepository = accountsRepository;
        this.accountWalletsRepository = accountWalletsRepository;
        this.goalProductWalletRepository = goalProductWalletRepository;
        this.agenciesRepository = agenciesRepository;
        this.walletRangeValueRepository = walletRangeValueRepository;
        this.segmentService = segmentService;
        this.segmentRepository = segmentRepository;
        this.userRepository = userRepository;
        this.walletRangeService = walletRangeService;
        this.associatesService = associatesService;
        this.userWalletsService = userWalletsService;
        this.usersService = usersService;
        this.agenciesService = agenciesService;
        this.cryptography = cryptography;
    }
    async create(createWalletDto) {
        const { number, numberOld } = await this.findNumberWallet({
            segmentId: createWalletDto.segmentId,
            agencyId: createWalletDto.agencyId,
            walletRangeId: createWalletDto.walletRangeId,
        });
        const newData = new wallet_entity_1.Wallet();
        newData.name = createWalletDto.name;
        newData.userId = createWalletDto.userId;
        newData.segmentId = createWalletDto.segmentId;
        newData.agencyId = createWalletDto.agencyId;
        newData.walletRangeId = createWalletDto.walletRangeId;
        newData.numberWallet = createWalletDto.numberWallet;
        newData.limit = createWalletDto.limit;
        newData.number = number;
        newData.numberOld = numberOld;
        newData.category = 'NORMAL';
        const response = await this.walletRepository.save(newData);
        const userWalletDTO = {
            userId: newData.userId,
            walletId: newData.id,
        };
        await this.userWalletsService.create(userWalletDTO);
        return {
            id: response.id,
            name: response.name,
            userId: response.userId,
            segmentId: response.segmentId,
            agencyId: response.agencyId,
            walletRangeId: response.walletRangeId,
            number: response.number,
            numberOld: response.numberOld,
            numberWallet: response.numberWallet,
            limit: response.limit,
        };
    }
    async findAll() {
        return await this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .execute();
    }
    async findAllToUpdateWallet(id, req) {
        const userId = req.user.sub;
        await this.usersService.findByKeycloakId(userId);
        const data = await this.walletRepository.findOne({
            where: {
                id,
                deletedAt: (0, typeorm_2.IsNull)(),
            },
            relations: {
                segment: true,
                walletRangeValue: true,
                user: true,
            },
        });
        let associates = await this.associatesService.findAllByWallet(id);
        let agency = await this.agenciesService.findOne(data.agencyId);
        let usersToWallet = await this.usersService.findAllByAgency(data.agencyId);
        let response = {
            wallet: data,
            associates: associates,
            agency: agency,
            usersToWallet: usersToWallet.filter((user) => user.profileKey === 'WALLET_MANAGER'),
        };
        if (!data) {
            throw new common_1.NotFoundException(`Wallet with ID ${id} not found`);
        }
        return response;
    }
    async findAllToCreateWallet(userReq) {
        const segmentList = await this.segmentService.findAll();
        let agenciesToWallet = [];
        const user = await this.usersService.findById(userReq.id);
        const rules = ['ADMIN', 'FEDERATION', 'CENTRAL'];
        if (rules.includes(userReq.profile.key)) {
            agenciesToWallet = await this.agenciesService.findAll(userReq);
        }
        else if (userReq.profile.key === 'COOPERATIVE') {
            agenciesToWallet = await this.agenciesService.findAllByIdCooperative(user.cooperativeId);
        }
        else {
            agenciesToWallet.push(await this.agenciesService.findOne(user.agencyId));
        }
        return {
            segmentList: segmentList,
            agencyList: agenciesToWallet,
        };
    }
    async findOne(id) {
        const data = await this.walletRepository.findOne({
            where: {
                id,
                deletedAt: (0, typeorm_2.IsNull)(),
            },
            relations: {
                segment: true,
                walletRangeValue: true,
                user: true,
            },
        });
        let associates = await this.associatesService.findAllByWallet(id);
        let agency = await this.agenciesService.findOne(data.agencyId);
        let response = {
            wallet: data,
            associates: associates,
            agency: agency,
        };
        if (!data) {
            throw new common_1.NotFoundException(`Wallet with ID ${id} not found`);
        }
        return response;
    }
    async update(id, updateWalletDto) {
        const wallet = await this.walletRepository.findOne({
            where: { id },
        });
        await this.walletRepository.update(id, {
            name: updateWalletDto.name,
            userId: updateWalletDto.userId,
            segmentId: updateWalletDto.segmentId,
            agencyId: updateWalletDto.agencyId,
            walletRangeId: updateWalletDto.walletRangeId,
            number: updateWalletDto.number,
            numberOld: updateWalletDto.numberOld,
            limit: updateWalletDto.limit,
            updatedAt: new Date(),
        });
        if (updateWalletDto.userId) {
            const walletUser = await this.userWalletsService.findOneByUserAndWallet(wallet.userId, wallet.id);
            if (walletUser) {
                await this.userWalletsService.remove(walletUser.id);
            }
            await this.userWalletsService.create({
                walletId: id,
                userId: updateWalletDto.userId,
            });
        }
        const updated = await this.findOne(id);
        return {
            id: updated.wallet.id,
            name: updated.wallet.name,
            userId: updated.wallet.userId,
            segmentId: updated.wallet.segmentId,
            agencyId: updated.wallet.agencyId,
            walletRangeId: updated.wallet.walletRangeId,
            number: updated.wallet.number,
            numberOld: updated.wallet.numberOld,
        };
    }
    async remove(id) {
        const wallet = await this.findOne(id);
        const userWallet = await this.userWalletsService.findOneByUserAndWallet(wallet.wallet.userId, wallet.wallet.id);
        await this.userWalletsService.remove(userWallet.id);
        await this.walletRepository.update(id, { deletedAt: new Date() });
        return 'Deletado com sucesso';
    }
    async findNumberWallet({ segmentId, walletRangeId, agencyId, }) {
        return await this.calculateWalletNumbers(segmentId, walletRangeId, agencyId);
    }
    async calculateWalletNumbers(segmentId, walletRangeId, agencyId) {
        const segmente = await this.segmentService.findOne(segmentId);
        const walletRange = await this.walletRangeService.findOne(walletRangeId);
        const data = await this.walletRepository
            .createQueryBuilder('wallet')
            .select(['wallet.number_wallet AS "numberWallet"'])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.segment_id = :segmentId', { segmentId })
            .andWhere('wallet.wallet_range_id = :walletRangeId', { walletRangeId })
            .andWhere('wallet.agency_id = :agencyId', { agencyId })
            .orderBy('CAST(wallet.number_wallet AS INTEGER)', 'DESC')
            .limit(1)
            .execute();
        if (data.length === 0) {
            return {
                number: `${segmente.number}.${walletRange.number}.01`,
                numberOld: `${segmente.numberOld}${walletRange.numberOld}1`,
                segmenteNumber: segmente.number,
                walletRangeNumber: walletRange.number,
                numberWallet: '01',
                segmenteNumberOld: segmente.numberOld,
                walletRangeNumberOld: walletRange.numberOld,
                numberOldWallet: '1',
            };
        }
        const numberWalletOld = String(Number(data[0].numberWallet) + 1);
        let numberWallet = String(Number(data[0].numberWallet) + 1);
        if (numberWallet.length < 2) {
            numberWallet = `0${numberWallet}`;
        }
        return {
            number: `${segmente.number}.${walletRange.number}.${numberWallet}`,
            numberOld: `${segmente.numberOld}${walletRange.numberOld}${numberWalletOld}`,
            segmenteNumber: segmente.number,
            walletRangeNumber: walletRange.number,
            numberWallet: numberWallet,
            segmenteNumberOld: segmente.numberOld,
            walletRangeNumberOld: walletRange.numberOld,
            numberOldWallet: numberWalletOld,
        };
    }
    async findAllByCooperativeId(id) {
        const agencies = await this.agenciesService.findAllByIdCooperative(id);
        if (!agencies.length) {
            return [];
        }
        const wallets = [];
        for (const agency of agencies) {
            const walletsByAgency = await this.walletRepository
                .createQueryBuilder('wallet')
                .select([
                'wallet.id as id',
                'wallet.name as name',
                'wallet.user_id as userId',
                'wallet.segment_id as segmentId',
                'wallet.agency_id as agencyId',
                'wallet.wallet_range_id as walletRangeId',
                'wallet.number as number',
                'wallet.number_old as numberOld',
                'wallet.limit as limit',
            ])
                .where('wallet.deleted_at IS NULL')
                .andWhere('wallet.agency_id = :agencyId', { agencyId: agency.id })
                .execute();
            wallets.push(...walletsByAgency);
        }
        return wallets;
    }
    async findAllByAgencyId(id) {
        return await this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.agency_id = :id', { id })
            .execute();
    }
    async findAllByUserId(id) {
        return await this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.user_id = :id', { id })
            .execute();
    }
    async findAllPaged(userReq, page, pageSize, segmentId, walletRangeId) {
        const queryBuilder = this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL');
        if (segmentId) {
            queryBuilder.andWhere('wallet.segment_id = :segmentId', { segmentId });
        }
        if (walletRangeId) {
            queryBuilder.andWhere('wallet.wallet_range_id = :walletRangeId', {
                walletRangeId,
            });
        }
        if (['ASSISTANT', 'WALLET_MANAGER'].includes(userReq.profile.key)) {
            queryBuilder.andWhere(`coalesce(wallet.category, '') != 'OTHERS'`);
        }
        const totalItems = await queryBuilder.getCount();
        const offset = (page - 1) * pageSize;
        queryBuilder.take(pageSize).skip(offset);
        const data = await queryBuilder.execute();
        const totalPages = Math.ceil(totalItems / pageSize);
        const newData = await Promise.all(data.map(async (item) => ({
            id: item.id,
            name: item.name,
            userId: item.userId,
            segmentId: item.segmentid,
            walletRangeId: item.walletrangeid,
            number: item.number,
            numberOld: item.numberOld,
            limit: item.limit,
            numberAssociates: await this.associatesService.countAssociatesByWallet(item.id),
        })));
        return {
            items: newData,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByCooperativeIdPaged(cooperativeId, page = 1, limit = 20) {
        const agencies = await this.agenciesService.findAllByIdCooperative(cooperativeId);
        if (!agencies.length) {
            return {
                totalItems: 0,
                totalPages: 0,
                currentPage: 1,
                items: [],
            };
        }
        const agencyIds = agencies.map((agency) => agency.id);
        const wallets = this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.agency_id IN (:...agencyIds)', { agencyIds });
        const totalItems = await wallets.getCount();
        const offset = (page - 1) * limit;
        wallets.take(limit).skip(offset);
        const data = await wallets.execute();
        const totalPages = Math.ceil(totalItems / limit);
        const newData = await Promise.all(data.map(async (item) => ({
            id: item.id,
            name: item.name,
            userId: item.userId,
            segmentId: item.segmentid,
            walletRangeId: item.walletrangeid,
            number: item.number,
            numberOld: item.numberOld,
            limit: item.limit,
            numberAssociates: await this.associatesService.countAssociatesByWallet(item.id),
        })));
        return {
            items: newData,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByUserIdPaged(userReq, id, page, pageSize) {
        const wallets = this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL');
        if (userReq.profile.key === 'ASSISTANT') {
            wallets.andWhere(`wallet.agency_id = :agencyId`, {
                agencyId: userReq.agencyId,
            });
        }
        else {
            wallets.andWhere('wallet.user_id = :id', { id });
        }
        if (['ASSISTANT', 'WALLET_MANAGER'].includes(userReq.profile.key)) {
            wallets.andWhere(`coalesce(wallet.category, '') != 'OTHERS'`);
        }
        const totalItems = await wallets.getCount();
        const offset = (page - 1) * pageSize;
        wallets.take(pageSize).skip(offset);
        const data = await wallets.execute();
        const totalPages = Math.ceil(totalItems / pageSize);
        const newData = await Promise.all(data.map(async (item) => ({
            id: item.id,
            name: item.name,
            userId: item.userId,
            segmentId: item.segmentid,
            walletRangeId: item.walletrangeid,
            number: item.number,
            numberOld: item.numberOld,
            limit: item.limit,
            numberAssociates: await this.associatesService.countAssociatesByWallet(item.id),
        })));
        return {
            items: newData,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByAgencyIdPaged(id, page, pageSize) {
        const wallets = this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.agency_id = :id', { id });
        const totalItems = await wallets.getCount();
        const offset = (page - 1) * pageSize;
        wallets.take(pageSize).skip(offset);
        const data = await wallets.execute();
        const totalPages = Math.ceil(totalItems / pageSize);
        const newData = await Promise.all(data.map(async (item) => ({
            id: item.id,
            name: item.name,
            userId: item.userId,
            segmentId: item.segmentid,
            walletRangeId: item.walletrangeid,
            number: item.number,
            numberOld: item.numberOld,
            limit: item.limit,
            numberAssociates: await this.associatesService.countAssociatesByWallet(item.id),
        })));
        return {
            items: newData,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllUsersAndWalletRangeValuesByAgency(id) {
        const users = await this.usersService.findAllByAgency(id);
        const walletRangeValues = await this.walletRangeService.findByAgency(id);
        return {
            users: users.filter((user) => user.profileKey === 'WALLET_MANAGER'),
            walletRangeValues: walletRangeValues,
        };
    }
    async createDefaultWalletsByAgency(idAgency) {
        const defaultWallets = [
            {
                name: 'INATIVOS',
                number: '01.04.07',
                numberOld: '147',
                category: 'OTHERS',
            },
            {
                name: 'MENOR ASSOCIADO',
                number: '01.07.08',
                numberOld: '178',
                category: 'OTHERS',
            },
            {
                name: 'CO-TITULAR',
                number: '01.08.08',
                numberOld: '188',
                category: 'OTHERS',
            },
            {
                name: 'AVALISTA',
                number: '05.03.01',
                numberOld: '531',
                category: 'OTHERS',
            },
            {
                name: 'POUPANÇA',
                number: '05.03.02',
                numberOld: '532',
                category: 'OTHERS',
            },
            {
                name: 'SALÁRIO',
                number: '05.03.03',
                numberOld: '533',
                category: 'OTHERS',
            },
            {
                name: 'ENCERRADA',
                number: '05.03.04',
                numberOld: '534',
                category: 'OTHERS',
            },
            {
                name: 'PREJUÍZO',
                number: '05.03.05',
                numberOld: '535',
                category: 'OTHERS',
            },
        ];
        const wallets = defaultWallets.map((walletData) => {
            const newWallet = new wallet_entity_1.Wallet();
            newWallet.name = walletData.name;
            newWallet.number = walletData.number;
            newWallet.numberOld = walletData.numberOld;
            newWallet.category = walletData.category;
            newWallet.agencyId = idAgency;
            return newWallet;
        });
        await this.walletRepository.save(wallets);
    }
    async findAllByCentralIdPaged(centralId, page = 1, limit = 20, segmentId, walletRangeId) {
        const agencies = await this.agenciesService.findAllByCentral(centralId);
        const agencyIds = agencies.map((agency) => agency.id);
        if (!agencyIds.length) {
            return {
                totalItems: 0,
                totalPages: 0,
                currentPage: 1,
                items: [],
            };
        }
        const wallets = this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.agency_id IN (:...agencyIds)', { agencyIds });
        if (segmentId) {
            wallets.andWhere('wallet.segment_id = :segmentId', { segmentId });
        }
        if (walletRangeId) {
            wallets.andWhere('wallet.wallet_range_id = :walletRangeId', {
                walletRangeId,
            });
        }
        const totalItems = await wallets.getCount();
        const offset = (page - 1) * limit;
        wallets.take(limit).skip(offset);
        const data = await wallets.execute();
        const totalPages = Math.ceil(totalItems / limit);
        const newData = await Promise.all(data.map(async (item) => ({
            id: item.id,
            name: item.name,
            userId: item.userId,
            segmentId: item.segmentId,
            walletRangeId: item.walletRangeId,
            number: item.number,
            numberOld: item.numberOld,
            limit: item.limit,
            numberAssociates: await this.associatesService.countAssociatesByWallet(item.id),
        })));
        return {
            items: newData,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllByFederationIdPaged(federationId, page = 1, limit = 20, segmentId, walletRangeId) {
        const agencies = await this.agenciesService.findAllByFederation(federationId);
        if (!agencies.length) {
            return {
                totalItems: 0,
                totalPages: 0,
                currentPage: 1,
                items: [],
            };
        }
        const agencyIds = agencies.map((agency) => agency.id);
        const wallets = this.walletRepository
            .createQueryBuilder('wallet')
            .select([
            'wallet.id as id',
            'wallet.name as name',
            'wallet.user_id as userId',
            'wallet.segment_id as segmentId',
            'wallet.agency_id as agencyId',
            'wallet.wallet_range_id as walletRangeId',
            'wallet.number as number',
            'wallet.number_old as numberOld',
            'wallet.limit as limit',
        ])
            .where('wallet.deleted_at IS NULL')
            .andWhere('wallet.agency_id IN (:...agencyIds)', { agencyIds });
        if (segmentId) {
            wallets.andWhere('wallet.segment_id = :segmentId', { segmentId });
        }
        if (walletRangeId) {
            wallets.andWhere('wallet.wallet_range_id = :walletRangeId', {
                walletRangeId,
            });
        }
        const totalItems = await wallets.getCount();
        const offset = (page - 1) * limit;
        wallets.take(limit).skip(offset);
        const data = await wallets.execute();
        const totalPages = Math.ceil(totalItems / limit);
        const newData = await Promise.all(data.map(async (item) => ({
            id: item.id,
            name: item.name,
            userId: item.userId,
            segmentId: item.segmentid,
            walletRangeId: item.walletrangeid,
            number: item.number,
            numberOld: item.numberOld,
            limit: item.limit,
            numberAssociates: await this.associatesService.countAssociatesByWallet(item.id),
        })));
        return {
            items: newData,
            totalItems,
            totalPages,
            currentPage: page,
        };
    }
    async findAllPagedAndFiltered(userReq, page = 1, limit = 20, segmentId, walletRangeId) {
        let wallets = {
            totalItems: 0,
            totalPages: 0,
            currentPage: page,
            items: [],
        };
        const user = await this.usersService.findById(userReq.id);
        if (userReq.profile.key === 'CENTRAL') {
            wallets = await this.findAllByCentralIdPaged(user.centralId, page, limit, segmentId, walletRangeId);
        }
        else if (userReq.profile.key === 'FEDERATION') {
            wallets = await this.findAllByFederationIdPaged(user.federationId, page, limit, segmentId, walletRangeId);
        }
        else {
            wallets = await this.findAllPaged(userReq, page, limit, segmentId, walletRangeId);
        }
        return wallets;
    }
    async createWalletFromBulk(walletsData) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!Array.isArray(walletsData) || walletsData.length === 0) {
            return {
                status: 'error',
                message: 'O payload deve ser um array de WalletDto.',
                totalReceived: walletsData?.length || 0,
                totalSaved: 0,
                errors: ['Payload inválido.'],
            };
        }
        if (walletsData.length > MAX_BATCH_SIZE) {
            return {
                status: 'error',
                message: `Too many wallets in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: walletsData.length,
                totalSaved: 0,
                errors: [`Excedeu o limite de ${MAX_BATCH_SIZE} registros.`],
            };
        }
        walletsData.forEach(dto => {
            if (dto.user_cpf) {
                dto.user_cpf = dto.user_cpf.replace(/\D/g, '');
            }
        });
        const segmentCodes = [...new Set(walletsData.map(w => w.segment_code).filter(Boolean))];
        const agencyCodes = [...new Set(walletsData.map(w => w.agency_code).filter(Boolean))];
        const cpfs = [...new Set(walletsData.map(w => w.user_cpf).filter(Boolean))];
        const cnpjs = [...new Set(walletsData.map(w => w.user_cnpj).filter(Boolean))];
        const encryptedCpfMap = new Map();
        const encryptedCnpjMap = new Map();
        cpfs.forEach(cpf => encryptedCpfMap.set(cpf, this.cryptography.encrypt(cpf)));
        cnpjs.forEach(cnpj => encryptedCnpjMap.set(cnpj, this.cryptography.encrypt(cnpj)));
        const [segments, agencies, usersCPF, usersCNPJ] = await Promise.all([
            this.segmentRepository.find({ where: { code: (0, typeorm_2.In)(segmentCodes) } }),
            this.agenciesRepository.find({ where: { agencyCode: (0, typeorm_2.In)(agencyCodes) } }),
            this.userRepository.find({ where: { cpf: (0, typeorm_2.In)([...encryptedCpfMap.values()]) } }),
            this.userRepository.find({ where: { cnpj: (0, typeorm_2.In)([...encryptedCnpjMap.values()]) } }),
        ]);
        const segmentMap = new Map(segments.map(s => [s.code, s]));
        const agencyMap = new Map(agencies.map(a => [a.agencyCode, a]));
        const userCPFMap = new Map();
        usersCPF.forEach(user => {
            const decrypted = this.cryptography.decrypt(user.cpf);
            userCPFMap.set(decrypted, user);
        });
        const userCNPJMap = new Map();
        usersCNPJ.forEach(user => {
            const decrypted = this.cryptography.decrypt(user.cnpj);
            userCNPJMap.set(decrypted, user);
        });
        const orConditions = [];
        for (const dto of walletsData) {
            const ag = agencyMap.get(dto.agency_code);
            if (!ag)
                continue;
            if (dto.number)
                orConditions.push({ agencyId: ag.id, number: dto.number });
            if (dto.numberOld)
                orConditions.push({ agencyId: ag.id, numberOld: dto.numberOld });
        }
        const existingWallets = orConditions.length > 0
            ? await this.walletRepository.find({ where: orConditions })
            : [];
        const walletMap = new Map();
        for (const w of existingWallets) {
            if (w.number)
                walletMap.set(`${w.agencyId}#${w.number}`, w);
            if (w.numberOld)
                walletMap.set(`${w.agencyId}#${w.numberOld}`, w);
        }
        const batches = [];
        for (let i = 0; i < walletsData.length; i += BATCH_SIZE) {
            batches.push(walletsData.slice(i, i + BATCH_SIZE));
        }
        const results = await Promise.allSettled(batches.map((batch, idx) => this.processWalletBatch(batch, idx * BATCH_SIZE, segmentMap, agencyMap, userCPFMap, userCNPJMap, walletMap)));
        const processedWallets = [];
        const errors = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                processedWallets.push(...result.value.saved);
                errors.push(...result.value.errors);
            }
            else {
                errors.push({
                    batch: index,
                    error: result.reason?.message || 'Erro inesperado no processamento do lote.',
                });
            }
        });
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Algumas carteiras apresentaram erros' : 'Carteiras processadas com sucesso',
            totalReceived: walletsData.length,
            totalSaved: processedWallets.length,
            processedWallets,
            errors,
        };
    }
    async processWalletBatch(batch, offset, segmentMap, agencyMap, userCPFMap, userCNPJMap, walletMap) {
        const toSave = [];
        const errors = [];
        for (const [i, dto] of batch.entries()) {
            const index = offset + i;
            const messages = [];
            if (!dto.name?.trim())
                messages.push('name obrigatório');
            if (!dto.segment_code)
                messages.push('segment_code obrigatório');
            if (!dto.agency_code)
                messages.push('agency_code obrigatório');
            if (dto.limit === null || dto.limit === undefined)
                messages.push('limit obrigatório');
            if (!dto.number && dto.number !== '0')
                messages.push('number obrigatório');
            if (!dto.numberOld && dto.numberOld !== '0')
                messages.push('numberOld obrigatório');
            if (!dto.category)
                messages.push('category obrigatório');
            const segment = dto.segment_code ? segmentMap.get(dto.segment_code) : null;
            if (!segment)
                messages.push(`Segmento não encontrado: ${dto.segment_code}`);
            const agency = dto.agency_code ? agencyMap.get(dto.agency_code) : null;
            if (!agency)
                messages.push(`Agência não encontrada: ${dto.agency_code}`);
            const user = dto.user_cpf
                ? userCPFMap.get(dto.user_cpf)
                : dto.user_cnpj
                    ? userCNPJMap.get(dto.user_cnpj)
                    : null;
            if ((dto.user_cpf || dto.user_cnpj) && !user) {
                messages.push(`Usuário não encontrado para CPF/CNPJ`);
            }
            if (messages.length > 0) {
                errors.push({ index, data: dto, messages });
                continue;
            }
            const agencyId = agency.id;
            let existing = dto.number ? walletMap.get(`${agencyId}#${dto.number}`) : null;
            if (!existing && dto.numberOld) {
                existing = walletMap.get(`${agencyId}#${dto.numberOld}`);
            }
            const wallet = existing || this.walletRepository.create();
            wallet.name = dto.name;
            wallet.limit = dto.limit;
            wallet.number = dto.number;
            wallet.numberOld = dto.numberOld;
            wallet.category = dto.category;
            wallet.segmentId = segment.id;
            wallet.agencyId = agencyId;
            wallet.userId = user?.id || null;
            try {
                const rangeName = await this.getWalletRangeValue(dto.number, dto.numberOld);
                if (rangeName) {
                    const range = await this.walletRangeValueRepository.findOne({ where: { name: rangeName } });
                    if (!range) {
                        errors.push({ index, data: dto, messages: [`Faixa de Rendimento '${rangeName}' não encontrada.`] });
                        continue;
                    }
                    wallet.walletRangeId = range.id;
                }
                else {
                    wallet.walletRangeId = null;
                }
                toSave.push(wallet);
            }
            catch (err) {
                errors.push({ index, data: dto, messages: [err.message || 'Erro ao processar faixa de rendimento.'] });
            }
        }
        let saved = [];
        if (toSave.length > 0) {
            saved = await this.walletRepository.save(toSave);
        }
        return { saved, errors };
    }
    async deleteWalletsFromBulk(walletsData) {
        const MAX_BATCH_SIZE = 20;
        if (!walletsData || walletsData.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (walletsData.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many wallets in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: walletsData.length,
            });
        }
        const processedWallets = [];
        const errors = [];
        for (const { wallet_number, agency_code } of walletsData) {
            try {
                const agency = await this.agenciesRepository.findOne({ where: { agencyCode: agency_code } });
                if (!agency) {
                    throw new common_1.NotFoundException(`Agency with code '${agency_code}' not found.`);
                }
                const wallet = await this.walletRepository.findOne({
                    where: { number: wallet_number, agencyId: agency.id },
                });
                if (!wallet) {
                    throw new common_1.NotFoundException(`Wallet with number '${wallet_number}' not found in agency '${agency_code}'.`);
                }
                const linkedAccounts = await this.accountWalletsRepository.find({
                    where: { walletId: wallet.id },
                    select: ['accountId'],
                });
                let accountDetails = [];
                if (linkedAccounts.length > 0) {
                    const accountIds = linkedAccounts.map(acc => acc.accountId);
                    accountDetails = await this.accountsRepository.find({
                        where: { id: (0, typeorm_2.In)(accountIds) },
                        select: ['id', 'code'],
                    });
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete wallet '${wallet_number}' because it is linked to ${linkedAccounts.length} account(s).`,
                        linked_accounts: accountDetails,
                    });
                }
                const linkedGoals = await this.goalProductWalletRepository.find({
                    where: { walletId: wallet.id },
                    select: ['goalId'],
                });
                if (linkedGoals.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete wallet '${wallet_number}' because it is linked to ${linkedGoals.length} goal(s).`,
                        linked_goals: linkedGoals,
                    });
                }
                wallet.deletedAt = new Date();
                await this.walletRepository.save(wallet);
                processedWallets.push({
                    id: wallet.id,
                    number: wallet.number,
                    name: wallet.name,
                    agency_code: agency_code,
                    deleted_at: wallet.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    wallet_number,
                    agency_code,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some wallets had errors' : 'Wallets deleted successfully',
            processedWallets,
            errors,
        };
    }
    async findAllByAssociateAndAgency(associateId, user) {
        const agencyId = user.agencyId;
        const wallets = this.walletRepository
            .createQueryBuilder('wallet')
            .select(['wallet.id as id', 'wallet.name as name'])
            .innerJoin('account_wallets', 'account_wallets', 'account_wallets.wallet_id = wallet.id')
            .innerJoin('accounts', 'accounts', 'accounts.id = account_wallets.account_id')
            .where('wallet.deleted_at IS NULL')
            .andWhere('accounts.associate_id = :associateId', { associateId })
            .andWhere('accounts.agency_id = :agencyId', { agencyId });
        if (user.profile.key === 'WALLET_MANAGER') {
            wallets.andWhere('wallet.user_id = :userId', { userId: user.id });
        }
        const teste = await wallets.execute();
        return teste;
    }
    async findAllAccountsByAgencyId(agencyId) {
        const accounts = this.accountsRepository
            .createQueryBuilder('accounts')
            .select([
            'accounts.id as id',
            'accounts.associate_id as associateId',
            'accounts.agency_id as agencyId',
            'accounts.code as code',
        ])
            .where('accounts.agencyId = :agencyId', { agencyId });
        const response = await accounts.execute();
        return response;
    }
    async getWalletRangeValue(number, numberOld) {
        const mapping = {
            '01': 'Menor ou igual à R$ 2.000,00',
            '02': 'R$ 2.000,01 a R$ 3.999,99',
            '03': 'R$ 4.000,00 a R$ 7.999,99',
            '04': 'Maior ou igual à R$ 8.000,00',
        };
        const extractGroup = (num) => {
            if (!num)
                return null;
            if (num.includes('.')) {
                const parts = num.split('.');
                return parts.length === 3 ? parts[1] : null;
            }
            if (num.length === 3) {
                return `0${num[1]}`;
            }
            return null;
        };
        const groupNumber = extractGroup(number) || extractGroup(numberOld);
        return groupNumber ? mapping[groupNumber] || null : null;
    }
    async findByNumber(walletNumber, agencyCode) {
        const agency = await this.agenciesRepository.findOne({ where: { agencyCode } });
        if (!agency) {
            throw new common_1.NotFoundException(`Agency not found for agency_code: ${agencyCode}`);
        }
        const wallet = await this.walletRepository.findOne({
            where: {
                number: walletNumber,
                agencyId: agency.id,
            },
        });
        if (!wallet) {
            throw new common_1.NotFoundException(`Wallet not found for wallet_number: ${walletNumber} and agency_code: ${agencyCode}`);
        }
        return wallet;
    }
};
exports.WalletsService = WalletsService;
exports.WalletsService = WalletsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.Accounts)),
    __param(2, (0, typeorm_1.InjectRepository)(account_wallets_entity_1.AccountWallets)),
    __param(3, (0, typeorm_1.InjectRepository)(goal_product_wallet_entity_1.GoalProductWallet)),
    __param(4, (0, typeorm_1.InjectRepository)(agency_entity_1.Agency)),
    __param(5, (0, typeorm_1.InjectRepository)(wallet_range_value_entity_1.WalletRangeValue)),
    __param(7, (0, typeorm_1.InjectRepository)(segment_entity_1.Segment)),
    __param(8, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(11, (0, common_1.Inject)((0, common_1.forwardRef)(() => user_wallets_service_1.UserWalletsService))),
    __param(12, (0, common_1.Inject)((0, common_1.forwardRef)(() => users_service_1.UsersService))),
    __param(13, (0, common_1.Inject)((0, common_1.forwardRef)(() => agencies_service_1.AgenciesService))),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        segments_service_1.SegmentsService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        wallet_range_values_service_1.WalletRangeValuesService,
        associates_service_1.AssociatesService,
        user_wallets_service_1.UserWalletsService,
        users_service_1.UsersService,
        agencies_service_1.AgenciesService,
        cryptography_1.Cryptography])
], WalletsService);
//# sourceMappingURL=wallets.service.js.map