"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeycloakSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const keycloak_entity_1 = require("../modules/keycloak/entities/keycloak.entity");
const keycloak_service_1 = require("../modules/keycloak/keycloak.service");
let KeycloakSeeder = class KeycloakSeeder {
    constructor(dataSource, keycloak) {
        this.dataSource = dataSource;
        this.keycloak = keycloak;
    }
    async executeSeed() {
        await this.seedKeycloak();
    }
    async seedKeycloak() {
        const keycloakRepository = this.dataSource.getRepository(keycloak_entity_1.Keycloak);
        const existingKeycloak = await keycloakRepository.findOne({
            where: { config: 'realm' },
        });
        if (!existingKeycloak) {
            const { data } = await this.keycloak.adminToken();
            await this.keycloak.adminCreateRealm({
                token: data.access_token,
            });
            const keycloak = new keycloak_entity_1.Keycloak();
            keycloak.config = 'realm';
            await keycloakRepository.save(keycloak);
        }
    }
};
exports.KeycloakSeeder = KeycloakSeeder;
exports.KeycloakSeeder = KeycloakSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        keycloak_service_1.KeycloakService])
], KeycloakSeeder);
//# sourceMappingURL=keycloak.seeder.js.map