import { CreateAssociateAgencyAccountDto } from './dto/create-associate-agency-account.dto';
import { UpdateAssociateAgencyAccountDto } from './dto/update-associate-agency-account.dto';
import { Repository } from 'typeorm';
import { AssociateAgencyAccount } from './entities/associate-agency-account.entity';
import { Cryptography } from 'src/common/functions/cryptography';
export declare class AssociateAgencyAccountsService {
    private readonly associateAgencyAccountRepository;
    private readonly cryptography;
    constructor(associateAgencyAccountRepository: Repository<AssociateAgencyAccount>, cryptography: Cryptography);
    create(createAssociateAgencyAccountDto: CreateAssociateAgencyAccountDto): Promise<CreateAssociateAgencyAccountDto>;
    findAll(): Promise<CreateAssociateAgencyAccountDto[]>;
    findOne(id: number): Promise<CreateAssociateAgencyAccountDto>;
    update(id: number, updateData: UpdateAssociateAgencyAccountDto): Promise<UpdateAssociateAgencyAccountDto>;
    remove(id: number): Promise<void>;
}
