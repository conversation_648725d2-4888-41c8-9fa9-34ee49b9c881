import { FiltersService } from './filters.service';
export declare class FiltersController {
    private readonly filtersService;
    constructor(filtersService: FiltersService);
    getSegments(): Promise<import("../segments/entities/segment.entity").Segment[]>;
    getAgencies(cooperativeId?: number, userProfile?: string): Promise<import("../agencies/entities/agency.entity").Agency[]>;
    getUsers(agencyId: number, profile: string): Promise<import("../users/entities/user.entity").User[]>;
    getWallets(user: any, walletManagerId?: number, assistantId?: number): Promise<import("../wallets/entities/wallet.entity").Wallet[]>;
    getStatus(): Promise<import("../attendance-status/entities/attendance-status.entity").AttendanceStatus[]>;
}
