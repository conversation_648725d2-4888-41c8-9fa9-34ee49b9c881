"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountWalletsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const account_wallets_entity_1 = require("./entities/account-wallets.entity");
const accounts_service_1 = require("../accounts/accounts.service");
const wallets_service_1 = require("../wallets/wallets.service");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const agencies_service_1 = require("../agencies/agencies.service");
let AccountWalletsService = class AccountWalletsService {
    constructor(accountWalletsRepository, accountsService, agenciesService, walletService, walletsRepository) {
        this.accountWalletsRepository = accountWalletsRepository;
        this.accountsService = accountsService;
        this.agenciesService = agenciesService;
        this.walletService = walletService;
        this.walletsRepository = walletsRepository;
    }
    async createAccountWalletsFromBulk(accountWalletDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!accountWalletDto || accountWalletDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (accountWalletDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many account-wallet relations in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: accountWalletDto.length,
            });
        }
        const allMissingFields = [];
        accountWalletDto.forEach((item, index) => {
            if (!item.account_code)
                allMissingFields.push(`account_code (index ${index})`);
            if (!item.wallet_number && !item.wallet_number_old)
                allMissingFields.push(`wallet_number or wallet_number_old (index ${index})`);
            if (!item.agency_code)
                allMissingFields.push(`agency_code (index ${index})`);
        });
        if (allMissingFields.length > 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Missing required fields: ${allMissingFields.join(', ')}`,
            });
        }
        const batches = [];
        for (let i = 0; i < accountWalletDto.length; i += BATCH_SIZE) {
            batches.push(accountWalletDto.slice(i, i + BATCH_SIZE));
        }
        const results = await Promise.allSettled(batches.map((batch, index) => this.processAccountWalletBatch(batch, index * BATCH_SIZE)));
        const processedAccountWallets = [];
        const errors = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                processedAccountWallets.push(...result.value.processed);
                errors.push(...result.value.errors);
            }
            else {
                errors.push({
                    batch: index,
                    status: 'error',
                    message: result.reason?.message || 'Erro inesperado ao processar lote',
                });
            }
        });
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0
                ? 'Algumas relações de conta e carteira apresentaram erros'
                : 'Relações processadas com sucesso',
            processedAccountWallets,
            errors,
        };
    }
    async processAccountWalletBatch(batch, offset) {
        const processed = [];
        const errors = [];
        const accountCodes = [...new Set(batch.map(item => item.account_code))];
        const agencyCodes = [...new Set(batch.map(item => item.agency_code))];
        const [accounts, agencies] = await Promise.all([
            this.accountsService.findByCodes(accountCodes),
            this.agenciesService.findByCodes(agencyCodes),
        ]);
        const accountMap = new Map(accounts.map(acc => [acc.code, acc]));
        const agencyMap = new Map(agencies.map(ag => [ag.agencyCode, ag]));
        const newAccountWallets = [];
        const updatedAccountWallets = [];
        const existingAccountWallets = await this.accountWalletsRepository.find({
            where: { accountId: (0, typeorm_2.In)(accounts.map(a => a.id)) },
        });
        const existingAccountWalletMap = new Map(existingAccountWallets.map(aw => [aw.accountId, aw]));
        for (const [i, item] of batch.entries()) {
            const index = offset + i;
            const messages = [];
            const account = accountMap.get(item.account_code);
            const agency = agencyMap.get(item.agency_code);
            let wallet = null;
            if (agency) {
                wallet = await this.walletService.findByNumber(item.wallet_number, item.agency_code);
                if (!wallet && item.wallet_number_old) {
                    wallet = await this.walletService.findByNumber(item.wallet_number_old, item.agency_code);
                }
            }
            if (!account || !wallet || !agency) {
                messages.push(!account ? `Conta não encontrada: ${item.account_code}` : '', !wallet ? `Carteira não encontrada: ${item.wallet_number || item.wallet_number_old}` : '', !agency ? `Agência não encontrada: ${item.agency_code}` : '');
                errors.push({
                    index,
                    data: item,
                    messages: messages.filter(Boolean),
                });
                continue;
            }
            const existing = existingAccountWalletMap.get(account.id);
            if (existing) {
                existing.walletId = wallet.id;
                updatedAccountWallets.push(existing);
            }
            else {
                const newEntity = this.accountWalletsRepository.create({
                    accountId: account.id,
                    walletId: wallet.id,
                });
                newAccountWallets.push(newEntity);
            }
        }
        await this.accountWalletsRepository.manager.transaction(async (manager) => {
            if (newAccountWallets.length > 0)
                await manager.save(newAccountWallets);
            if (updatedAccountWallets.length > 0)
                await manager.save(updatedAccountWallets);
        });
        processed.push(...newAccountWallets.map(aw => ({
            id: aw.id,
            account_code: accountMap.get(aw.accountId)?.code,
            wallet_id: aw.walletId,
            status: 'created',
        })), ...updatedAccountWallets.map(aw => ({
            id: aw.id,
            account_code: accountMap.get(aw.accountId)?.code,
            wallet_id: aw.walletId,
            status: 'updated',
        })));
        return { processed, errors };
    }
    async deleteAccountWalletsFromBulk(accountWallets) {
        const MAX_BATCH_SIZE = 20;
        if (!accountWallets || accountWallets.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (accountWallets.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many account-wallet relations in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: accountWallets.length,
            });
        }
        const processedAccountWallets = [];
        const errors = [];
        for (const item of accountWallets) {
            try {
                const agency = await this.agenciesService.findOne(item.agency_code);
                if (!agency) {
                    throw new common_1.NotFoundException(`Agency with code '${item.agency_code}' not found.`);
                }
                const account = await this.accountsService.findOne(item.account_code);
                if (!account) {
                    throw new common_1.NotFoundException(`Account with code '${item.account_code}' not found.`);
                }
                const wallet = await this.walletsRepository.findOne({
                    where: {
                        number: item.wallet_number,
                        agencyId: agency.id,
                    },
                });
                if (!wallet) {
                    throw new common_1.NotFoundException(`Wallet with number '${item.wallet_number}' and agency '${item.agency_code}' not found.`);
                }
                const existingAccountWallet = await this.accountWalletsRepository.findOne({
                    where: { accountId: account.id, walletId: wallet.id },
                });
                if (!existingAccountWallet) {
                    throw new common_1.NotFoundException(`Account-wallet relation for account '${item.account_code}' and wallet '${item.wallet_number}' not found.`);
                }
                existingAccountWallet.deletedAt = new Date();
                await this.accountWalletsRepository.save(existingAccountWallet);
                processedAccountWallets.push({
                    id: existingAccountWallet.id,
                    account_code: item.account_code,
                    wallet_number: item.wallet_number,
                    agency_code: item.agency_code,
                    deleted_at: existingAccountWallet.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    account_wallet: item,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some account-wallet relations had errors' : 'Account-wallet relations deleted successfully',
            processedAccountWallets,
            errors,
        };
    }
};
exports.AccountWalletsService = AccountWalletsService;
exports.AccountWalletsService = AccountWalletsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_wallets_entity_1.AccountWallets)),
    __param(4, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        accounts_service_1.AccountsService,
        agencies_service_1.AgenciesService,
        wallets_service_1.WalletsService,
        typeorm_2.Repository])
], AccountWalletsService);
//# sourceMappingURL=account-wallets.service.js.map