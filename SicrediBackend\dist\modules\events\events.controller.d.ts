import { EventsService } from './events.service';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
export declare class EventsController {
    private readonly eventsService;
    constructor(eventsService: EventsService);
    create(createEventDto: CreateEventDto): Promise<CreateEventDto>;
    findAll(): Promise<CreateEventDto[]>;
    findOne(id: string): Promise<CreateEventDto>;
    update(id: string, updateEventDto: UpdateEventDto): Promise<UpdateEventDto>;
    remove(id: string): Promise<void>;
}
