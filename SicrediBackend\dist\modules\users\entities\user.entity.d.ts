import { Profile } from 'src/modules/profiles/entities/profile.entity';
import { Wallet } from '../../wallets/entities/wallet.entity';
import { UserWallet } from '../../user-wallets/entities/user-wallets.entity';
import { Attendance } from 'src/modules/attendances/entities/attendance.entity';
import { Agency } from 'src/modules/agencies/entities/agency.entity';
import { Cooperative } from 'src/modules/cooperatives/entities/cooperative.entity';
import { Central } from 'src/modules/centrals/entities/central.entity';
export declare class User {
    id: number;
    phone: string;
    name: string;
    lastName: string;
    email: string;
    profileId: number;
    attendances: Attendance[];
    active: boolean;
    photo: string;
    notificationToken: string;
    idUserKeycloak: string;
    agencyId: number;
    centralId: number;
    federationId: number;
    cnpj: string;
    cpf: string;
    cooperativeId: number;
    serviceUnitNumber: string;
    birthDate: Date;
    geralRegister: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date;
    profile: Profile;
    wallet: Wallet[];
    userWallets: UserWallet[];
    agency: Agency;
    cooperative: Cooperative;
    central: Central;
}
