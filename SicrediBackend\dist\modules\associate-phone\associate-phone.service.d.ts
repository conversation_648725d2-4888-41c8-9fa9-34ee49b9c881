import { Repository } from "typeorm";
import { AssociatePhone } from "./entities/associate-phone.entity";
import { CreateAssociatePhoneDto } from "./dto/create-associate-phone.dto";
import { UpdateAssociatePhoneDto } from "./dto/update-associate-phone.dto";
import { Cryptography } from "src/common/functions/cryptography";
import { Associate } from "../associates/entities/associate.entity";
export declare class AssociatePhoneService {
    private readonly associatePhoneRepository;
    private readonly associateRepository;
    private readonly cryptography;
    constructor(associatePhoneRepository: Repository<AssociatePhone>, associateRepository: Repository<Associate>, cryptography: Cryptography);
    create(createAssociatePhoneDto: CreateAssociatePhoneDto): Promise<{
        id: number;
        associateId: number;
        phone: string;
        createdAt: Date;
    }>;
    createMany(createAssociatePhoneDto: CreateAssociatePhoneDto[]): Promise<{
        id: number;
        associateId: number;
        phone: string;
        createdAt: Date;
    }[]>;
    findAllByAssociate(id: number): Promise<{
        phone: string;
        id?: number;
        associateId: number;
        createdAt: Date;
        updatedAt?: Date;
        deletedAt?: Date;
        associate?: Associate;
    }[]>;
    findOne(id: number): Promise<{
        phone: string;
        id?: number;
        associateId: number;
        createdAt: Date;
        updatedAt?: Date;
        deletedAt?: Date;
        associate?: Associate;
    }>;
    update(id: number, updateAssociatePhoneDto: UpdateAssociatePhoneDto): Promise<{
        id: number;
        associateId: number;
        phone: string;
        createdAt: Date;
        updatedAt: Date;
    }>;
    remove(id: number): Promise<{
        id: number;
        associateId: number;
        phone: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date;
    }>;
}
