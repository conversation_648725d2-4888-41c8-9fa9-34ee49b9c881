"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPasswordLdapUsers1700000000000 = void 0;
const typeorm_1 = require("typeorm");
class AddPasswordLdapUsers1700000000000 {
    constructor() {
        this.name = 'AddPasswordLdapUsers1700000000000';
    }
    async up(queryRunner) {
        await queryRunner.addColumn('user', new typeorm_1.TableColumn({
            name: 'password',
            type: 'varchar',
            length: '500',
            isNullable: true,
        }));
        await queryRunner.addColumn('user', new typeorm_1.TableColumn({
            name: 'ldap',
            type: 'varchar',
            length: '300',
            isNullable: true,
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropColumn('user', 'ldap');
        await queryRunner.dropColumn('user', 'password');
    }
}
exports.AddPasswordLdapUsers1700000000000 = AddPasswordLdapUsers1700000000000;
//# sourceMappingURL=1700000000000-add-password-ldap-users.js.map