"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayloadCryptography = void 0;
const forge = __importStar(require("node-forge"));
class PayloadCryptography {
    password() {
        return process.env.CRYPTO_PAYLOAD_PASSWORD ?? '';
    }
    deriveKey(salt) {
        let saltData = process.env.CRYPTO_PAYLOAD_SALT ?? '';
        if (salt) {
            saltData = salt;
        }
        const password = process.env.CRYPTO_PAYLOAD_PASSWORD ?? '';
        const key = forge.pkcs5.pbkdf2(password, saltData, 20000, 32);
        const derivedKeyData = forge.util.createBuffer(key).getBytes();
        return { key: derivedKeyData, salt: saltData };
    }
    encrypt(textToEncrypt) {
        const iv = forge.random.getBytesSync(16);
        const { key, salt } = this.deriveKey();
        const cipher = forge.cipher.createCipher('AES-CTR', key);
        cipher.start({ iv: iv });
        cipher.update(forge.util.createBuffer(textToEncrypt, 'utf8'));
        cipher.finish();
        const encrypted = cipher.output.getBytes();
        return {
            salt: forge.util.encode64(salt),
            iv: forge.util.encode64(iv),
            encrypted: forge.util.encode64(encrypted),
        };
    }
    decrypt(encryptedData) {
        const { salt, iv, encrypted } = encryptedData;
        const saltBytes = forge.util.decode64(salt);
        const ivBytes = forge.util.decode64(iv);
        const encryptedBytes = forge.util.decode64(encrypted);
        const { key } = this.deriveKey(saltBytes);
        const decipher = forge.cipher.createDecipher('AES-CTR', key);
        decipher.start({ iv: ivBytes });
        decipher.update(forge.util.createBuffer(encryptedBytes));
        const success = decipher.finish();
        if (!success) {
            throw new Error('Failed to decrypt the data.');
        }
        return decipher.output.toString();
    }
}
exports.PayloadCryptography = PayloadCryptography;
//# sourceMappingURL=payload-cryptography.js.map