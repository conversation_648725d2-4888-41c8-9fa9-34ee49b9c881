"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateSeeder = void 0;
const common_1 = require("@nestjs/common");
const associate_entity_1 = require("../modules/associates/entities/associate.entity");
const typeorm_1 = require("typeorm");
const cryptography_1 = require("../common/functions/cryptography");
let AssociateSeeder = class AssociateSeeder {
    constructor(dataSource, cryptography) {
        this.dataSource = dataSource;
        this.cryptography = cryptography;
    }
    async executeSeed() {
        const associateRepository = this.dataSource.getRepository(associate_entity_1.Associate);
        await this.seedAssociate({
            name: "Arthur Abreu",
            cpf: this.cryptography.encrypt("912.660.940-16"),
            email: this.cryptography.encrypt("<EMAIL>"),
            income: "2500.75",
            isAssociate: true,
            preApprovedCredit: false,
            birthDate: new Date("1990-02-21"),
            createdAt: new Date("2025-02-21T15:55:04.967Z"),
            updatedAt: null,
            deletedAt: null,
        }, associateRepository);
    }
    async seedAssociate(associateData, associateRepository) {
        const existing = await associateRepository.findOne({ where: { email: associateData.email } });
        if (!existing) {
            const associate = associateRepository.create(associateData);
            await associateRepository.save(associate);
        }
    }
};
exports.AssociateSeeder = AssociateSeeder;
exports.AssociateSeeder = AssociateSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        cryptography_1.Cryptography])
], AssociateSeeder);
//# sourceMappingURL=associate.seeder.js.map