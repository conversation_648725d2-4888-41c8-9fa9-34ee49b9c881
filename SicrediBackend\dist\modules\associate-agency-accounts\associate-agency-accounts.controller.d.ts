import { AssociateAgencyAccountsService } from './associate-agency-accounts.service';
import { CreateAssociateAgencyAccountDto } from './dto/create-associate-agency-account.dto';
import { UpdateAssociateAgencyAccountDto } from './dto/update-associate-agency-account.dto';
export declare class AssociateAgencyAccountsController {
    private readonly associateAgencyAccountsService;
    constructor(associateAgencyAccountsService: AssociateAgencyAccountsService);
    create(createAssociateAgencyAccountDto: CreateAssociateAgencyAccountDto): Promise<CreateAssociateAgencyAccountDto>;
    findAll(): Promise<CreateAssociateAgencyAccountDto[]>;
    findOne(id: string): Promise<CreateAssociateAgencyAccountDto>;
    update(id: string, updateAssociateAgencyAccountDto: UpdateAssociateAgencyAccountDto): Promise<UpdateAssociateAgencyAccountDto>;
    remove(id: string): Promise<void>;
}
