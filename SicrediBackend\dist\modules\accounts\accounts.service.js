"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const account_entity_1 = require("./entities/account.entity");
const agencies_service_1 = require("../agencies/agencies.service");
const associates_service_1 = require("../associates/associates.service");
const account_types_service_1 = require("../account-type/account-types.service");
const attendance_entity_1 = require("../attendances/entities/attendance.entity");
const account_wallets_entity_1 = require("../account-wallets/entities/account-wallets.entity");
const cryptography_1 = require("../../common/functions/cryptography");
let AccountsService = class AccountsService {
    constructor(accountRepository, agenciesService, accountWalletsRepository, attendanceRepository, associatesService, accountTypesService, cryptography) {
        this.accountRepository = accountRepository;
        this.agenciesService = agenciesService;
        this.accountWalletsRepository = accountWalletsRepository;
        this.attendanceRepository = attendanceRepository;
        this.associatesService = associatesService;
        this.accountTypesService = accountTypesService;
        this.cryptography = cryptography;
    }
    async createAccountsFromBulk(accountDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!accountDto || accountDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (accountDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many accounts in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: accountDto.length,
            });
        }
        const allMissingFields = [];
        accountDto.forEach((item, index) => {
            if (!item.code)
                allMissingFields.push(`code (index ${index})`);
            if (!item.agency_code)
                allMissingFields.push(`agency_code (index ${index})`);
            if (!item.associate_cpf && !item.associate_cnpj)
                allMissingFields.push(`associate_cpf or associate_cnpj (index ${index})`);
            if (!item.account_type_key)
                allMissingFields.push(`account_type_key (index ${index})`);
        });
        if (allMissingFields.length > 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Missing required fields: ${allMissingFields.join(', ')}`,
            });
        }
        const normalize = (doc) => doc.replace(/\D/g, '');
        const encryptedCpfMap = new Map();
        const encryptedCnpjMap = new Map();
        for (const item of accountDto) {
            if (item.associate_cpf) {
                const doc = normalize(item.associate_cpf);
                if (!encryptedCpfMap.has(doc)) {
                    encryptedCpfMap.set(doc, this.cryptography.encrypt(doc));
                }
            }
            if (item.associate_cnpj) {
                const doc = normalize(item.associate_cnpj);
                if (!encryptedCnpjMap.has(doc)) {
                    encryptedCnpjMap.set(doc, this.cryptography.encrypt(doc));
                }
            }
        }
        const batches = [];
        for (let i = 0; i < accountDto.length; i += BATCH_SIZE) {
            batches.push(accountDto.slice(i, i + BATCH_SIZE));
        }
        const results = await Promise.allSettled(batches.map((batch, index) => this.processAccountBatch(batch, index * BATCH_SIZE, encryptedCpfMap, encryptedCnpjMap)));
        const processedAccounts = [];
        const errors = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                processedAccounts.push(...result.value);
            }
            else {
                errors.push({
                    status: 'error',
                    message: `Erro ao processar lote ${index}: ${result.reason?.message || 'Erro inesperado'}`,
                });
            }
        });
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Algumas contas apresentaram erros' : 'Contas processadas com sucesso',
            processedAccounts,
            errors,
        };
    }
    async processAccountBatch(batch, offset, encryptedCpfMap, encryptedCnpjMap) {
        const normalize = (doc) => doc.replace(/\D/g, '');
        const cpfList = batch
            .filter(item => item.associate_cpf)
            .map(item => normalize(item.associate_cpf));
        const cnpjList = batch
            .filter(item => item.associate_cnpj)
            .map(item => normalize(item.associate_cnpj));
        const agencyCodes = batch.map(item => item.agency_code);
        const accountTypeKeys = batch.map(item => item.account_type_key);
        const accountCodes = batch.map(item => item.code);
        const [agencies, associates, accountTypes, existingAccounts] = await Promise.all([
            this.agenciesService.findByCodes(agencyCodes),
            this.associatesService.findByDocuments(cpfList.map(cpf => encryptedCpfMap.get(cpf)), cnpjList.map(cnpj => encryptedCnpjMap.get(cnpj))),
            this.accountTypesService.findByKeys(accountTypeKeys),
            this.accountRepository.find({ where: { code: (0, typeorm_2.In)(accountCodes) } }),
        ]);
        const agencyMap = new Map(agencies.map(a => [a.agencyCode, a]));
        const associateMap = new Map(associates.map(a => [a.cpf ?? a.cnpj, a]));
        const accountTypeMap = new Map(accountTypes.map(at => [at.key, at]));
        const existingAccountMap = new Map(existingAccounts.map(acc => [acc.code, acc]));
        const newAccounts = [];
        const updatedAccounts = [];
        const processed = [];
        for (const item of batch) {
            const agency = agencyMap.get(item.agency_code);
            const doc = item.associate_cpf ? normalize(item.associate_cpf) : normalize(item.associate_cnpj);
            const encryptedDoc = item.associate_cpf ? encryptedCpfMap.get(doc) : encryptedCnpjMap.get(doc);
            const associate = associateMap.get(encryptedDoc);
            const accountType = accountTypeMap.get(item.account_type_key);
            if (!agency || !associate || !accountType) {
                processed.push({
                    account: item.code,
                    status: 'error',
                    message: [
                        !agency ? `Agência não encontrada: ${item.agency_code}` : '',
                        !associate ? `Associado não encontrado para CPF/CNPJ informado` : '',
                        !accountType ? `Tipo de conta não encontrado: ${item.account_type_key}` : '',
                    ].filter(Boolean).join('. '),
                });
                continue;
            }
            let account = existingAccountMap.get(item.code);
            if (account) {
                account.agencyId = agency.id;
                account.associateId = associate.id;
                account.cnpj = associate.cnpj;
                account.accountTypeId = accountType.id;
                account.paymentKey = item.paymentKey;
                account.lastAccountActivity = item.lastAccountActivity;
                account.automaticDebit = item.automaticDebit;
                updatedAccounts.push(account);
            }
            else {
                const newAccount = this.accountRepository.create({
                    code: item.code,
                    agencyId: agency.id,
                    associateId: associate.id,
                    cnpj: associate.cnpj,
                    accountTypeId: accountType.id,
                    paymentKey: item.paymentKey,
                    lastAccountActivity: item.lastAccountActivity,
                    automaticDebit: item.automaticDebit,
                });
                newAccounts.push(newAccount);
            }
        }
        await this.accountRepository.manager.transaction(async (manager) => {
            if (newAccounts.length > 0)
                await manager.save(newAccounts);
            if (updatedAccounts.length > 0)
                await manager.save(updatedAccounts);
        });
        processed.push(...newAccounts.map(a => ({
            code: a.code,
            status: 'created',
        })), ...updatedAccounts.map(a => ({
            code: a.code,
            status: 'updated',
        })));
        return processed;
    }
    async deleteAccountsFromBulk(accountCodes) {
        const MAX_BATCH_SIZE = 20;
        if (!accountCodes || accountCodes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (accountCodes.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many accounts in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: accountCodes.length,
            });
        }
        const processedAccounts = [];
        const errors = [];
        for (const accountCode of accountCodes) {
            try {
                const account = await this.accountRepository.findOne({ where: { code: accountCode } });
                if (!account) {
                    throw new common_1.NotFoundException(`Account with code '${accountCode}' not found.`);
                }
                const linkedWallets = await this.accountWalletsRepository.find({
                    where: { accountId: account.id },
                    select: ['walletId'],
                });
                if (linkedWallets.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete account '${accountCode}' because it is linked to ${linkedWallets.length} wallet(s).`,
                        linked_wallets: linkedWallets,
                    });
                }
                const linkedAttendances = await this.attendanceRepository.find({
                    where: { accountId: account.id },
                    select: ['id'],
                });
                if (linkedAttendances.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete account '${accountCode}' because it is linked to ${linkedAttendances.length} attendance record(s).`,
                        linked_attendances: linkedAttendances,
                    });
                }
                account.deletedAt = new Date();
                await this.accountRepository.save(account);
                processedAccounts.push({
                    id: account.id,
                    code: account.code,
                    deleted_at: account.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    account_code: accountCode,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some accounts had errors' : 'Accounts deleted successfully',
            processedAccounts,
            errors,
        };
    }
    async findOne(identifier) {
        let account;
        if (typeof identifier === 'number') {
            account = await this.accountRepository.findOne({
                where: { id: identifier, deletedAt: (0, typeorm_2.IsNull)() },
            });
        }
        else {
            account = await this.accountRepository.findOne({
                where: { code: identifier, deletedAt: (0, typeorm_2.IsNull)() },
            });
        }
        if (!account) {
            throw new common_1.NotFoundException(`Account with ${typeof identifier === 'number' ? 'ID' : 'CODE'} ${identifier} not found.`);
        }
        return {
            id: account.id,
            code: account.code,
            associate_cpf: account.associateId,
            associate_cnpj: account.associateId,
            agency_code: account.agencyId,
            cnpj: account.cnpj,
            account_type_key: "",
            paymentKey: account.paymentKey,
            lastAccountActivity: account.lastAccountActivity,
            automaticDebit: account.automaticDebit,
        };
    }
    async findByCodes(codes) {
        if (!codes || codes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'A lista de códigos não pode estar vazia.',
            });
        }
        const centrals = await this.accountRepository.find({
            where: { code: (0, typeorm_2.In)(codes) },
        });
        if (!centrals || centrals.length === 0) {
            throw new common_1.NotFoundException({
                status: 'error',
                message: 'Nenhuma central encontrada para os códigos fornecidos.',
            });
        }
        return centrals;
    }
};
exports.AccountsService = AccountsService;
exports.AccountsService = AccountsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_entity_1.Accounts)),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => agencies_service_1.AgenciesService))),
    __param(2, (0, typeorm_1.InjectRepository)(account_wallets_entity_1.AccountWallets)),
    __param(3, (0, typeorm_1.InjectRepository)(attendance_entity_1.Attendance)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        agencies_service_1.AgenciesService,
        typeorm_2.Repository,
        typeorm_2.Repository,
        associates_service_1.AssociatesService,
        account_types_service_1.AccountTypeService,
        cryptography_1.Cryptography])
], AccountsService);
//# sourceMappingURL=accounts.service.js.map