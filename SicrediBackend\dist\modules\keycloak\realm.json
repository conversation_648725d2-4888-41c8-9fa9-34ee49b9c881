{"id": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "realm": "dev", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 86400, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 14400, "ssoSessionMaxLifespan": 2073600, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 1800, "accessCodeLifespanLogin": 14400, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "13139b6f-770b-472a-b787-97c1482b74bd", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "cfff633c-7e61-4a02-bfc7-9e856caa32b2", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "cd1cb072-31d8-46a9-ae3f-b6dd2636b981", "name": "gerente-agencia", "description": "", "composite": true, "composites": {"client": {"realm-management": ["manage-users", "view-users", "query-users"]}}, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "461fb92e-2603-4306-968e-d81f00e3ac9c", "name": "gestor-carteira", "description": "", "composite": true, "composites": {"client": {"realm-management": ["manage-users", "view-users", "query-users"]}}, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "9d40c2e0-ab93-48de-a1b1-f3b224546d7e", "name": "gerente-operacional", "description": "", "composite": true, "composites": {"client": {"realm-management": ["manage-users", "view-users", "query-users"]}}, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "a00e121b-ac0a-4e24-bba7-278838b0898c", "name": "agencia", "description": "", "composite": true, "composites": {"client": {"realm-management": ["manage-users", "view-users", "query-users"]}}, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "3d062c91-e8c4-427e-96ed-d3d413f01d5a", "name": "default-roles-dev", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}, {"id": "800cc7fb-b860-40c7-84e7-4dd205410a30", "name": "assistente", "description": "", "composite": true, "composites": {"client": {"realm-management": ["view-users"]}}, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7", "attributes": {}}], "client": {"realm-management": [{"id": "b18b0f5e-093f-4429-ba4c-6f2342281a2b", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "6685063f-b804-4ad9-9f18-ca22946ed30e", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "6d8ab068-51fd-42f2-9cfb-88ba8ea59053", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "4c3a1938-7b78-4a34-80a5-c71c8bfa9245", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "98816d65-153f-4f2f-b44b-ce332a04312c", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "5acb6cba-0c85-4b6d-b3c7-be0f3d4bdaf9", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "54460d1e-f125-4dce-9036-9078960c621b", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "889b3f6f-34c6-475c-a003-738858e83d26", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "24ad1bdd-a76f-489b-99a5-95326eefb64a", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "dac856ed-6521-485e-9f68-4bda0c1563df", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "d9ad9fcb-40ff-41b7-963c-e4ac101153ef", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "fa462c89-50db-4d75-980f-f29e71368c4f", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "18c8dd93-6d51-4cd4-916b-04509652f69b", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "cdc8f6ee-b3a2-4219-b038-a865da4283da", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "19398226-4a08-4b8b-91fb-ba1d30066844", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "030f8e97-6d83-4921-98ea-c0709504f9c8", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "b6c52f1a-01a4-46fc-b905-914e174fe470", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-users", "view-users", "view-realm", "view-identity-providers", "view-authorization", "manage-identity-providers", "query-realms", "manage-authorization", "view-clients", "query-users", "manage-realm", "create-client", "manage-clients", "query-clients", "impersonation", "view-events", "manage-events", "query-groups"]}}, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "d7500642-bf65-4601-8d5a-bcba263f2704", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}, {"id": "44330b8c-3a7a-4cb4-97a1-bf25ed6b2f94", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "nest": [{"id": "9491e2d4-252a-4cc9-bd4a-6e3d90787858", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "d4c12321-1210-4b94-8ef7-139023c361d9", "attributes": {}}], "broker": [{"id": "653cf3cc-2359-4632-b2ee-a1c72ff8a09f", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "048175d6-5243-4b8f-b9b5-a5e82dc6098e", "attributes": {}}], "account": [{"id": "01faa215-02de-48d5-9f1f-6f580031b5db", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "50b81e5b-f19c-4228-9e1b-8edea73d9ea1", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "efd256e2-e27c-4a19-8d21-e177b1e6821e", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "222049e3-f6e0-4679-a65f-2c3d7dda641d", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "ba97766e-f44d-416d-a9bb-21c0d69d17a8", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "e77c1f98-e86a-4d04-834d-11b620f992b3", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "9f5abcce-4cc1-42aa-9550-a972a964e65c", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}, {"id": "59e843c1-3346-4986-b72a-7296c66da95b", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "attributes": {}}]}}, "groups": [{"id": "a0985019-75be-45aa-a2c6-74ad0fc3cf5f", "name": "create-user", "path": "/create-user", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"realm-management": ["manage-users", "view-users", "query-users"]}}], "defaultRole": {"id": "3d062c91-e8c4-427e-96ed-d3d413f01d5a", "name": "default-roles-dev", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "5b23cb8b-d9b3-4006-806b-1ebad6d8d8e7"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256", "RS256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "users": [{"id": "f5dbdbb2-6516-41c0-b651-044b63000d63", "username": "service-account-nest", "emailVerified": false, "createdTimestamp": *************, "enabled": true, "totp": false, "serviceAccountClientId": "nest", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-dev"], "clientRoles": {"nest": ["uma_protection"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "e1fa3b52-a38a-4eca-aeb9-8d64e62d7745", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "26e9f343-0965-423d-8d38-f917c1261bbc", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dev/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dev/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "e1d83da4-acaa-4c15-b5e3-f80a7a8c6a27", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "310629f6-5546-49a0-b575-d4eb5589b435", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "048175d6-5243-4b8f-b9b5-a5e82dc6098e", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "d4c12321-1210-4b94-8ef7-139023c361d9", "clientId": "nest", "name": "nest", "description": "", "rootUrl": "", "adminUrl": "", "baseUrl": "", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "**********", "redirectUris": ["http://localhost:3000/api/v1/"], "webOrigins": ["http://localhost:3000/"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "authorizationServicesEnabled": true, "publicClient": false, "frontchannelLogout": true, "protocol": "openid-connect", "attributes": {"realm_client": "false", "oidc.ciba.grant.enabled": "false", "client.secret.creation.time": "**********", "backchannel.logout.session.required": "true", "login_theme": "keycloak.v2", "post.logout.redirect.uris": "+", "display.on.consent.screen": "false", "oauth2.device.authorization.grant.enabled": "false", "use.jwks.url": "false", "backchannel.logout.revoke.offline.tokens": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "a3bf8d73-b6f3-4d2d-ad24-ec2bfa80731b", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "395ed4cb-a283-4594-96c5-8b05b9f5b04b", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "5fbd0e1c-e933-4673-bb2b-d690c9b740f7", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "client_id", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "client_id", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "openid", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"], "authorizationSettings": {"allowRemoteResourceManagement": true, "policyEnforcementMode": "ENFORCING", "resources": [{"name": "Default Resource", "type": "urn:nest:resources:default", "ownerManagedAccess": false, "attributes": {}, "uris": ["/*"]}], "policies": [], "scopes": [], "decisionStrategy": "UNANIMOUS"}}, {"id": "e56a9459-fdeb-4b7b-82d2-9bf2e4502a37", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "true", "post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}, {"id": "d3bff0cf-4f12-4058-a1a2-ca6554007f60", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/dev/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/dev/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"realm_client": "false", "client.use.lightweight.access.token.enabled": "true", "post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "ec51ed85-d820-478e-9e37-dc8969d73f74", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "roles", "profile", "basic", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "organization", "microprofile-jwt"]}], "clientScopes": [{"id": "908a8abd-9588-4e2e-b111-0fa0cbf38257", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "9a9ec5c6-c7bb-4c4c-9e8e-9923ff009e4e", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "31e3fc67-2b31-49a7-bc3f-3fca61c885c7", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "c119223a-0e86-4939-af28-30d5d72d4696", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "2f3c8508-75ec-4c7d-8cb7-cd63f8e38ca3", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "384c47a0-4b5a-41df-b386-c4deb5a0220a", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}, {"id": "d210abc8-a58c-4694-89c0-34da1f215cfb", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "consent.screen.text": "${rolesScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "c22b25e9-6da3-4336-868f-c4d30b6d8c01", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}, {"id": "9a1df3fa-d99d-4e44-9a0b-f72c7b701a44", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "3bfb7bd8-e9b0-49bd-8ee4-22a287363087", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "introspection.token.claim": "true", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "7e5ad583-9abd-4c5f-b04f-29f9ad582ff6", "name": "basic", "description": "OpenID Connect scope for add all basic claims to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "37b97ee9-99b4-4b6f-8e8d-e885976f5c70", "name": "auth_time", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "AUTH_TIME", "introspection.token.claim": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "auth_time", "jsonType.label": "long"}}, {"id": "68cafd54-4b40-4837-8d14-95c3d53a35d1", "name": "sub", "protocol": "openid-connect", "protocolMapper": "oidc-sub-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "c3d2d349-3a34-4f09-aa0b-88a2dfb3bd60", "name": "organization", "description": "Additional claims about the organization a subject belongs to", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${organizationScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "c474cbf4-2545-4cca-bcf4-06969e6debb1", "name": "organization", "protocol": "openid-connect", "protocolMapper": "oidc-organization-membership-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "organization", "jsonType.label": "String"}}]}, {"id": "d98bee83-6bec-4a5e-94cd-699274fc1970", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${phoneScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "7d3d7993-5372-402e-a39c-76694ac7fa72", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "a3a7e9d2-07de-4534-953f-343aa575830c", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "ac6bee39-0aff-4634-a9ef-40b3f94ff074", "name": "saml_organization", "description": "Organization Membership", "protocol": "saml", "attributes": {"display.on.consent.screen": "false"}, "protocolMappers": [{"id": "8259fa02-28cb-405c-92f7-c3dff10be93e", "name": "organization", "protocol": "saml", "protocolMapper": "saml-organization-membership-mapper", "consentRequired": false, "config": {}}]}, {"id": "2e11aa21-4567-48b1-a0bc-bc2337ff7428", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "05ca54e9-3cd7-4903-9530-9d0174ca8365", "name": "openid", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "gui.order": "", "consent.screen.text": ""}}, {"id": "20da94ae-a27b-450a-b559-a481df7ee984", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${addressScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "087f147c-34eb-4500-b11b-a50aaebbefff", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "introspection.token.claim": "true", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "4e686db8-6f19-450f-9264-d974c2ee00ad", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${profileScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "87c0f789-2cf5-47dc-a085-b56165fa968a", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "71d721ab-037f-4ef7-9028-14ee0d81ea5b", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "99d44545-abce-47f4-a17a-2e77d45dabbf", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "54736969-1617-4717-aafc-785dbe8640f3", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "9f67d609-97c7-4b5f-985b-613951701464", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "fb1a953a-9eb9-41db-ba8b-7a7faf03ea4e", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "4397e0dd-841c-452e-8bd0-fc96018fb384", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "6360f9d4-066c-40f7-abe8-fac258b1f28e", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "85ebf483-bd20-44c1-a51d-cf93fa8b1e7b", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "da9275a4-e3d2-4bdf-9e36-006d77900f1f", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "80cb41f4-1784-48aa-97fa-f1a3479e693e", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "56850d80-4ca8-4a56-a6cf-1edd0d97101b", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updatedAt", "jsonType.label": "long"}}, {"id": "c8a76da0-479a-4e5f-bc47-ab26f36a6e3d", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "ee4d2d06-adda-4002-8af9-155a87e97ad5", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}]}, {"id": "e48e93c1-eb0a-4652-b30c-46712d2e2aee", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "consent.screen.text": "${emailScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "535496a3-754b-42af-a89d-3597e85df049", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "8fc56eb9-0d9a-4886-ae05-fda4e6fec4d4", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "65352f06-8862-4153-b0e3-d441434709f8", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "bb60a31f-78f6-4db6-a4d4-bac9a4509eed", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "5d8b7203-6772-4bc0-ae18-e46f62f7fab2", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"introspection.token.claim": "true", "userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["role_list", "saml_organization", "profile", "email", "roles", "web-origins", "acr", "basic", "openid"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt", "organization"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "f309a9b9-a07d-4215-ad9b-bf7a7a674071", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "99d644a8-7136-4adf-99c1-2e03c057e2af", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "28b96569-906d-4eae-96af-927a6424b1fd", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "saml-role-list-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "saml-user-property-mapper"]}}, {"id": "a25461a4-547d-4e4a-b824-77fbff8d200a", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "44c611df-f466-4205-91b0-73e0a649a2a8", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "afcf24ee-46b4-46b7-8de4-0c4b6f95450e", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "oidc-usermodel-attribute-mapper", "saml-role-list-mapper"]}}, {"id": "66d251f2-4d9a-43cb-af29-671bd3a78dec", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "383b7f59-cb3c-4d3c-a352-ae106bc86531", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "e719fa79-d207-43ac-b222-3bc91e23445e", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "194336fd-cd67-44b5-92bb-fa65dba9506f", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "c6208fb5-1b3c-424d-b9f9-a67abd89ba08", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS512"]}}, {"id": "dc29af76-4907-48a8-b063-7071c96b73f1", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "1d83a906-1f62-4ea7-9c6f-b75ab3fec025", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "a4e91d08-a026-4616-b318-b543585575de", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "709caeaa-2b1a-45e0-99a3-f12d50b6bac3", "alias": "Browser - Conditional Organization", "description": "Flow to determine if the organization identity-first login is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "organization", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "bee4d99a-5e19-4a9f-9650-87944c1de167", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "245a0005-4b21-4397-88c6-deffb039a1a9", "alias": "First Broker Login - Conditional Organization", "description": "Flow to determine if the authenticator that adds organization members is to be used", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "idp-add-organization-member", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "30b0aac1-2b36-4513-824d-c7d016024e78", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b006ae86-2af7-4557-b4e3-01d71dea92f7", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "a6912c05-c512-4741-bc94-f7ea13af7422", "alias": "Organization", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 10, "autheticatorFlow": true, "flowAlias": "Browser - Conditional Organization", "userSetupAllowed": false}]}, {"id": "f82df1d4-e142-4bf6-b679-06870ffa2f61", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "63e12aed-50d9-40c2-9b37-0e7f71097cd8", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "ee14958c-0144-41ad-8ae6-cf3d254df830", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "4a10b29e-f4b3-475a-8021-54eb68209122", "alias": "browser", "description": "Browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 26, "autheticatorFlow": true, "flowAlias": "Organization", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "c13feb1c-6484-4a1e-b108-65a7be64001f", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "52077d24-6156-4f36-a99a-b0a9cc065e05", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "5181db27-211d-4697-94dd-2bb8a7eed692", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "49614b35-7ed7-40a8-9427-05b60e27b2b5", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 50, "autheticatorFlow": true, "flowAlias": "First Broker Login - Conditional Organization", "userSetupAllowed": false}]}, {"id": "d0fe6116-93cf-4187-9791-3c5787b3c28e", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "30706514-affe-450c-bf1c-aa69789efa37", "alias": "registration", "description": "Registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "d7b82823-d178-4926-a327-12b2f3aa5794", "alias": "registration form", "description": "Registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-terms-and-conditions", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 70, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "047c2ac6-5182-4a16-9c22-b37293d84d9a", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "fb2aeac2-6d0f-4016-8d2a-f0c5b817e395", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "55e5636f-628d-40e8-84d5-82f8a6c389ca", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "606fd311-0c9a-4160-ad04-d9d3b2747765", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "VERIFY_PROFILE", "name": "Verify Profile", "providerId": "VERIFY_PROFILE", "enabled": true, "defaultAction": false, "priority": 90, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaAuthRequestedUserHint": "login_hint", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan.verify-email": "", "actionTokenGeneratedByUserLifespan.idp-verify-account-via-email": "", "clientOfflineSessionIdleTimeout": "0", "actionTokenGeneratedByUserLifespan.execute-actions": "", "cibaInterval": "5", "realmReusableOtpCode": "false", "cibaExpiresIn": "120", "oauth2DeviceCodeLifespan": "600", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "organizationsEnabled": "false", "shortVerificationUri": "", "actionTokenGeneratedByUserLifespan.reset-credentials": ""}, "keycloakVersion": "26.0.1", "userManagedAccessAllowed": false, "organizationsEnabled": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}