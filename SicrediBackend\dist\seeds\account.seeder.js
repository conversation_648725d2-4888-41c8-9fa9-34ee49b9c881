"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const account_entity_1 = require("../modules/accounts/entities/account.entity");
const associate_entity_1 = require("../modules/associates/entities/associate.entity");
const agency_entity_1 = require("../modules/agencies/entities/agency.entity");
const cryptography_1 = require("../common/functions/cryptography");
const account_type_entity_1 = require("../modules/account-type/entities/account-type.entity");
let AccountSeeder = class AccountSeeder {
    constructor(dataSource, cryptography) {
        this.dataSource = dataSource;
        this.cryptography = cryptography;
    }
    async executeSeed() {
        const accountRepository = this.dataSource.getRepository(account_entity_1.Accounts);
        const associateRepository = this.dataSource.getRepository(associate_entity_1.Associate);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const accountTypeRepository = this.dataSource.getRepository(account_type_entity_1.AccountType);
        const associate = await associateRepository.findOne({
            where: { name: 'Arthur Abreu' },
        });
        const agency = await agencyRepository.findOne({
            where: { name: 'Agência Central' },
        });
        const accountType = await accountTypeRepository.findOne({
            where: { name: 'PESSOA FÍSICA' },
        });
        if (!associate || !agency || !accountType) {
            return;
        }
        await this.seedAccount({
            code: '123',
            associateId: associate.id,
            agencyId: agency.id,
            cnpj: this.cryptography.encrypt('**************'),
            accountTypeId: accountType.id,
            createdAt: new Date('2025-02-21T15:55:04.980Z'),
            updatedAt: null,
            deletedAt: null,
        }, accountRepository);
    }
    async seedAccount(accountData, accountRepository) {
        const existing = await accountRepository.findOne({
            where: { code: accountData.code },
        });
        if (!existing) {
            const account = accountRepository.create(accountData);
            await accountRepository.save(account);
        }
    }
};
exports.AccountSeeder = AccountSeeder;
exports.AccountSeeder = AccountSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        cryptography_1.Cryptography])
], AccountSeeder);
//# sourceMappingURL=account.seeder.js.map