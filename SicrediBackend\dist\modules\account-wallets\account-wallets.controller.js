"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountWalletsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const account_wallets_service_1 = require("./account-wallets.service");
const create_account_wallets_dto_1 = require("./dto/create-account-wallets.dto");
let AccountWalletsController = class AccountWalletsController {
    constructor(accountWalletsService) {
        this.accountWalletsService = accountWalletsService;
    }
    async createBulk(accountWalletsDto) {
        if (!accountWalletsDto || accountWalletsDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.accountWalletsService.createAccountWalletsFromBulk(accountWalletsDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(body) {
        if (!body.accountWallets || body.accountWallets.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.accountWalletsService.deleteAccountWalletsFromBulk(body.accountWallets);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.AccountWalletsController = AccountWalletsController;
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltilas relações conta-carteira em massa',
        description: `Endpoint para criar ou atualizar múltiplas relações entre contas e carteiras.
                      Se a relação já existir, os dados serão atualizados.`,
    }),
    (0, swagger_1.ApiBody)({
        type: [create_account_wallets_dto_1.CreateAccountWalletsDto],
        description: 'Array de objetos contendo os dados das relações conta-carteira a serem criadas ou atualizadas',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar/atualizar múltiplas relações conta-carteira',
                value: [
                    {
                        account_code: '0123',
                        wallet_number: '01.01.01',
                        agency_code: '01',
                    },
                    {
                        account_code: '0456',
                        wallet_number: '02.02.02',
                        agency_code: '02',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Relações conta-carteira criadas ou atualizadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Account-Wallets processed successfully',
                processedAccountWallets: [
                    {
                        id: 1,
                        account_code: '0123',
                        wallet_number: '01.01.01',
                        wallet_number_old: '131',
                        agency_code: '01',
                    },
                    {
                        id: 2,
                        account_code: '0456',
                        wallet_number: '02.02.02',
                        wallet_number_old: '121',
                        agency_code: '02',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: account_code, wallet_number, agency_code',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AccountWalletsController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir múltiplas relações de conta-carteira',
        description: `
            Este endpoint permite a exclusão de múltiplas relações entre contas e carteiras em uma única requisição.
            Caso a relação não exista, um erro será retornado.
            A exclusão é feita via *soft delete*.
        `,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de relações de conta-carteira a serem excluídas',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar relações de conta-carteira',
                value: {
                    accountWallets: [
                        { account_code: 'ACC123', wallet_number: '01.01.01', agency_code: 'AG01' },
                        { account_code: 'ACC456', wallet_number: '02.02.02', agency_code: 'AG02' },
                    ],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Relações de conta-carteira excluídas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Account-wallet relations deleted successfully',
                processedAccountWallets: [
                    {
                        id: 1,
                        account_code: 'ACC123',
                        wallet_number: '01.01.01',
                        agency_code: 'AG01',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas relações de conta-carteira foram excluídas, mas outras apresentaram erro.',
        schema: {
            example: {
                status: 'partial_success',
                message: 'Some account-wallet relations had errors',
                processedAccountWallets: [
                    {
                        id: 1,
                        account_code: 'ACC123',
                        wallet_number: '01.01.01',
                        agency_code: 'AG01',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        account_wallet: {
                            account_code: 'ACC789',
                            wallet_number: '03.03.03',
                            agency_code: 'AG03',
                        },
                        status: 'error',
                        message: "Account-wallet relation for account 'ACC789' and wallet '03.03.03' not found.",
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AccountWalletsController.prototype, "deleteBulk", null);
exports.AccountWalletsController = AccountWalletsController = __decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, common_1.Controller)('/api/v1/account-wallets'),
    __metadata("design:paramtypes", [account_wallets_service_1.AccountWalletsService])
], AccountWalletsController);
//# sourceMappingURL=account-wallets.controller.js.map