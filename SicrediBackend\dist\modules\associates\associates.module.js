"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociatesModule = void 0;
const common_1 = require("@nestjs/common");
const associates_service_1 = require("./associates.service");
const associates_controller_1 = require("./associates.controller");
const typeorm_1 = require("@nestjs/typeorm");
const associate_entity_1 = require("./entities/associate.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const account_entity_1 = require("../accounts/entities/account.entity");
const account_wallets_entity_1 = require("../account-wallets/entities/account-wallets.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const agencies_module_1 = require("../agencies/agencies.module");
const attendance_entity_1 = require("../attendances/entities/attendance.entity");
const propensity_product_entity_1 = require("../propensity-products/entities/propensity-product.entity");
const attendance_history_entity_1 = require("../attendance-history/entities/attendance-history.entity");
const associate_phone_entity_1 = require("../associate-phone/entities/associate-phone.entity");
let AssociatesModule = class AssociatesModule {
};
exports.AssociatesModule = AssociatesModule;
exports.AssociatesModule = AssociatesModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([
                associate_entity_1.Associate,
                account_entity_1.Accounts,
                account_wallets_entity_1.AccountWallets,
                wallet_entity_1.Wallet,
                attendance_entity_1.Attendance,
                propensity_product_entity_1.PropensityProduct,
                account_entity_1.Accounts,
                attendance_history_entity_1.AttendanceHistory,
                associate_phone_entity_1.AssociatePhone,
            ]), (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule)],
        controllers: [associates_controller_1.AssociatesController],
        providers: [associates_service_1.AssociatesService, cryptography_1.Cryptography],
        exports: [associates_service_1.AssociatesService, cryptography_1.Cryptography],
    })
], AssociatesModule);
//# sourceMappingURL=associates.module.js.map