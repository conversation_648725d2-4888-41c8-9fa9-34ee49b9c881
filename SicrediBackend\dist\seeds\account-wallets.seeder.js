"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountWalletSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const account_wallets_entity_1 = require("../modules/account-wallets/entities/account-wallets.entity");
const account_entity_1 = require("../modules/accounts/entities/account.entity");
const wallet_entity_1 = require("../modules/wallets/entities/wallet.entity");
let AccountWalletSeeder = class AccountWalletSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const accountWalletRepository = this.dataSource.getRepository(account_wallets_entity_1.AccountWallets);
        const accountRepository = this.dataSource.getRepository(account_entity_1.Accounts);
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const account = await accountRepository.findOne({ where: { code: '123' } });
        const wallet = await walletRepository.findOne({
            where: { name: 'Pessoa Física - Menor ou igual à R$ 2.000,00 - 01' },
        });
        if (!account || !wallet) {
            return;
        }
        const accountWallet = await accountWalletRepository.findOne({
            where: { accountId: account.id },
        });
        if (accountWallet) {
            return;
        }
        await this.seedAccountWallet({
            accountId: account.id,
            walletId: wallet.id,
            createdAt: new Date('2025-02-21T15:55:04.988Z'),
            updatedAt: null,
            deletedAt: null,
        }, accountWalletRepository);
    }
    async seedAccountWallet(accountWalletData, accountWalletRepository) {
        const existing = await accountWalletRepository.findOne({
            where: {
                accountId: accountWalletData.accountId,
                walletId: accountWalletData.walletId,
            },
        });
        if (!existing) {
            const accountWallet = accountWalletRepository.create(accountWalletData);
            await accountWalletRepository.save(accountWallet);
        }
    }
};
exports.AccountWalletSeeder = AccountWalletSeeder;
exports.AccountWalletSeeder = AccountWalletSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AccountWalletSeeder);
//# sourceMappingURL=account-wallets.seeder.js.map