"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IndexSeeder = void 0;
const common_1 = require("@nestjs/common");
const permissions_seeder_1 = require("./permissions.seeder");
const profile_seeder_1 = require("./profile.seeder");
const user_seeder_1 = require("./user.seeder");
const profile_permissions_seeder_1 = require("./profile-permissions.seeder");
const permissions_strategy_seeder_1 = require("./permissions-strategy.seeder");
const profile_permissions_strategy_seeder_1 = require("./profile-permissions-strategy.seeder");
const federation_seeder_1 = require("./federation.seeder");
const central_seeder_1 = require("./central.seeder");
const cooperative_seeder_1 = require("./cooperative.seeder");
const agency_seeder_1 = require("./agency.seeder");
const wallet_range_value_seeder_1 = require("./wallet-range-value.seeder");
const wallet_seeder_1 = require("./wallet.seeder");
const keycloak_seeder_1 = require("./keycloak.seeder");
const account_type_seeder_1 = require("./account-type.seeder");
const associate_seeder_1 = require("./associate.seeder");
const account_wallets_seeder_1 = require("./account-wallets.seeder");
const account_seeder_1 = require("./account.seeder");
const associate_detail_seeder_1 = require("./associate-detail.seeder");
const card_seeder_1 = require("./card.seeder");
const card_type_seeder_1 = require("./card-type.seeder");
const associate_phone_seeder_1 = require("./associate-phone.seeder");
let IndexSeeder = class IndexSeeder {
    constructor(keycloak, permissions, permissionsStrategySeeder, profile, user, profilePermission, profilePermissionsStrategySeeder, federation, central, cooperative, agency, wallet, walletRangeValues, accountType, account, accountWallet, associate, associateDetail, cardType, card, associatePhone) {
        this.keycloak = keycloak;
        this.permissions = permissions;
        this.permissionsStrategySeeder = permissionsStrategySeeder;
        this.profile = profile;
        this.user = user;
        this.profilePermission = profilePermission;
        this.profilePermissionsStrategySeeder = profilePermissionsStrategySeeder;
        this.federation = federation;
        this.central = central;
        this.cooperative = cooperative;
        this.agency = agency;
        this.wallet = wallet;
        this.walletRangeValues = walletRangeValues;
        this.accountType = accountType;
        this.account = account;
        this.accountWallet = accountWallet;
        this.associate = associate;
        this.associateDetail = associateDetail;
        this.cardType = cardType;
        this.card = card;
        this.associatePhone = associatePhone;
    }
    async onModuleInit() {
        await this.keycloak.executeSeed();
        await this.permissions.executeSeed();
        await this.permissionsStrategySeeder.executeSeed();
        await this.profile.executeSeed();
        await this.user.executeSeed();
        await this.profilePermission.executeSeed();
        await this.profilePermissionsStrategySeeder.executeSeed();
        await this.federation.executeSeed();
        await this.central.executeSeed();
        await this.cooperative.executeSeed();
        await this.agency.executeSeed();
        await this.walletRangeValues.onModuleInit();
        await this.wallet.executeSeed();
        await this.accountType.executeSeed();
        await this.associate.executeSeed();
        await this.associatePhone.executeSeed();
        await this.account.executeSeed();
        await this.accountWallet.executeSeed();
        await this.associateDetail.executeSeed();
        await this.cardType.executeSeed();
        await this.card.executeSeed();
    }
};
exports.IndexSeeder = IndexSeeder;
exports.IndexSeeder = IndexSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [keycloak_seeder_1.KeycloakSeeder,
        permissions_seeder_1.PermissionsSeeder,
        permissions_strategy_seeder_1.PermissionsStrategySeeder,
        profile_seeder_1.ProfilesSeeder,
        user_seeder_1.UserSeeder,
        profile_permissions_seeder_1.ProfilePermissionsSeeder,
        profile_permissions_strategy_seeder_1.ProfilePermissionsStrategySeeder,
        federation_seeder_1.FederationSeeder,
        central_seeder_1.CentralSeeder,
        cooperative_seeder_1.CooperativeSeeder,
        agency_seeder_1.AgencySeeder,
        wallet_seeder_1.WalletSeeder,
        wallet_range_value_seeder_1.WalletRangeValueSeeder,
        account_type_seeder_1.AccountTypeSeeder,
        account_seeder_1.AccountSeeder,
        account_wallets_seeder_1.AccountWalletSeeder,
        associate_seeder_1.AssociateSeeder,
        associate_detail_seeder_1.AssociateDetailSeeder,
        card_type_seeder_1.CardTypeSeeder,
        card_seeder_1.CardSeeder,
        associate_phone_seeder_1.AssociatePhoneSeeder])
], IndexSeeder);
//# sourceMappingURL=index.seeder.js.map