"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletSeeder = void 0;
const common_1 = require("@nestjs/common");
const wallet_entity_1 = require("../modules/wallets/entities/wallet.entity");
const typeorm_1 = require("typeorm");
const agency_entity_1 = require("../modules/agencies/entities/agency.entity");
const segment_entity_1 = require("../modules/segments/entities/segment.entity");
const wallet_range_value_entity_1 = require("../modules/wallet-range-values/entities/wallet-range-value.entity");
let WalletSeeder = class WalletSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        await this.seedInitialWallet();
        await this.seedInactive();
        await this.seedMinorAssociate();
        await this.seedCoHolder();
        await this.seedGuarantor();
        await this.seedWage();
        await this.seedClosed();
        await this.seedPrejudice();
    }
    async getWallet(name, number, walletRepository) {
        const existing = await walletRepository.findOne({
            where: { name, number },
        });
        return existing;
    }
    async saveWallet(existing, name, number, numberOld, category, agencyCode, walletRepository, agencyRepository) {
        let agency = await agencyRepository.findOne({
            where: { agencyCode: agencyCode },
        });
        if (!existing) {
            const wallet = new wallet_entity_1.Wallet();
            wallet.name = name;
            wallet.number = number;
            wallet.numberOld = numberOld;
            wallet.category = category;
            wallet.agencyId = agency.id;
            return await walletRepository.save(wallet);
        }
        return false;
    }
    async seedInitialWallet() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const segmentRepository = this.dataSource.getRepository(segment_entity_1.Segment);
        const walletRangeRepository = this.dataSource.getRepository(wallet_range_value_entity_1.WalletRangeValue);
        let segment = await segmentRepository.findOne({
            where: { name: 'Pessoa Física' },
        });
        let agency = await agencyRepository.findOne({
            where: { agencyCode: '0001' },
        });
        let walletRange = await walletRangeRepository.findOne({
            where: { agencyId: agency.id, name: 'Menor ou igual à R$ 2.000,00' },
        });
        const existingCreate = await this.getWallet('Menor ou igual à R$ 2.000,00', '01.01.01', walletRepository);
        if (!existingCreate) {
            const wallet = new wallet_entity_1.Wallet();
            wallet.name = 'Menor ou igual à R$ 2.000,00';
            wallet.number = '111';
            wallet.numberOld = '01.01.01';
            wallet.category = 'NORMAL';
            wallet.agencyId = agency.id;
            wallet.segmentId = segment.id;
            wallet.walletRangeId = walletRange.id;
            return await walletRepository.save(wallet);
        }
    }
    async seedInactive() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('INATIVOS', '01.04.07', walletRepository);
        await this.saveWallet(existingCreate, 'INATIVOS', '01.04.07', '147', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedMinorAssociate() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('MENOR ASSOCIADO', '01.07.08', walletRepository);
        await this.saveWallet(existingCreate, 'MENOR ASSOCIADO', '01.07.08', '178', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedCoHolder() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('CO-TITULAR', '01.08.08', walletRepository);
        await this.saveWallet(existingCreate, 'CO-TITULAR', '01.08.08', '188', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedGuarantor() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('AVALISTA', '05.03.01', walletRepository);
        await this.saveWallet(existingCreate, 'AVALISTA', '05.03.01', '531', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedSavings() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('POUPANÇA', '05.03.02', walletRepository);
        await this.saveWallet(existingCreate, 'POUPANÇA', '05.03.02', '532', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedWage() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('SALÁRIO', '05.03.03', walletRepository);
        await this.saveWallet(existingCreate, 'SALÁRIO', '05.03.03', '533', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedClosed() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('ENCERRADA', '05.03.04', walletRepository);
        await this.saveWallet(existingCreate, 'ENCERRADA', '05.03.04', '534', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
    async seedPrejudice() {
        const walletRepository = this.dataSource.getRepository(wallet_entity_1.Wallet);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const existingCreate = await this.getWallet('PREJUÍZO', '05.03.05', walletRepository);
        await this.saveWallet(existingCreate, 'PREJUÍZO', '05.03.05', '535', 'OTHERS', '0001', walletRepository, agencyRepository);
    }
};
exports.WalletSeeder = WalletSeeder;
exports.WalletSeeder = WalletSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], WalletSeeder);
//# sourceMappingURL=wallet.seeder.js.map