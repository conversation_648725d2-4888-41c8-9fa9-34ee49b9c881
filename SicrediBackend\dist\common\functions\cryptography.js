"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Cryptography = void 0;
const common_1 = require("@nestjs/common");
const crypto_1 = require("crypto");
let Cryptography = class Cryptography {
    cipher() {
        const password = process.env.CRYPTO_PASSWORD;
        const ivSize = Number(process.env.CRYPTO_IV_SIZE);
        const algorithm = process.env.CRYPTO_ALGORITHM;
        const key = (0, crypto_1.scryptSync)(password, 'salt', 32);
        const iv = Buffer.alloc(ivSize, 0);
        return {
            key,
            iv,
            algorithm,
        };
    }
    encrypt(textToEncrypt) {
        if (typeof textToEncrypt !== 'string') {
            return textToEncrypt;
        }
        const { key, iv, algorithm } = this.cipher();
        const cipher = (0, crypto_1.createCipheriv)(algorithm, key, iv);
        let cipherText = cipher.update(textToEncrypt, 'utf8', 'hex');
        cipherText += cipher.final('hex');
        return cipherText;
    }
    decrypt(textToDecrypt) {
        if (typeof textToDecrypt !== 'string') {
            return textToDecrypt;
        }
        const { key, iv, algorithm } = this.cipher();
        const decipher = (0, crypto_1.createDecipheriv)(algorithm, key, iv);
        let decryptedText = decipher.update(textToDecrypt, 'hex', 'utf8');
        decryptedText += decipher.final('utf8');
        return decryptedText;
    }
};
exports.Cryptography = Cryptography;
exports.Cryptography = Cryptography = __decorate([
    (0, common_1.Injectable)()
], Cryptography);
//# sourceMappingURL=cryptography.js.map