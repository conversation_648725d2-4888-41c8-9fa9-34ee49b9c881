"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountTypeModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cryptography_1 = require("../../common/functions/cryptography");
const associates_module_1 = require("../associates/associates.module");
const agencies_module_1 = require("../agencies/agencies.module");
const account_types_controller_1 = require("./account-types.controller");
const account_types_service_1 = require("./account-types.service");
const account_type_entity_1 = require("./entities/account-type.entity");
const account_entity_1 = require("../accounts/entities/account.entity");
let AccountTypeModule = class AccountTypeModule {
};
exports.AccountTypeModule = AccountTypeModule;
exports.AccountTypeModule = AccountTypeModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([account_type_entity_1.AccountType, account_entity_1.Accounts]),
            (0, common_1.forwardRef)(() => associates_module_1.AssociatesModule),
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule)
        ],
        controllers: [account_types_controller_1.AccountTypeController],
        providers: [account_types_service_1.AccountTypeService, cryptography_1.Cryptography],
        exports: [account_types_service_1.AccountTypeService, typeorm_1.TypeOrmModule],
    })
], AccountTypeModule);
//# sourceMappingURL=account-types.module.js.map