import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthResponseDto } from './dto/auth.dto';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { jwtDecode } from 'jwt-decode';
import { UsersService } from './../modules/users/users.service';
import { DataSource } from 'typeorm';
import { Attendance } from 'src/modules/attendances/entities/attendance.entity';
import { endOfDay, startOfDay } from 'date-fns';

@Injectable()
export class AuthService {
  private readonly keycloakUrl: string;
  private readonly keycloakRealm: string;
  private readonly keycloakClientId: string;
  private readonly keycloakClientSecret: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly http: HttpService,
    private readonly usersService: UsersService,
    private readonly dataSource: DataSource,
  ) {
    this.keycloakUrl = this.configService.get<string>('KEYCLOAK_URL');
    this.keycloakRealm = this.configService.get<string>('KEYCLOAK_REALM');
    this.keycloakClientId =
      this.configService.get<string>('KEYCLOAK_CLIENT_ID');
    this.keycloakClientSecret = this.configService.get<string>(
      'KEYCLOAK_CLIENT_SECRET',
    );
  }

  async signIn(login: string, password: string): Promise<AuthResponseDto> {
    try {
      const { data, status } = await firstValueFrom(
        this.http.post(
          `${this.keycloakUrl}/realms/${this.keycloakRealm}/protocol/openid-connect/token`,
          new URLSearchParams({
            client_id: this.keycloakClientId,
            client_secret: this.keycloakClientSecret,
            grant_type: 'password',
            username: login,
            password,
          }),
        ),
      );

      if (status != 200) {
        throw new UnauthorizedException('Invalid credentials');
      }

      if (!data.access_token) {
        throw new UnauthorizedException('Invalid Access Token');
      }

      const decodedToken = jwtDecode(data.access_token);
      if (!decodedToken?.sub) {
        throw new UnauthorizedException('Invalid Access Token');
      }

      try {
        const user = await this.usersService.findByIdKeyCloeker({
          idUserKeycloak: decodedToken.sub,
        });
        if (
          user.profileKey === 'WALLET_MANAGER' ||
          user.profileKey === 'ASSISTANT'
        ) {
          const queryRunner = this.dataSource.createQueryRunner();
          const todayStart = startOfDay(new Date());
          const todayEnd = endOfDay(new Date());

          const attendanceCount = await queryRunner.manager
            .getRepository(Attendance)
            .createQueryBuilder('attendance')
            .where('attendance.attendant_id = :id', { id: user.id })
            .andWhere('attendance.created_at BETWEEN :start AND :end', {
              start: todayStart,
              end: todayEnd,
            })
            .getCount();

          data.user = {
            ...user,
            hasAttendedToday: attendanceCount > 0,
          };
        } else {
          data.user = user;
        }
      } catch (error) {
        console.log(error);
      }

      return data;
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  async refreshAccessToken(refreshToken: string): Promise<string> {
    try {
      const { data, status } = await firstValueFrom(
        this.http.post(
          `${this.keycloakUrl}/realms/${this.keycloakRealm}/protocol/openid-connect/token`,
          new URLSearchParams({
            client_id: this.keycloakClientId,
            client_secret: this.keycloakClientSecret,
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
          }),
        ),
      );
      if (status != 200) {
        throw new UnauthorizedException('Invalid credentials');
      }
      return data;
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }

  async validateToken(token: string): Promise<any> {
    try {
      const { data, status } = await firstValueFrom(
        this.http.get(
          `${this.keycloakUrl}/realms/${this.keycloakRealm}/protocol/openid-connect/userinfo`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          },
        ),
      );
      if (status !== 200) {
        throw new UnauthorizedException('Invalid token');
      }
      return data;
    } catch (e) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  async findByToken(token: string): Promise<any> {
    try {
      const decodedToken = jwtDecode(token);
      if (!decodedToken?.sub) {
        throw new UnauthorizedException('Invalid Access Token');
      }

      const user = await this.usersService.findByIdKeyCloeker({
        idUserKeycloak: decodedToken.sub,
      });
      return user;
    } catch (e) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
