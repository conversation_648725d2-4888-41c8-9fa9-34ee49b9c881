"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FederationSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const federation_entity_1 = require("../modules/federations/entities/federation.entity");
let FederationSeeder = class FederationSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedFederation();
    }
    async getFederation(name, address, federationRepository) {
        return await federationRepository.findOne({
            where: { name, address },
        });
    }
    async saveFederation(existing, federationRepository) {
        if (!existing) {
            const federation = new federation_entity_1.Federation();
            federation.name = 'Federação Exemplo';
            federation.address = 'Rua Exemplo, 789 - Centro';
            federation.createdAt = new Date();
            return await federationRepository.save(federation);
        }
        return false;
    }
    async executeSeed() {
        await this.seedFederation();
    }
    async seedFederation() {
        const federationRepository = this.dataSource.getRepository(federation_entity_1.Federation);
        const existingFederation = await this.getFederation('Federação Exemplo', 'Rua Exemplo, 789 - Centro', federationRepository);
        await this.saveFederation(existingFederation, federationRepository);
    }
};
exports.FederationSeeder = FederationSeeder;
exports.FederationSeeder = FederationSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], FederationSeeder);
//# sourceMappingURL=federation.seeder.js.map