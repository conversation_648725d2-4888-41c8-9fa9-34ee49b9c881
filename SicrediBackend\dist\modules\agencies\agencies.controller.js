"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgenciesController = void 0;
const common_1 = require("@nestjs/common");
const agencies_service_1 = require("./agencies.service");
const create_agency_dto_1 = require("./dto/create-agency.dto");
const update_agency_dto_1 = require("./dto/update-agency.dto");
const swagger_1 = require("@nestjs/swagger");
const search_agency_dto_1 = require("./dto/search-agency.dto");
const paginated_agency_dto_1 = require("./dto/paginated-agency.dto");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
const user_decorator_1 = require("../../common/decorators/user.decorator");
const noPermission_decorator_1 = require("../../common/decorators/noPermission.decorator");
const agency_dto_1 = require("./dto/agency.dto");
let AgenciesController = class AgenciesController {
    constructor(agenciesService) {
        this.agenciesService = agenciesService;
    }
    create(createAgencyDto) {
        return this.agenciesService.create(createAgencyDto);
    }
    findAll(user) {
        return this.agenciesService.findAll(user);
    }
    findPaginatedAgencies(paginationParams, user) {
        return this.agenciesService.findPaginatedAgencies(paginationParams, user);
    }
    update(id, updateAgencyDto) {
        return this.agenciesService.update(+id, updateAgencyDto);
    }
    remove(id) {
        return this.agenciesService.remove(+id);
    }
    findAllByIdCooperative(id) {
        return this.agenciesService.findAllByIdCooperative(+id);
    }
    async getAllAgencyCodes() {
        return this.agenciesService.getAllAgencyCodes();
    }
    findOne(id, user) {
        return this.agenciesService.findOneUser(+id, user);
    }
    async createBulk(agencyDto) {
        if (!agencyDto || agencyDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.agenciesService.createAgencyFromBulk(agencyDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(agencyCodes) {
        return this.agenciesService.deleteAgenciesFromBulk(agencyCodes);
    }
};
exports.AgenciesController = AgenciesController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova Agência' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Agência criada com sucesso.',
        type: create_agency_dto_1.CreateAgencyDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_agency_dto_1.CreateAgencyDto]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Agências' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Agências encontrada com sucesso.',
        type: search_agency_dto_1.SearchAgencyDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Agência encontrada.' }),
    (0, common_1.Get)(),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, noPermission_decorator_1.NoPermission)(),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Agências com paginação e filtro' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Agências encontrada com sucesso.',
        schema: {
            example: {
                items: [
                    {
                        id: 1,
                        name: 'Agência Exemplo',
                        agencyCode: '1234',
                        address: 'Endereço da Agência',
                        cooperative: { id: 1, name: 'Cooperativa Exemplo' },
                        central: { id: 1, name: 'Central Exemplo' },
                        federation: { id: 1, name: 'Federação Exemplo' },
                    },
                ],
                totalItems: 100,
                totalPages: 10,
                currentPage: 1,
            },
        },
    }),
    (0, common_1.Get)('paginated'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [paginated_agency_dto_1.PaginatedAgencyDto, Object]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "findPaginatedAgencies", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Agência' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Agência atualizada com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agência não encontrada.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_agency_dto_1.UpdateAgencyDto]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Agência' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Agência removida com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agência não encontrada.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, noPermission_decorator_1.NoPermission)(),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Agências pelo id cooperativa' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Agências encontrada com sucesso.',
        type: [search_agency_dto_1.SearchAgencyDto],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Agência encontrada.' }),
    (0, common_1.Get)('get-by-cooperative/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "findAllByIdCooperative", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar todos os códigos de agência' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de códigos de agências retornada com sucesso.',
        schema: {
            example: ['1234', '5678', '9101'],
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma agência encontrada.' }),
    (0, common_1.Get)('get-codes'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AgenciesController.prototype, "getAllAgencyCodes", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/agencies'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Agência pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Agência encontrada com sucesso.',
        type: create_agency_dto_1.CreateAgencyDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Agência não encontrada.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", void 0)
], AgenciesController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplas agências em massa',
        description: `
      Este endpoint permite criar novas agências ou atualizar agências já existentes em massa.
      Se uma agência já existir (com base no código da agência), seus dados serão atualizados com as informações enviadas.
      O número máximo permitido de agências por requisição é 20.000, sendo processadas em lotes de 500.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [agency_dto_1.AgencyDto],
        description: 'Array de objetos contendo os dados das agências a serem criadas ou atualizadas.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar agências',
                value: [
                    {
                        name: 'Agência Central',
                        agency_code: '1234',
                        cooperative_code: 'CP01',
                        address: 'Avenida Principal, 100',
                    },
                    {
                        name: 'Agência Sul',
                        agency_code: '5678',
                        cooperative_code: 'CP02',
                        address: 'Rua Secundária, 200',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Agências criadas ou atualizadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Agencies processed successfully',
                processedAgencies: [
                    {
                        id: 1,
                        name: 'Agência Central',
                        agency_code: '1234',
                        cooperative_code: 'CP01',
                        address: 'Avenida Principal, 100',
                        updated: true,
                    },
                    {
                        id: 2,
                        name: 'Agência Sul',
                        agency_code: '5678',
                        cooperative_code: 'CP02',
                        address: 'Rua Secundária, 200',
                        updated: false,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: name, agency_code, cooperative_code, address',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas agências foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedAgencies: [
                    {
                        id: 3,
                        name: 'Agência Oeste',
                        agency_code: '9999',
                        cooperative_code: 'CP01',
                        address: 'Rua Central, 789',
                        updated: false,
                    },
                ],
                errors: [
                    {
                        agency: {
                            name: 'Agência X',
                            agency_code: '1234',
                            cooperative_code: 'CP02',
                            address: 'Rua Y',
                        },
                        status: 'error',
                        message: 'Cooperative not found for cooperative_code: CP01',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AgenciesController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Deletar múltiplas agências em massa',
        description: `
      Este endpoint permite excluir várias agências ao mesmo tempo.
      Apenas um soft delete será realizado, mantendo os registros no banco.
      O limite é de 20 agências por requisição.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [String],
        description: 'Array de códigos das agências a serem deletadas',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar múltiplas agências',
                value: ['AG001', 'AG002', 'AG003'],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Agências deletadas com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Agencies deleted successfully',
                processedAgencies: [
                    {
                        id: 1,
                        name: 'Agência Central',
                        agency_code: 'AG001',
                        deleted_at: '2024-02-26T14:00:00Z',
                    },
                ],
                errors: [],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Nenhum código enviado ou número de registros excede o limite.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Algumas agências foram processadas com sucesso, mas outras tiveram erros.',
        schema: {
            example: {
                processedAgencies: [
                    {
                        id: 2,
                        name: 'Agência Sul',
                        agency_code: 'AG002',
                        deleted_at: '2024-02-26T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        agency_code: 'AG003',
                        status: 'error',
                        message: 'Agency not found',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AgenciesController.prototype, "deleteBulk", null);
exports.AgenciesController = AgenciesController = __decorate([
    (0, permission_decorator_1.Permission)('agencies'),
    (0, common_1.Controller)('/api/v1/agencies'),
    __metadata("design:paramtypes", [agencies_service_1.AgenciesService])
], AgenciesController);
//# sourceMappingURL=agencies.controller.js.map