"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateAgencyAccountsController = void 0;
const common_1 = require("@nestjs/common");
const associate_agency_accounts_service_1 = require("./associate-agency-accounts.service");
const create_associate_agency_account_dto_1 = require("./dto/create-associate-agency-account.dto");
const update_associate_agency_account_dto_1 = require("./dto/update-associate-agency-account.dto");
const swagger_1 = require("@nestjs/swagger");
let AssociateAgencyAccountsController = class AssociateAgencyAccountsController {
    constructor(associateAgencyAccountsService) {
        this.associateAgencyAccountsService = associateAgencyAccountsService;
    }
    create(createAssociateAgencyAccountDto) {
        return this.associateAgencyAccountsService.create(createAssociateAgencyAccountDto);
    }
    findAll() {
        return this.associateAgencyAccountsService.findAll();
    }
    findOne(id) {
        return this.associateAgencyAccountsService.findOne(+id);
    }
    update(id, updateAssociateAgencyAccountDto) {
        return this.associateAgencyAccountsService.update(+id, updateAssociateAgencyAccountDto);
    }
    remove(id) {
        return this.associateAgencyAccountsService.remove(+id);
    }
};
exports.AssociateAgencyAccountsController = AssociateAgencyAccountsController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-agency-accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar nova Conta de Agência Associada' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Conta de Agência Associada criada com sucesso.',
        type: create_associate_agency_account_dto_1.CreateAssociateAgencyAccountDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_associate_agency_account_dto_1.CreateAssociateAgencyAccountDto]),
    __metadata("design:returntype", void 0)
], AssociateAgencyAccountsController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-agency-accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Contas de Agência Associada' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Contas de Agência Associada encontrada com sucesso.',
        type: create_associate_agency_account_dto_1.CreateAssociateAgencyAccountDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Nenhuma Conta de Agência Associada encontrada.',
    }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AssociateAgencyAccountsController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-agency-accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Conta de Agência Associada pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta de Agência Associada encontrada com sucesso.',
        type: create_associate_agency_account_dto_1.CreateAssociateAgencyAccountDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conta de Agência Associada não encontrada.',
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssociateAgencyAccountsController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-agency-accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Conta de Agência Associada' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta de Agência Associada atualizada com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conta de Agência Associada não encontrada.',
    }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_associate_agency_account_dto_1.UpdateAssociateAgencyAccountDto]),
    __metadata("design:returntype", void 0)
], AssociateAgencyAccountsController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-agency-accounts'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Conta de Agência Associada' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Conta de Agência Associada removida com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Conta de Agência Associada não encontrada.',
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssociateAgencyAccountsController.prototype, "remove", null);
exports.AssociateAgencyAccountsController = AssociateAgencyAccountsController = __decorate([
    (0, common_1.Controller)('/api/v1/associate-agency-accounts'),
    __metadata("design:paramtypes", [associate_agency_accounts_service_1.AssociateAgencyAccountsService])
], AssociateAgencyAccountsController);
//# sourceMappingURL=associate-agency-accounts.controller.js.map