"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociatePhoneController = void 0;
const common_1 = require("@nestjs/common");
const associate_phone_service_1 = require("./associate-phone.service");
const create_associate_phone_dto_1 = require("./dto/create-associate-phone.dto");
const swagger_1 = require("@nestjs/swagger");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
let AssociatePhoneController = class AssociatePhoneController {
    constructor(associatePhoneService) {
        this.associatePhoneService = associatePhoneService;
    }
    create(createAssociatePhoneDto) {
        try {
            return this.associatePhoneService.create(createAssociatePhoneDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    createMany(createAssociatePhoneDto) {
        try {
            return this.associatePhoneService.createMany(createAssociatePhoneDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
    findAllByAssociate(id) {
        try {
            return this.associatePhoneService.findAllByAssociate(+id);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException(error.message);
        }
    }
};
exports.AssociatePhoneController = AssociatePhoneController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/asociate-phone'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar um novo telefone para um associado' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Telefone criado com sucesso', type: create_associate_phone_dto_1.CreateAssociatePhoneDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Erro de validação.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_associate_phone_dto_1.CreateAssociatePhoneDto]),
    __metadata("design:returntype", void 0)
], AssociatePhoneController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/asociate-phone'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar vários telefones para um associado' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Telefones criados com sucesso', type: create_associate_phone_dto_1.CreateAssociatePhoneDto, isArray: true }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Erro de validação.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)('create-many'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", void 0)
], AssociatePhoneController.prototype, "createMany", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/asociate-phone'),
    (0, swagger_1.ApiOperation)({ summary: 'Listar todos os telefones de um associado' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Telefones listados com sucesso', type: create_associate_phone_dto_1.CreateAssociatePhoneDto, isArray: true }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Erro de validação.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Get)('associate/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssociatePhoneController.prototype, "findAllByAssociate", null);
exports.AssociatePhoneController = AssociatePhoneController = __decorate([
    (0, permission_decorator_1.Permission)('associates'),
    (0, common_1.Controller)('/api/v1/associate-phone'),
    __metadata("design:paramtypes", [associate_phone_service_1.AssociatePhoneService])
], AssociatePhoneController);
//# sourceMappingURL=associate-phone.controller.js.map