"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateAgencyAccountsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const associate_agency_account_entity_1 = require("./entities/associate-agency-account.entity");
const cryptography_1 = require("../../common/functions/cryptography");
let AssociateAgencyAccountsService = class AssociateAgencyAccountsService {
    constructor(associateAgencyAccountRepository, cryptography) {
        this.associateAgencyAccountRepository = associateAgencyAccountRepository;
        this.cryptography = cryptography;
    }
    async create(createAssociateAgencyAccountDto) {
        const newData = new associate_agency_account_entity_1.AssociateAgencyAccount();
        newData.associateId = createAssociateAgencyAccountDto.associateId;
        newData.agencyId = createAssociateAgencyAccountDto.agencyId;
        newData.accountNumber = this.cryptography.encrypt(String(createAssociateAgencyAccountDto.accountNumber));
        const response = await this.associateAgencyAccountRepository.save(newData);
        return {
            id: response.id,
            associateId: response.associateId,
            agencyId: response.agencyId,
            accountNumber: this.cryptography.decrypt(response.accountNumber),
        };
    }
    async findAll() {
        const data = await this.associateAgencyAccountRepository
            .createQueryBuilder('associateAgencyAccount')
            .select([
            'associateAgencyAccount.id as id',
            'associateAgencyAccount.associate_id as associateId',
            'associateAgencyAccount.agency_id as agencyId',
            'associateAgencyAccount.account_number as accountNumber',
        ])
            .where('associateAgencyAccount.deleted_at IS NULL')
            .execute();
        data.forEach(async (value) => {
            value.accountNumber = this.cryptography.decrypt(value.accountNumber);
        });
        return data;
    }
    async findOne(id) {
        const data = await this.associateAgencyAccountRepository.findOneBy({
            id,
            deletedAt: (0, typeorm_2.IsNull)(),
        });
        if (!data) {
            throw new common_1.NotFoundException(`Associate Agency Account with ID ${id} not found`);
        }
        return {
            ...data,
            accountNumber: this.cryptography.decrypt(data.accountNumber),
        };
    }
    async update(id, updateData) {
        await this.findOne(id);
        const data = {
            associateId: updateData.associateId,
            agencyId: updateData.agencyId,
            accountNumber: this.cryptography.encrypt(updateData.accountNumber),
            updatedAt: new Date(),
        };
        await this.associateAgencyAccountRepository.update(id, data);
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            associateId: updated.associateId,
            agencyId: updated.agencyId,
            accountNumber: updated.accountNumber,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.associateAgencyAccountRepository.update(id, {
            deletedAt: new Date(),
        });
    }
};
exports.AssociateAgencyAccountsService = AssociateAgencyAccountsService;
exports.AssociateAgencyAccountsService = AssociateAgencyAccountsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(associate_agency_account_entity_1.AssociateAgencyAccount)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        cryptography_1.Cryptography])
], AssociateAgencyAccountsService);
//# sourceMappingURL=associate-agency-accounts.service.js.map