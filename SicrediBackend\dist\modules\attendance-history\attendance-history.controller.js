"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceHistoryController = void 0;
const common_1 = require("@nestjs/common");
const attendance_history_service_1 = require("./attendance-history.service");
const create_attendance_history_dto_1 = require("./dto/create-attendance-history.dto");
const update_attendance_history_dto_1 = require("./dto/update-attendance-history.dto");
const swagger_1 = require("@nestjs/swagger");
const response_attendance_dto_1 = require("./dto/response-attendance.dto");
let AttendanceHistoryController = class AttendanceHistoryController {
    constructor(attendanceHistoryService) {
        this.attendanceHistoryService = attendanceHistoryService;
    }
    create(createAttendanceHistoryDto) {
        return this.attendanceHistoryService.create(createAttendanceHistoryDto);
    }
    async findAllByAssociate(id) {
        return await this.attendanceHistoryService.findAllByAssociate(id);
    }
    findOne(id) {
        return this.attendanceHistoryService.findOne(+id);
    }
    update(id, updateAttendanceHistoryDto) {
        return this.attendanceHistoryService.update(+id, updateAttendanceHistoryDto);
    }
    remove(id) {
        return this.attendanceHistoryService.remove(+id);
    }
};
exports.AttendanceHistoryController = AttendanceHistoryController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo Histórico de Atendimento' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Histórico de Atendimento criado com sucesso.',
        type: create_attendance_history_dto_1.CreateAttendanceHistoryDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_attendance_history_dto_1.CreateAttendanceHistoryDto]),
    __metadata("design:returntype", void 0)
], AttendanceHistoryController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-history'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar lista de Histórico de Atendimento por Associado',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Histórico de Atendimento encontrada com sucesso.',
        type: response_attendance_dto_1.ResponseAttendanceDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Nenhuma Histórico de Atendimento encontrado.',
    }),
    (0, common_1.Get)('by-associate/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AttendanceHistoryController.prototype, "findAllByAssociate", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Histórico de Atendimento pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Histórico de Atendimento encontrado com sucesso.',
        type: create_attendance_history_dto_1.CreateAttendanceHistoryDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Histórico de Atendimento não encontrado.',
    }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AttendanceHistoryController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Histórico de Atendimento' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Histórico de Atendimento atualizado com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Histórico de Atendimento não encontrado.',
        type: update_attendance_history_dto_1.UpdateAttendanceHistoryDto,
    }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_attendance_history_dto_1.UpdateAttendanceHistoryDto]),
    __metadata("design:returntype", void 0)
], AttendanceHistoryController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/attendance-history'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Histórico de Atendimento' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Histórico de Atendimento removido com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Histórico de Atendimento não encontrado.',
    }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AttendanceHistoryController.prototype, "remove", null);
exports.AttendanceHistoryController = AttendanceHistoryController = __decorate([
    (0, common_1.Controller)('api/v1/attendance-history'),
    __metadata("design:paramtypes", [attendance_history_service_1.AttendanceHistoryService])
], AttendanceHistoryController);
//# sourceMappingURL=attendance-history.controller.js.map