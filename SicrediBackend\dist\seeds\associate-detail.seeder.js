"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateDetailSeeder = void 0;
const common_1 = require("@nestjs/common");
const associate_details_entity_1 = require("../modules/associate-details/entities/associate-details.entity");
const associate_entity_1 = require("../modules/associates/entities/associate.entity");
const typeorm_1 = require("typeorm");
let AssociateDetailSeeder = class AssociateDetailSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const associateDetailRepository = this.dataSource.getRepository(associate_details_entity_1.AssociateDetails);
        const associateRepository = this.dataSource.getRepository(associate_entity_1.Associate);
        const associate = await associateRepository.findOne({
            where: { name: 'Arthur Abreu' },
        });
        await this.seedAssociateDetail({
            associateId: associate.id,
            isRestrict: false,
            isDebtor: false,
            lcaValue: 1000,
            depositValue: 1000,
            missingInstallments: 10,
            hasSubscription: false,
            termDepositExpiration: new Date("2025-05-25T15:55:04.967Z"),
            lcaExpiration: new Date("2025-05-25T15:55:04.967Z"),
            lastRegistrationUpdate: new Date("2025-01-15T15:55:04.967Z"),
            channelAccess: new Date("2025-02-23T15:55:04.967Z"),
            hasFinancialFlow: false,
            createdAt: new Date("2025-03-12T09:40:04.967Z"),
            updatedAt: null,
            deletedAt: null,
        }, associateDetailRepository);
    }
    async seedAssociateDetail(associateDetailData, associateDetailRepository) {
        const existing = await associateDetailRepository.findOne({ where: { associateId: associateDetailData.associateId } });
        if (!existing) {
            const associateDetails = associateDetailRepository.create(associateDetailData);
            await associateDetailRepository.save(associateDetails);
        }
    }
};
exports.AssociateDetailSeeder = AssociateDetailSeeder;
exports.AssociateDetailSeeder = AssociateDetailSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AssociateDetailSeeder);
//# sourceMappingURL=associate-detail.seeder.js.map