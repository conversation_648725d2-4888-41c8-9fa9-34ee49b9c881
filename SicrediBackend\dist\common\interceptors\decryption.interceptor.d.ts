import { NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { PayloadCryptography } from '../functions/payload-cryptography';
export declare class DecryptionInterceptor implements NestInterceptor {
    private readonly cryptography;
    constructor(cryptography: PayloadCryptography);
    private readonly EXCLUDED_ENDPOINTS;
    intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>>;
}
