"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DecryptionInterceptor = void 0;
const common_1 = require("@nestjs/common");
const payload_cryptography_1 = require("../functions/payload-cryptography");
let DecryptionInterceptor = class DecryptionInterceptor {
    constructor(cryptography) {
        this.cryptography = cryptography;
        this.EXCLUDED_ENDPOINTS = [
            '/associates/bulk',
            '/associates/delete/bulk',
            'users/bulk',
            '/wallets/bulk',
            '/wallets/delete/bulk',
            '/wallet-range-values/bulk',
            '/wallet-range-values/delete/bulk',
            '/agencies/bulk',
            '/agencies/delete/bulk',
            '/centrals/bulk',
            '/centrals/delete/bulk',
            '/federations/bulk',
            '/federations/delete/bulk',
            '/products/bulk',
            '/products/delete/bulk',
            '/profiles/bulk',
            '/profiles/delete/bulk',
            '/segments/bulk',
            '/segments/delete/bulk',
            '/accounts/bulk',
            '/accounts/delete/bulk',
            '/account-wallets/bulk',
            '/account-wallets/delete/bulk',
            'cooperatives/bulk',
            'cooperatives/delete/bulk',
            'goal/bulk',
            'attendance-products-effective/bulk',
            'account-types/bulk',
            'associate-details/bulk',
            'cards/bulk',
            'attendance-products-effective',
        ];
    }
    async intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        if (['GET', 'DELETE'].includes(request.method))
            return next.handle();
        if (this.EXCLUDED_ENDPOINTS.some((endpoint) => request.url.includes(endpoint))) {
            return next.handle();
        }
        const body = request.body;
        if (!body?.payload) {
            throw new common_1.BadRequestException();
        }
        const decrypted = this.cryptography.decrypt(body.payload);
        const dataDecrypted = JSON.parse(decrypted);
        request.body = dataDecrypted;
        return next.handle();
    }
};
exports.DecryptionInterceptor = DecryptionInterceptor;
exports.DecryptionInterceptor = DecryptionInterceptor = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [payload_cryptography_1.PayloadCryptography])
], DecryptionInterceptor);
//# sourceMappingURL=decryption.interceptor.js.map