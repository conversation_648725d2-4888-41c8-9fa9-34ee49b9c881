"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsStrategySeeder = void 0;
const common_1 = require("@nestjs/common");
const permission_entity_1 = require("../modules/permissions/entities/permission.entity");
const typeorm_1 = require("typeorm");
let PermissionsStrategySeeder = class PermissionsStrategySeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const permissionRepository = this.dataSource.getRepository(permission_entity_1.Permission);
        await this.seedPermission('STRATEGY_CREATE', 'Criar Estratégias', 'Permissão para criar Estratégias', permissionRepository);
        await this.seedPermission('STRATEGY_UPDATE', 'Atualizar Estratégias', 'Permissão para atualizar Estratégias', permissionRepository);
        await this.seedPermission('STRATEGY_VIEW', 'Visualizar Estratégias', 'Permissão para visualizar Estratégias', permissionRepository);
        await this.seedPermission('STRATEGY_DELETE', 'Excluir Estratégias', 'Permissão para excluir Estratégias', permissionRepository);
    }
    async seedPermission(key, name, description, permissionRepository) {
        const existing = await permissionRepository.findOne({ where: { key } });
        if (!existing) {
            const permission = new permission_entity_1.Permission();
            permission.key = key;
            permission.name = name;
            permission.description = description;
            await permissionRepository.save(permission);
        }
    }
};
exports.PermissionsStrategySeeder = PermissionsStrategySeeder;
exports.PermissionsStrategySeeder = PermissionsStrategySeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], PermissionsStrategySeeder);
//# sourceMappingURL=permissions-strategy.seeder.js.map