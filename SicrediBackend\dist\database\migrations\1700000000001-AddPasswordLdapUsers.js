"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddPasswordLdapUsers1700000000001 = void 0;
class AddPasswordLdapUsers1700000000001 {
    constructor() {
        this.name = 'AddPasswordLdapUsers1700000000001';
    }
    async up(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "user" 
      ADD COLUMN "password" VARCHAR(500) NULL
    `);
        await queryRunner.query(`
      ALTER TABLE "user" 
      ADD COLUMN "ldap" VARCHAR(300) NULL
    `);
        console.log('✅ Campos password e ldap adicionados à tabela user');
    }
    async down(queryRunner) {
        await queryRunner.query(`
      ALTER TABLE "user" 
      DROP COLUMN "ldap"
    `);
        await queryRunner.query(`
      ALTER TABLE "user" 
      DROP COLUMN "password"
    `);
        console.log('❌ Campos password e ldap removidos da tabela user');
    }
}
exports.AddPasswordLdapUsers1700000000001 = AddPasswordLdapUsers1700000000001;
//# sourceMappingURL=1700000000001-AddPasswordLdapUsers.js.map