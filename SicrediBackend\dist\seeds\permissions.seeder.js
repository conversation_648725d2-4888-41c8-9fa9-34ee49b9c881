"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionsSeeder = void 0;
const common_1 = require("@nestjs/common");
const permission_entity_1 = require("../modules/permissions/entities/permission.entity");
const typeorm_1 = require("typeorm");
let PermissionsSeeder = class PermissionsSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const permissionRepository = this.dataSource.getRepository(permission_entity_1.Permission);
        await this.seedPermission('USERS_CREATE', 'Criar Usuários', 'Permissão para criar usuários', permissionRepository);
        await this.seedPermission('USERS_UPDATE', 'Atualizar Usuários', 'Permissão para atualizar usuários', permissionRepository);
        await this.seedPermission('USERS_VIEW', 'Visualizar Usuários', 'Permissão para visualizar usuários', permissionRepository);
        await this.seedPermission('USERS_DELETE', 'Excluir Usuários', 'Permissão para excluir usuários', permissionRepository);
        await this.seedPermission('PROFILES_CREATE', 'Criar Perfis', 'Permissão para criar perfis', permissionRepository);
        await this.seedPermission('PROFILES_VIEW', 'Visualizar Perfis', 'Permissão para visualizar perfis', permissionRepository);
        await this.seedPermission('PROFILES_UPDATE', 'Atualizar Perfis', 'Permissão para atualizar perfis', permissionRepository);
        await this.seedPermission('PROFILES_DELETE', 'Excluir Perfis', 'Permissão para excluir perfis', permissionRepository);
        await this.seedPermission('WALLETS_VIEW', 'Visualizar Carteiras', 'Permissão para visualizar carteiras', permissionRepository);
        await this.seedPermission('WALLETS_CREATE', 'Criar Carteiras', 'Permissão para criar carteiras', permissionRepository);
        await this.seedPermission('WALLETS_DELETE', 'Excluir Carteiras', 'Permissão para excluir carteiras', permissionRepository);
        await this.seedPermission('WALLETS_UPDATE', 'Atualizar Carteiras', 'Permissão para atualizar carteiras', permissionRepository);
        await this.seedPermission('FEDERATIONS_VIEW', 'Ver Federações', 'Permissão para visualizar federações', permissionRepository);
        await this.seedPermission('FEDERATIONS_CREATE', 'Criar Federações', 'Permissão para cadastrar federações', permissionRepository);
        await this.seedPermission('FEDERATIONS_UPDATE', 'Atualizar Federações', 'Permissão para atualizar federações', permissionRepository);
        await this.seedPermission('FEDERATIONS_DELETE', 'Excluir Federações', 'Permissão para excluir federações', permissionRepository);
        await this.seedPermission('CENTRALS_VIEW', 'Ver Centrais', 'Permissão para visualizar centrais', permissionRepository);
        await this.seedPermission('CENTRALS_CREATE', 'Criar Centrais', 'Permissão para cadastrar centrais', permissionRepository);
        await this.seedPermission('CENTRALS_UPDATE', 'Atualizar Centrais', 'Permissão para atualizar centrais', permissionRepository);
        await this.seedPermission('CENTRALS_DELETE', 'Excluir Centrais', 'Permissão para excluir centrais', permissionRepository);
        await this.seedPermission('COOPERATIVES_VIEW', 'Ver Centrais', 'Permissão para visualizar centrais', permissionRepository);
        await this.seedPermission('COOPERATIVES_CREATE', 'Criar Centrais', 'Permissão para cadastrar centrais', permissionRepository);
        await this.seedPermission('COOPERATIVES_UPDATE', 'Atualizar Centrais', 'Permissão para atualizar centrais', permissionRepository);
        await this.seedPermission('COOPERATIVES_DELETE', 'Excluir Centrais', 'Permissão para excluir centrais', permissionRepository);
        await this.seedPermission('AGENCIES_VIEW', 'Ver Agências', 'Permissão para visualizar agências', permissionRepository);
        await this.seedPermission('AGENCIES_CREATE', 'Criar Agências', 'Permissão para cadastrar agências', permissionRepository);
        await this.seedPermission('AGENCIES_UPDATE', 'Atualizar Agências', 'Permissão para atualizar agências', permissionRepository);
        await this.seedPermission('AGENCIES_DELETE', 'Excluir Agências', 'Permissão para excluir Agências', permissionRepository);
        await this.seedPermission('WALLETRANGEVALUES_DELETE', 'Excluir faixa de rendimento', 'Permissão para excluir faixa de rendimento', permissionRepository);
        await this.seedPermission('WALLETRANGEVALUES_VIEW', 'Visualizar faixa de rendimento', 'Permissão para visualizar faixa de rendimento', permissionRepository);
        await this.seedPermission('WALLETRANGEVALUES_CREATE', 'Criar faixa de rendimento', 'Permissão para criar faixa de rendimento', permissionRepository);
        await this.seedPermission('WALLETRANGEVALUES_UPDATE', 'Atualizar faixa de rendimento', 'Permissão para atualizar faixa de rendimento', permissionRepository);
        await this.seedPermission('ATTENDANCES_VIEW', 'Ver Atendimentos', 'Permissão para visualizar Atendimentos', permissionRepository);
        await this.seedPermission('ATTENDANCES_CREATE', 'Criar Atendimentos', 'Permissão para cadastrar Atendimentos', permissionRepository);
        await this.seedPermission('ATTENDANCES_UPDATE', 'Atualizar Atendimentos', 'Permissão para atualizar Atendimentos', permissionRepository);
        await this.seedPermission('ATTENDANCES_DELETE', 'Excluir Atendimentos', 'Permissão para excluir Atendimentos', permissionRepository);
        await this.seedPermission('SCHEDULES_VIEW', 'Ver Eventos', 'Permissão para visualizar eventos', permissionRepository);
        await this.seedPermission('SCHEDULES_CREATE', 'Criar Eventos', 'Permissão para cadastrar eventos', permissionRepository);
        await this.seedPermission('SCHEDULES_UPDATE', 'Atualizar Eventos', 'Permissão para atualizar eventos', permissionRepository);
        await this.seedPermission('SCHEDULES_DELETE', 'Excluir Eventos', 'Permissão para excluir eventos', permissionRepository);
        await this.seedPermission('ASSOCIATES_VIEW', 'Ver Associados', 'Permissão para visualizar associados', permissionRepository);
        await this.seedPermission('ASSOCIATES_CREATE', 'Criar Associados', 'Permissão para cadastrar associados', permissionRepository);
        await this.seedPermission('ASSOCIATES_UPDATE', 'Atualizar Associados', 'Permissão para atualizar associados', permissionRepository);
        await this.seedPermission('ASSOCIATES_DELETE', 'Excluir Associados', 'Permissão para excluir associados', permissionRepository);
        await this.seedPermission('BUSINESS_VIEW', 'Ver Negócios no APP', 'Permissão para visualizar negócios no APP', permissionRepository);
        await this.seedPermission('ASSOCIATES_WALLETS_VIEW', 'Ver Atendimentos no APP', 'Permissão para visualizar atendimentos no APP', permissionRepository);
        await this.seedPermission('WALLET-SUMMARY_VIEW', 'Visualizar Sumário da carteira', 'Permissão para visualizar o resumo de carteiras', permissionRepository);
        await this.seedPermission('WALLET-SUMMARY-FILTERS_VIEW', 'Visualizar Filtros do sumário da carteira', 'Permissão para visualizar os filtros', permissionRepository);
        await this.seedPermission('WALLET-AGENCY-FILTERS_VIEW', 'Visualizar Filtros da agência da carteira', 'Permissão para visualizar o filtro de agência', permissionRepository);
        await this.seedPermission('ATTENDANCE-PRODUCTS-EFFECTIVE_VIEW', 'Visualizar produtos efetivados', 'Permissão para visualizar produtos efetivados', permissionRepository);
        await this.seedPermission('ATTENDANCE-PRODUCTS-EFFECTIVE_CREATE', 'Criar produtos efetivados', 'Permissão para criar produtos efetivados', permissionRepository);
        await this.seedPermission('ATTENDANCE-PRODUCTS-EFFECTIVE_UPDATE', 'Atualizar produtos efetivados', 'Permissão para atualizar produtos efetivados', permissionRepository);
        await this.seedPermission('ATTENDANCE-PRODUCTS-EFFECTIVE_DELETE', 'Excluir produtos efetivados', 'Permissão para excluir produtos efetivados', permissionRepository);
        await this.seedPermission('GOAL_CREATE', 'Criar Metas', 'Permissão para criar Metas', permissionRepository);
        await this.seedPermission('GOAL_UPDATE', 'Atualizar Metas', 'Permissão para atualizar Metas', permissionRepository);
        await this.seedPermission('GOAL_VIEW', 'Visualizar Metas', 'Permissão para visualizar Metas', permissionRepository);
        await this.seedPermission('GOAL_DELETE', 'Excluir Metas', 'Permissão para excluir Metas', permissionRepository);
        await this.seedPermission('GOAL_VIEW_APP', 'Visualizar Metas no APP', 'Permissão para visualizar Metas no APP', permissionRepository);
    }
    async seedPermission(key, name, description, permissionRepository) {
        const existing = await permissionRepository.findOne({ where: { key } });
        if (!existing) {
            const permission = new permission_entity_1.Permission();
            permission.key = key;
            permission.name = name;
            permission.description = description;
            await permissionRepository.save(permission);
        }
    }
};
exports.PermissionsSeeder = PermissionsSeeder;
exports.PermissionsSeeder = PermissionsSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], PermissionsSeeder);
//# sourceMappingURL=permissions.seeder.js.map