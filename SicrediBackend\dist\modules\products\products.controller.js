"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsController = void 0;
const common_1 = require("@nestjs/common");
const products_service_1 = require("./products.service");
const create_product_dto_1 = require("./dto/create-product.dto");
const update_product_dto_1 = require("./dto/update-product.dto");
const swagger_1 = require("@nestjs/swagger");
let ProductsController = class ProductsController {
    constructor(productsService) {
        this.productsService = productsService;
    }
    create(createProductDto) {
        return this.productsService.create(createProductDto);
    }
    findAll() {
        return this.productsService.findAll();
    }
    findOne(id) {
        return this.productsService.findOne(+id);
    }
    update(id, updateProductDto) {
        return this.productsService.update(+id, updateProductDto);
    }
    remove(id) {
        return this.productsService.remove(+id);
    }
    async createBulk(productDto) {
        if (!productDto || productDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.productsService.createProductFromBulk(productDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    async deleteBulk(body) {
        if (!body.productCodes || body.productCodes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.productsService.deleteProductsFromBulk(body.productCodes);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.ProductsController = ProductsController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/products'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo Produto' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Produto criado com sucesso.',
        type: create_product_dto_1.CreateProductDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_product_dto_1.CreateProductDto]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/products'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Produtos' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Produtos encontrada com sucesso.',
        type: create_product_dto_1.CreateProductDto,
        isArray: true,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Produto encontrado.' }),
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/products'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Produto pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produto encontrado com sucesso.',
        type: create_product_dto_1.CreateProductDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Produto não encontrado.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/products'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Produto' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produto atualizado com sucesso.',
        type: update_product_dto_1.UpdateProductDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Produto não encontrado.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_product_dto_1.UpdateProductDto]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/products'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Produto' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Produto removido com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Produto não encontrado.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductsController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplos produtos em massa',
        description: `
    Este endpoint permite criar novos produtos ou atualizar produtos já existentes em massa.
    **Se um produto já existir (com base no código - "code"), seus dados serão atualizados**.
    A requisição pode conter até **20.000 produtos** por chamada, sendo processados em lotes de 500.
  `,
    }),
    (0, swagger_1.ApiBody)({
        type: [create_product_dto_1.CreateProductDto],
        description: 'Array de objetos contendo os dados dos produtos a serem criados ou atualizados.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar produtos',
                value: [
                    {
                        code: 'PD01',
                        name: 'Produto A',
                        description: 'Descrição do Produto A',
                        type: 'Tipo 1',
                        valueOrQuantity: 0,
                    },
                    {
                        code: 'PD02',
                        name: 'Produto B',
                        description: 'Descrição do Produto B',
                        type: 'Tipo 2',
                        valueOrQuantity: 1,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Produtos criados ou atualizados com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Products processed successfully',
                processedProducts: [
                    {
                        id: 1,
                        name: 'Produto A',
                        code: 'PD01',
                        description: 'Descrição do Produto A',
                        type: 'Tipo 1',
                        valueOrQuantity: 0,
                        updated: true,
                    },
                    {
                        id: 2,
                        name: 'Produto B',
                        code: 'PD02',
                        description: 'Descrição do Produto B',
                        type: 'Tipo 2',
                        valueOrQuantity: 1,
                        updated: false,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: code, name, description, type, value_or_quantity',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns produtos foram processados com sucesso, mas outros tiveram erros.',
        schema: {
            example: {
                processedProducts: [
                    {
                        id: 3,
                        name: 'Produto C',
                        code: 'PD03',
                        description: 'Descrição do Produto C',
                        type: 'Tipo 3',
                        valueOrQuantity: 2,
                        updated: false,
                    },
                ],
                errors: [
                    {
                        product: {
                            code: 'INVALID_PD',
                            name: 'Produto Inválido',
                        },
                        status: 'error',
                        message: 'Type field is required',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "createBulk", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir múltiplos produtos',
        description: `
      Este endpoint permite excluir múltiplos produtos em uma única requisição.
      Caso um produto esteja vinculado a outras tabelas (como atendimentos, metas e estratégias), a exclusão será impedida.
      A exclusão é feita via *soft delete*.
    `,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de códigos dos produtos a serem excluídos',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar produtos',
                value: {
                    productCodes: ['P001', 'P002', 'P003'],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Produtos excluídos com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Products deleted successfully',
                processedProducts: [
                    {
                        id: 1,
                        code: 'P001',
                        name: 'Produto XPTO',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns produtos foram excluídos, mas outros apresentaram erro.',
        schema: {
            example: {
                status: 'partial_success',
                message: 'Some products had errors',
                processedProducts: [
                    {
                        id: 1,
                        code: 'P001',
                        name: 'Produto XPTO',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        product_code: 'P002',
                        status: 'error',
                        message: "Cannot delete product 'P002' because it is linked to 3 attendance_product(s).",
                        linked_attendance_products: [
                            { id: 10, attendanceId: 1001 },
                            { id: 11, attendanceId: 1002 },
                            { id: 12, attendanceId: 1003 },
                        ],
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProductsController.prototype, "deleteBulk", null);
exports.ProductsController = ProductsController = __decorate([
    (0, common_1.Controller)('/api/v1/products'),
    __metadata("design:paramtypes", [products_service_1.ProductsService])
], ProductsController);
//# sourceMappingURL=products.controller.js.map