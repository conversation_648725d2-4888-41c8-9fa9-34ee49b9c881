"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateDetailsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const associate_details_entity_1 = require("./entities/associate-details.entity");
const typeorm_2 = require("typeorm");
const associate_entity_1 = require("../associates/entities/associate.entity");
const account_entity_1 = require("../accounts/entities/account.entity");
const card_type_entity_1 = require("../card-types/entities/card-type.entity");
const card_entity_1 = require("../cards/entities/card.entity");
const cryptography_1 = require("../../common/functions/cryptography");
let AssociateDetailsService = class AssociateDetailsService {
    constructor(associateDetailsRepository, associateRepository, accountRepository, cardRepository, cardTypeRepository, cryptography) {
        this.associateDetailsRepository = associateDetailsRepository;
        this.associateRepository = associateRepository;
        this.accountRepository = accountRepository;
        this.cardRepository = cardRepository;
        this.cardTypeRepository = cardTypeRepository;
        this.cryptography = cryptography;
    }
    async create(createAssociateDetails) {
        const newAssociateDetails = new associate_details_entity_1.AssociateDetails();
        newAssociateDetails.associateId = createAssociateDetails.associateId;
        newAssociateDetails.isRestrict = createAssociateDetails.isRestrict;
        newAssociateDetails.lcaValue = createAssociateDetails.lcaValue;
        newAssociateDetails.depositValue = createAssociateDetails.depositValue;
        newAssociateDetails.missingInstallments = createAssociateDetails.missingInstallments;
        newAssociateDetails.hasSubscription = createAssociateDetails.hasSubscription;
        newAssociateDetails.termDepositExpiration = createAssociateDetails.termDepositExpiration;
        newAssociateDetails.lcaExpiration = createAssociateDetails.lcaExpiration;
        newAssociateDetails.lastRegistrationUpdate = createAssociateDetails.lastRegistrationUpdate;
        newAssociateDetails.channelAccess = createAssociateDetails.channelAccess;
        newAssociateDetails.hasFinancialFlow = createAssociateDetails.hasFinancialFlow;
        const response = await this.associateDetailsRepository.save(newAssociateDetails);
        return {
            id: response.id,
            associateId: response.associateId,
            isRestrict: response.isRestrict,
            lcaValue: response.lcaValue,
            depositValue: response.depositValue,
            missingInstallments: response.missingInstallments,
            hasSubscription: response.hasSubscription,
            termDepositExpiration: response.termDepositExpiration,
            lcaExpiration: response.lcaExpiration,
            lastRegistrationUpdate: response.lastRegistrationUpdate,
            channelAccess: response.channelAccess,
            hasFinancialFlow: response.hasFinancialFlow,
        };
    }
    async findByAssociateId(id) {
        const associateDetails = await this.associateDetailsRepository.findOne({ where: { associateId: id } });
        return associateDetails;
    }
    async findOne(id) {
        const associateDetails = await this.associateDetailsRepository.findOne({ where: { id } });
        return associateDetails;
    }
    async update(id, updateAssociateDetailsDto) {
        const associateDetails = await this.associateDetailsRepository.findOne({ where: { id } });
        if (!associateDetails) {
            return associateDetails;
        }
        associateDetails.associateId = updateAssociateDetailsDto.associateId;
        associateDetails.isRestrict = updateAssociateDetailsDto.isRestrict;
        associateDetails.isDebtor = updateAssociateDetailsDto.isDebtor;
        associateDetails.lcaValue = updateAssociateDetailsDto.lcaValue;
        associateDetails.depositValue = updateAssociateDetailsDto.depositValue;
        associateDetails.missingInstallments = updateAssociateDetailsDto.missingInstallments;
        associateDetails.hasSubscription = updateAssociateDetailsDto.hasSubscription;
        associateDetails.termDepositExpiration = updateAssociateDetailsDto.termDepositExpiration;
        associateDetails.lcaExpiration = updateAssociateDetailsDto.lcaExpiration;
        associateDetails.lastRegistrationUpdate = updateAssociateDetailsDto.lastRegistrationUpdate;
        associateDetails.channelAccess = updateAssociateDetailsDto.channelAccess;
        associateDetails.hasFinancialFlow = updateAssociateDetailsDto.hasFinancialFlow;
        const response = await this.associateDetailsRepository.save(associateDetails);
        return {
            id: response.id,
            associateId: response.associateId,
            isRestrict: response.isRestrict,
            isDebtor: response.isDebtor,
            lcaValue: response.lcaValue,
            depositValue: response.depositValue,
            missingInstallments: response.missingInstallments,
            hasSubscription: response.hasSubscription,
            termDepositExpiration: response.termDepositExpiration,
            lcaExpiration: response.lcaExpiration,
            lastRegistrationUpdate: response.lastRegistrationUpdate,
            channelAccess: response.channelAccess,
            hasFinancialFlow: response.hasFinancialFlow,
        };
    }
    async remove(id) {
        const associateDetails = await this.associateDetailsRepository.findOne({ where: { id } });
        if (!associateDetails) {
            return associateDetails;
        }
        await this.associateDetailsRepository.update(id, { deletedAt: new Date() });
    }
    async createAssociateDetailsFromBulk(dtos) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!Array.isArray(dtos) || dtos.length === 0) {
            return {
                status: 'error',
                message: 'O payload deve ser um array de AssociateDetailsDto.',
                totalReceived: dtos?.length || 0,
                totalSaved: 0,
                errors: ['Payload inválido.'],
            };
        }
        if (dtos.length > MAX_BATCH_SIZE) {
            return {
                status: 'error',
                message: `Too many associate details in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: dtos.length,
                totalSaved: 0,
                errors: [`Excedeu o limite de ${MAX_BATCH_SIZE} registros.`],
            };
        }
        const allMissingFields = [];
        dtos.forEach((item, index) => {
            const hasCpf = !!item.associate_cpf;
            const hasCnpj = !!item.associate_cnpj;
            if (hasCpf && hasCnpj) {
                allMissingFields.push(`Não envie CPF e CNPJ ao mesmo tempo (index ${index})`);
            }
            else if (!hasCpf && !hasCnpj) {
                allMissingFields.push(`É obrigatório informar CPF ou CNPJ (index ${index})`);
            }
            if (item.is_restrict === undefined) {
                allMissingFields.push(`Campo obrigatório ausente: is_restrict (index ${index})`);
            }
            if (item.is_debtor === undefined) {
                allMissingFields.push(`Campo obrigatório ausente: is_debtor (index ${index})`);
            }
            if (item.has_subscription === undefined) {
                allMissingFields.push(`Campo obrigatório ausente: has_subscription (index ${index})`);
            }
            if (item.has_financial_flow === undefined) {
                allMissingFields.push(`Campo obrigatório ausente: has_financial_flow (index ${index})`);
            }
            if (item.is_blocked === undefined) {
                allMissingFields.push(`Campo obrigatório ausente: is_blocked (index ${index})`);
            }
        });
        if (allMissingFields.length > 0) {
            return {
                status: 'error',
                message: `Campos obrigatórios ausentes ou inválidos: ${allMissingFields.join(', ')}`,
                totalReceived: dtos.length,
                totalSaved: 0,
                errors: allMissingFields,
            };
        }
        const batches = [];
        for (let i = 0; i < dtos.length; i += BATCH_SIZE) {
            batches.push(dtos.slice(i, i + BATCH_SIZE));
        }
        const results = await Promise.allSettled(batches.map((batch, idx) => this.processAssociateDetailsBatch(batch, idx * BATCH_SIZE)));
        const savedDetails = [];
        const errors = [];
        results.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                savedDetails.push(...result.value.saved);
                errors.push(...result.value.errors);
            }
            else {
                errors.push({
                    batch: index,
                    status: 'error',
                    message: result.reason?.message || 'Erro inesperado ao processar lote',
                });
            }
        });
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Alguns registros apresentaram erros' : 'Registros processados com sucesso',
            totalReceived: dtos.length,
            totalSaved: savedDetails.length,
            savedDetails,
            errors,
        };
    }
    async processAssociateDetailsBatch(batch, offset) {
        const normalize = (doc) => doc.replace(/\D/g, '');
        const pureCpfs = batch.filter(d => d.associate_cpf).map(d => normalize(d.associate_cpf));
        const pureCnpjs = batch.filter(d => d.associate_cnpj).map(d => normalize(d.associate_cnpj));
        const encryptedCpfs = pureCpfs.map(cpf => this.cryptography.encrypt(cpf));
        const encryptedCnpjs = pureCnpjs.map(cnpj => this.cryptography.encrypt(cnpj));
        const [associatesByCpf, associatesByCnpj] = await Promise.all([
            encryptedCpfs.length ? this.associateRepository.find({ where: { cpf: (0, typeorm_2.In)(encryptedCpfs) } }) : [],
            encryptedCnpjs.length ? this.associateRepository.find({ where: { cnpj: (0, typeorm_2.In)(encryptedCnpjs) } }) : [],
        ]);
        const mapCpf = new Map();
        associatesByCpf.forEach(a => a.cpf && mapCpf.set(a.cpf, a));
        const mapCnpj = new Map();
        associatesByCnpj.forEach(a => a.cnpj && mapCnpj.set(a.cnpj, a));
        const allAssociateIds = new Set([...associatesByCpf, ...associatesByCnpj].map(a => a.id));
        const existingDetails = allAssociateIds.size
            ? await this.associateDetailsRepository.find({ where: { associateId: (0, typeorm_2.In)([...allAssociateIds]) } })
            : [];
        const detailsMap = new Map();
        existingDetails.forEach(d => detailsMap.set(d.associateId, d));
        const toSave = [];
        const errors = [];
        for (const [i, dto] of batch.entries()) {
            const index = offset + i;
            const messages = [];
            const hasCpf = !!dto.associate_cpf;
            const hasCnpj = !!dto.associate_cnpj;
            if (hasCpf && hasCnpj || (!hasCpf && !hasCnpj)) {
                continue;
            }
            const doc = hasCpf ? normalize(dto.associate_cpf) : normalize(dto.associate_cnpj);
            const encrypted = this.cryptography.encrypt(doc);
            const found = hasCpf ? mapCpf.get(encrypted) : mapCnpj.get(encrypted);
            if (!found) {
                messages.push(`Nenhum Associate encontrado para ${hasCpf ? 'CPF' : 'CNPJ'} = ${doc}`);
                errors.push({ index, data: dto, messages });
                continue;
            }
            let entity = detailsMap.get(found.id);
            if (!entity) {
                entity = this.associateDetailsRepository.create({ associateId: found.id });
                detailsMap.set(found.id, entity);
            }
            if (dto.is_restrict !== undefined)
                entity.isRestrict = dto.is_restrict;
            if (dto.is_debtor !== undefined)
                entity.isDebtor = dto.is_debtor;
            if (dto.lca_value !== undefined)
                entity.lcaValue = dto.lca_value;
            if (dto.deposit_value !== undefined)
                entity.depositValue = dto.deposit_value;
            if (dto.missing_installments !== undefined)
                entity.missingInstallments = dto.missing_installments;
            if (dto.has_subscription !== undefined)
                entity.hasSubscription = dto.has_subscription;
            if (dto.last_registration_update !== undefined)
                entity.lastRegistrationUpdate = dto.last_registration_update;
            if (dto.channel_access !== undefined)
                entity.channelAccess = dto.channel_access;
            if (dto.has_financial_flow !== undefined)
                entity.hasFinancialFlow = dto.has_financial_flow;
            if (dto.term_deposit_expiration !== undefined)
                entity.termDepositExpiration = dto.term_deposit_expiration;
            if (dto.lca_expiration !== undefined)
                entity.lcaExpiration = dto.lca_expiration;
            if (dto.is_blocked !== undefined)
                entity.isBlocked = dto.is_blocked;
            if (dto.credit_card_last_buy !== undefined)
                entity.creditCardLastBuy = dto.credit_card_last_buy;
            toSave.push(entity);
        }
        let saved = [];
        if (toSave.length > 0) {
            saved = await this.associateDetailsRepository.save(toSave);
        }
        return { saved, errors };
    }
};
exports.AssociateDetailsService = AssociateDetailsService;
exports.AssociateDetailsService = AssociateDetailsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(associate_details_entity_1.AssociateDetails)),
    __param(1, (0, typeorm_1.InjectRepository)(associate_entity_1.Associate)),
    __param(2, (0, typeorm_1.InjectRepository)(account_entity_1.Accounts)),
    __param(3, (0, typeorm_1.InjectRepository)(card_entity_1.Card)),
    __param(4, (0, typeorm_1.InjectRepository)(card_type_entity_1.CardType)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        cryptography_1.Cryptography])
], AssociateDetailsService);
//# sourceMappingURL=associate-details.service.js.map