import { OnModuleInit } from '@nestjs/common';
import { DataSource } from 'typeorm';
export declare class CooperativeSeeder implements OnModuleInit {
    private readonly dataSource;
    constructor(dataSource: DataSource);
    onModuleInit(): Promise<void>;
    private getCentralByName;
    private getCooperative;
    private saveCooperative;
    executeSeed(): Promise<void>;
    private seedCooperative;
}
