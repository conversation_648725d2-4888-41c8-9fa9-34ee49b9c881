import { AssociatePhoneService } from './associate-phone.service';
import { CreateAssociatePhoneDto } from './dto/create-associate-phone.dto';
export declare class AssociatePhoneController {
    private readonly associatePhoneService;
    constructor(associatePhoneService: AssociatePhoneService);
    create(createAssociatePhoneDto: CreateAssociatePhoneDto): Promise<{
        id: number;
        associateId: number;
        phone: string;
        createdAt: Date;
    }>;
    createMany(createAssociatePhoneDto: CreateAssociatePhoneDto[]): Promise<{
        id: number;
        associateId: number;
        phone: string;
        createdAt: Date;
    }[]>;
    findAllByAssociate(id: string): Promise<{
        phone: string;
        id?: number;
        associateId: number;
        createdAt: Date;
        updatedAt?: Date;
        deletedAt?: Date;
        associate?: import("../associates/entities/associate.entity").Associate;
    }[]>;
}
