"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CentralsModule = void 0;
const common_1 = require("@nestjs/common");
const centrals_service_1 = require("./centrals.service");
const centrals_controller_1 = require("./centrals.controller");
const typeorm_1 = require("@nestjs/typeorm");
const central_entity_1 = require("./entities/central.entity");
const cryptography_1 = require("../../common/functions/cryptography");
const cooperatives_module_1 = require("../cooperatives/cooperatives.module");
const federations_module_1 = require("../federations/federations.module");
let CentralsModule = class CentralsModule {
};
exports.CentralsModule = CentralsModule;
exports.CentralsModule = CentralsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([central_entity_1.Central]),
            (0, common_1.forwardRef)(() => cooperatives_module_1.CooperativesModule),
            (0, common_1.forwardRef)(() => federations_module_1.FederationsModule),
        ],
        controllers: [centrals_controller_1.CentralsController],
        providers: [centrals_service_1.CentralsService, cryptography_1.Cryptography],
        exports: [centrals_service_1.CentralsService, typeorm_1.TypeOrmModule],
    })
], CentralsModule);
//# sourceMappingURL=centrals.module.js.map