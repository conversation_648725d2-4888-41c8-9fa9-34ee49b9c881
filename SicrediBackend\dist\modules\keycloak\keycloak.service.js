"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KeycloakService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const jsonReal = __importStar(require("./realm.json"));
let KeycloakService = class KeycloakService {
    constructor(configService, http) {
        this.configService = configService;
        this.http = http;
        this.keycloakUrl = this.configService.get('KEYCLOAK_URL');
        this.keycloakRealm = this.configService.get('KEYCLOAK_REALM');
        this.keycloakAdminClientId = this.configService.get('KEYCLOAK_ADMIN_CLIENT_ID');
        this.keycloakAdminLogin = this.configService.get('KEYCLOAK_ADMIN_LOGIN');
        this.keycloakAdminPassword = this.configService.get('KEYCLOAK_ADMIN_PASSWORD');
        this.keycloakAdminRealm = this.configService.get('KEYCLOAK_ADMIN_REALM');
    }
    async get({ token, url, }) {
        try {
            const fullUrl = `${this.keycloakUrl}/admin/realms/${this.keycloakRealm}/${url}`;
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.get(fullUrl, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
            }));
            return { data, status };
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error?.response?.data?.errorMessage);
        }
    }
    async post({ token, url, values, }) {
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/admin/realms/${this.keycloakRealm}/${url}`, values, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
            }));
            return { data: response.data, status: response.status };
        }
        catch (error) {
            const errorMessage = error?.response?.data?.errorMessage ||
                error?.message ||
                'Erro desconhecido';
            throw new common_1.BadRequestException(errorMessage);
        }
    }
    async put({ token, url, values, }) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.put(`${this.keycloakUrl}/admin/realms/${this.keycloakRealm}/${url}`, values, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
            }));
            return { data, status };
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error);
        }
    }
    async delete({ token, url, }) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.delete(`${this.keycloakUrl}/admin/realms/${this.keycloakRealm}/${url}`, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
            }));
            return { data, status };
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error);
        }
    }
    async createUser({ userName, password, email, firstName, lastName, token, }) {
        const value = {
            username: userName,
            enabled: true,
            email: email,
            firstName: firstName,
            lastName: lastName,
            groups: ['create-user'],
            credentials: [
                {
                    type: 'password',
                    value: password,
                    temporary: false,
                },
            ],
        };
        try {
            const { status } = await this.post({
                token,
                url: 'users',
                values: value,
            });
            if (status !== 201) {
                throw new common_1.BadRequestException('Failed to register user');
            }
            return true;
        }
        catch (error) {
            const errorMessage = error?.message || 'Erro ao criar usuário';
            if (errorMessage.includes('403')) {
                throw new common_1.ForbiddenException('Ação não permitida: verifique suas permissões.');
            }
            throw new common_1.BadRequestException(`Falha ao criar usuário: ${errorMessage}`);
        }
    }
    async usersList({ token }) {
        const { data, status } = await this.get({
            token,
            url: 'users',
        });
        if (status != 200) {
            throw new common_1.UnauthorizedException('Failed to users list');
        }
        return data;
    }
    async updateUser({ email, firstName, token, idKeycloak, }) {
        try {
            const value = {
                email: email,
                firstName: firstName,
            };
            const { status } = await this.put({
                token,
                url: `users/${idKeycloak}`,
                values: value,
            });
            if (status != 204) {
                throw new common_1.UnauthorizedException('Failed to update user');
            }
            return true;
        }
        catch (error) {
            throw new common_1.UnauthorizedException(`Failed to update user. ${error?.response?.data?.errorMessage}`);
        }
    }
    async inactiveUser({ token, idKeycloak, }) {
        try {
            const value = {
                enabled: false,
            };
            const { status } = await this.put({
                token,
                url: `users/${idKeycloak}`,
                values: value,
            });
            if (status != 204) {
                throw new common_1.UnauthorizedException('Failed to inactive user');
            }
            return true;
        }
        catch (error) {
            throw new common_1.UnauthorizedException(`Failed to inactive user. ${error?.response?.data?.errorMessage}`);
        }
    }
    async adminToken() {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/realms/${this.keycloakAdminRealm}/protocol/openid-connect/token`, new URLSearchParams({
                client_id: this.keycloakAdminClientId,
                grant_type: 'password',
                username: this.keycloakAdminLogin,
                password: this.keycloakAdminPassword,
            })));
            return { data, status };
        }
        catch (error) {
            throw new common_1.UnauthorizedException(error);
        }
    }
    async adminCreateRealm({ token, }) {
        try {
            const { data, status } = await (0, rxjs_1.firstValueFrom)(this.http.post(`${this.keycloakUrl}/admin/realms`, jsonReal, {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${token}`,
                },
            }));
            return { data, status };
        }
        catch (error) {
            if (error.status === 409) {
                throw new common_1.ConflictException(error.response.data.errorMessage, 'Falha ao criar realm');
            }
            throw new common_1.BadRequestException(error, 'Falha ao criar realm');
        }
    }
    async deleteUser({ token, idKeycloak, }) {
        try {
            const { status } = await this.delete({
                token,
                url: `users/${idKeycloak}`,
            });
            if (status != 204) {
                throw new common_1.UnauthorizedException('Failed to delete user');
            }
            return true;
        }
        catch (error) {
            throw new common_1.UnauthorizedException(`Failed to delete user. ${error?.response?.data?.errorMessage}`);
        }
    }
};
exports.KeycloakService = KeycloakService;
exports.KeycloakService = KeycloakService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService,
        axios_1.HttpService])
], KeycloakService);
//# sourceMappingURL=keycloak.service.js.map