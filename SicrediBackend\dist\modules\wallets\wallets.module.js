"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const wallets_service_1 = require("./wallets.service");
const wallets_controller_1 = require("./wallets.controller");
const wallet_entity_1 = require("./entities/wallet.entity");
const segment_entity_1 = require("../segments/entities/segment.entity");
const wallet_range_value_entity_1 = require("../wallet-range-values/entities/wallet-range-value.entity");
const segments_module_1 = require("../segments/segments.module");
const wallet_range_values_module_1 = require("../wallet-range-values/wallet-range-values.module");
const associates_module_1 = require("../associates/associates.module");
const user_wallets_module_1 = require("../user-wallets/user-wallets.module");
const users_module_1 = require("../users/users.module");
const keycloak_module_1 = require("../keycloak/keycloak.module");
const profile_permissions_module_1 = require("../profile-permissions/profile-permissions.module");
const profiles_module_1 = require("../profiles/profiles.module");
const axios_1 = require("@nestjs/axios");
const agency_entity_1 = require("../agencies/entities/agency.entity");
const agencies_module_1 = require("../agencies/agencies.module");
const account_entity_1 = require("../accounts/entities/account.entity");
const account_wallets_entity_1 = require("../account-wallets/entities/account-wallets.entity");
const goal_product_wallet_entity_1 = require("../goal-product-wallet/entities/goal-product-wallet.entity");
let WalletsModule = class WalletsModule {
};
exports.WalletsModule = WalletsModule;
exports.WalletsModule = WalletsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                wallet_entity_1.Wallet,
                segment_entity_1.Segment,
                wallet_range_value_entity_1.WalletRangeValue,
                agency_entity_1.Agency,
                account_entity_1.Accounts,
                account_wallets_entity_1.AccountWallets,
                goal_product_wallet_entity_1.GoalProductWallet,
                wallet_range_value_entity_1.WalletRangeValue
            ]),
            segments_module_1.SegmentsModule,
            wallet_range_values_module_1.WalletRangeValuesModule,
            associates_module_1.AssociatesModule,
            (0, common_1.forwardRef)(() => user_wallets_module_1.UserWalletsModule),
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            keycloak_module_1.KeycloakModule,
            profile_permissions_module_1.ProfilePermissionsModule,
            profiles_module_1.ProfilesModule,
            axios_1.HttpModule,
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule),
        ],
        controllers: [wallets_controller_1.WalletsController],
        providers: [wallets_service_1.WalletsService],
        exports: [wallets_service_1.WalletsService, typeorm_1.TypeOrmModule],
    })
], WalletsModule);
//# sourceMappingURL=wallets.module.js.map