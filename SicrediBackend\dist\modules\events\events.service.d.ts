import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { Repository } from 'typeorm';
import { Event } from './entities/event.entity';
export declare class EventsService {
    private readonly eventRepository;
    constructor(eventRepository: Repository<Event>);
    create(createEventDto: CreateEventDto): Promise<CreateEventDto>;
    findAll(): Promise<CreateEventDto[]>;
    findOne(id: number): Promise<CreateEventDto>;
    update(id: number, updateEventDto: UpdateEventDto): Promise<UpdateEventDto>;
    remove(id: number): Promise<void>;
}
