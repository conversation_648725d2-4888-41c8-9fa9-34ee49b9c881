"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgencySeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const cooperative_entity_1 = require("../modules/cooperatives/entities/cooperative.entity");
const agency_entity_1 = require("../modules/agencies/entities/agency.entity");
let AgencySeeder = class AgencySeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async getCooperativeByName(name, cooperativeRepository) {
        return await cooperativeRepository.findOne({ where: { name: name } });
    }
    async getAgency(agencyCode, name, agencyRepository) {
        return await agencyRepository.findOne({
            where: { agencyCode, name },
        });
    }
    async saveAgency(existing, cooperative, agencyRepository) {
        if (!existing) {
            const agency = new agency_entity_1.Agency();
            agency.cooperativeId = cooperative.id;
            agency.cooperative = cooperative;
            agency.agencyCode = '0001';
            agency.name = 'Agência Central';
            agency.address = 'Av. Principal, 123 - Centro';
            return await agencyRepository.save(agency);
        }
        return false;
    }
    async executeSeed() {
        await this.seedAgency();
    }
    async seedAgency() {
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const cooperativeRepository = this.dataSource.getRepository(cooperative_entity_1.Cooperative);
        const cooperative = await this.getCooperativeByName('Cooperativa Central Exemplo', cooperativeRepository);
        if (!cooperative) {
            console.error('Cooperative with name "Cooperativa Central Exemplo" does not exist. Please create it first.');
            return;
        }
        const existingAgency = await this.getAgency('0001', 'Agência Central', agencyRepository);
        await this.saveAgency(existingAgency, cooperative, agencyRepository);
    }
};
exports.AgencySeeder = AgencySeeder;
exports.AgencySeeder = AgencySeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AgencySeeder);
//# sourceMappingURL=agency.seeder.js.map