import { OnModuleInit } from '@nestjs/common';
import { PermissionsSeeder } from './permissions.seeder';
import { ProfilesSeeder } from './profile.seeder';
import { UserSeeder } from './user.seeder';
import { ProfilePermissionsSeeder } from './profile-permissions.seeder';
import { PermissionsStrategySeeder } from './permissions-strategy.seeder';
import { ProfilePermissionsStrategySeeder } from './profile-permissions-strategy.seeder';
import { FederationSeeder } from './federation.seeder';
import { CentralSeeder } from './central.seeder';
import { CooperativeSeeder } from './cooperative.seeder';
import { AgencySeeder } from './agency.seeder';
import { WalletRangeValueSeeder } from './wallet-range-value.seeder';
import { WalletSeeder } from './wallet.seeder';
import { KeycloakSeeder } from './keycloak.seeder';
import { AccountTypeSeeder } from './account-type.seeder';
import { AssociateSeeder } from './associate.seeder';
import { AccountWalletSeeder } from './account-wallets.seeder';
import { AccountSeeder } from './account.seeder';
import { AssociateDetailSeeder } from './associate-detail.seeder';
import { CardSeeder } from './card.seeder';
import { CardTypeSeeder } from './card-type.seeder';
import { AssociatePhoneSeeder } from './associate-phone.seeder';
export declare class IndexSeeder implements OnModuleInit {
    private readonly keycloak;
    private readonly permissions;
    private readonly permissionsStrategySeeder;
    private readonly profile;
    private readonly user;
    private readonly profilePermission;
    private readonly profilePermissionsStrategySeeder;
    private readonly federation;
    private readonly central;
    private readonly cooperative;
    private readonly agency;
    private readonly wallet;
    private readonly walletRangeValues;
    private readonly accountType;
    private readonly account;
    private readonly accountWallet;
    private readonly associate;
    private readonly associateDetail;
    private readonly cardType;
    private readonly card;
    private readonly associatePhone;
    constructor(keycloak: KeycloakSeeder, permissions: PermissionsSeeder, permissionsStrategySeeder: PermissionsStrategySeeder, profile: ProfilesSeeder, user: UserSeeder, profilePermission: ProfilePermissionsSeeder, profilePermissionsStrategySeeder: ProfilePermissionsStrategySeeder, federation: FederationSeeder, central: CentralSeeder, cooperative: CooperativeSeeder, agency: AgencySeeder, wallet: WalletSeeder, walletRangeValues: WalletRangeValueSeeder, accountType: AccountTypeSeeder, account: AccountSeeder, accountWallet: AccountWalletSeeder, associate: AssociateSeeder, associateDetail: AssociateDetailSeeder, cardType: CardTypeSeeder, card: CardSeeder, associatePhone: AssociatePhoneSeeder);
    onModuleInit(): Promise<void>;
}
