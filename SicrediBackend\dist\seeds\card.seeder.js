"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CardSeeder = void 0;
const common_1 = require("@nestjs/common");
const card_entity_1 = require("../modules/cards/entities/card.entity");
const card_type_entity_1 = require("../modules/card-types/entities/card-type.entity");
const account_entity_1 = require("../modules/accounts/entities/account.entity");
const typeorm_1 = require("typeorm");
const cryptography_1 = require("../common/functions/cryptography");
let CardSeeder = class CardSeeder {
    constructor(dataSource, cryptography) {
        this.dataSource = dataSource;
        this.cryptography = cryptography;
    }
    async executeSeed() {
        const cardRepository = this.dataSource.getRepository(card_entity_1.Card);
        const cardTypeRepository = this.dataSource.getRepository(card_type_entity_1.CardType);
        const accountRepository = this.dataSource.getRepository(account_entity_1.Accounts);
        const account = await accountRepository.findOne({ where: { code: "123" } });
        const cardType = await cardTypeRepository.findOne({ where: { name: "CRÉDITO" } });
        await this.seedCard({
            accountId: account.id,
            holderName: "João da Silva",
            cardNumber: this.cryptography.encrypt("****************"),
            expirationDate: "2030/05",
            securityCode: this.cryptography.encrypt("123"),
            cardTypeId: cardType.id,
            createdAt: new Date("2025-03-12T09:40:04.967Z"),
            updatedAt: null,
            deletedAt: null,
        }, cardRepository);
    }
    async seedCard(cardData, cardRepository) {
        const existing = await cardRepository.findOne({ where: { accountId: cardData.accountId } });
        if (!existing) {
            const card = cardRepository.create(cardData);
            await cardRepository.save(card);
        }
    }
};
exports.CardSeeder = CardSeeder;
exports.CardSeeder = CardSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        cryptography_1.Cryptography])
], CardSeeder);
//# sourceMappingURL=card.seeder.js.map