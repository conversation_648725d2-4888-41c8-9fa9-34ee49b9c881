"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountTypeSeeder = void 0;
const common_1 = require("@nestjs/common");
const account_type_entity_1 = require("../modules/account-type/entities/account-type.entity");
const typeorm_1 = require("typeorm");
let AccountTypeSeeder = class AccountTypeSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async executeSeed() {
        const accountTypeRepository = this.dataSource.getRepository(account_type_entity_1.AccountType);
        await this.seedPermission('PESSOA FÍSICA', accountTypeRepository);
        await this.seedPermission('PESSOA JURÍDICA', accountTypeRepository);
    }
    async seedPermission(name, accountTypeRepository) {
        const existing = await accountTypeRepository.findOne({ where: { name } });
        if (!existing) {
            const type = new account_type_entity_1.AccountType();
            type.name = name;
            await accountTypeRepository.save(type);
        }
    }
};
exports.AccountTypeSeeder = AccountTypeSeeder;
exports.AccountTypeSeeder = AccountTypeSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], AccountTypeSeeder);
//# sourceMappingURL=account-type.seeder.js.map