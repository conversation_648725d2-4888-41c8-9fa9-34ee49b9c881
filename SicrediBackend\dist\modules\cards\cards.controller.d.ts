import { CardsService } from './cards.service';
import { CardDto } from './dto/card.dto';
export declare class CardsController {
    private readonly cardsService;
    constructor(cardsService: CardsService);
    findAll(): Promise<import("./entities/card.entity").Card[]>;
    findOne(id: number): Promise<{
        cardNumber: string;
        securityCode: string;
        id: number;
        accountId: number;
        holderName: string;
        expirationDate: string;
        cardTypeId: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date;
        account: import("../accounts/entities/account.entity").Accounts;
    }>;
    findByAccountId(accountId: number): Promise<{
        cardNumber: string;
        securityCode: string;
        id: number;
        accountId: number;
        holderName: string;
        expirationDate: string;
        cardTypeId: number;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date;
        account: import("../accounts/entities/account.entity").Accounts;
    }>;
    createCardsFromBulk(cardDto: CardDto[]): Promise<any>;
}
