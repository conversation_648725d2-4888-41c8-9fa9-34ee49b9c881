"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceProductsEffectiveService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const cryptography_1 = require("../../common/functions/cryptography");
const attendance_products_effective_entity_1 = require("./entities/attendance-products-effective.entity");
const product_entity_1 = require("../products/entities/product.entity");
const attendance_product_entity_1 = require("../attendance-products/entities/attendance-product.entity");
const account_entity_1 = require("../accounts/entities/account.entity");
const users_service_1 = require("../users/users.service");
const associates_service_1 = require("../associates/associates.service");
let AttendanceProductsEffectiveService = class AttendanceProductsEffectiveService {
    constructor(repo, accountsRepository, usersService, associatesService, productsRepository, cryptography, dataSource) {
        this.repo = repo;
        this.accountsRepository = accountsRepository;
        this.usersService = usersService;
        this.associatesService = associatesService;
        this.productsRepository = productsRepository;
        this.cryptography = cryptography;
        this.dataSource = dataSource;
    }
    async create(dto) {
        const entity = new attendance_products_effective_entity_1.AttendanceProductEffective();
        if (dto.accountId)
            entity.accountId = dto.accountId;
        if (dto.productId)
            entity.productId = dto.productId;
        if (dto.attendantId)
            entity.attendantId = dto.attendantId;
        if (dto.associateId)
            entity.associateId = dto.associateId;
        entity.productValue = dto.productValue
            ? this.cryptography.encrypt(dto.productValue)
            : null;
        entity.quantity = dto.quantity ?? null;
        entity.valueOrQuantity = dto.valueOrQuantity ?? null;
        const saved = await this.repo.save(entity);
        return this.toDto(saved);
    }
    async createBatch(dtos) {
        return this.repo.manager.transaction(async (transactionalEntityManager) => {
            const entities = dtos.map((dto) => {
                const entity = new attendance_products_effective_entity_1.AttendanceProductEffective();
                if (dto.accountId)
                    entity.accountId = dto.accountId;
                if (dto.productId)
                    entity.productId = dto.productId;
                if (dto.attendantId)
                    entity.attendantId = dto.attendantId;
                if (dto.associateId)
                    entity.associateId = dto.associateId;
                entity.productValue = dto.productValue
                    ? this.cryptography.encrypt(dto.productValue)
                    : null;
                entity.quantity = dto.quantity ?? null;
                entity.valueOrQuantity = dto.valueOrQuantity ?? null;
                return entity;
            });
            const savedEntities = await transactionalEntityManager.save(entities);
            return savedEntities.map((entity) => this.toDto(entity));
        });
    }
    async findAll() {
        const list = await this.repo.find({
            where: { deletedAt: (0, typeorm_2.IsNull)() },
        });
        return list.map((item) => this.toDto(item));
    }
    async findAllProducts() {
        const queryRunner = this.dataSource.createQueryRunner();
        const productsQuery = queryRunner.manager
            .getRepository(product_entity_1.Product)
            .createQueryBuilder('product')
            .select('product.id', 'id')
            .addSelect('product.name', 'productName');
        const products = await productsQuery.execute();
        return products;
    }
    async findAllAttendanceProducts() {
        const queryRunner = this.dataSource.createQueryRunner();
        const productsQuery = queryRunner.manager
            .getRepository(attendance_product_entity_1.AttendanceProduct)
            .createQueryBuilder('attendance_product')
            .select('attendance_product.id', 'attendanceProductId')
            .addSelect('attendance_product.product_id', 'productId')
            .addSelect('attendance_product.attendance_id', 'attendanceId')
            .addSelect('attendance.description', 'attendanceDescription')
            .addSelect('user.id', 'userId')
            .addSelect('user.name', 'userName')
            .innerJoin('attendance', 'attendance', 'attendance.id = attendance_product.attendance_id')
            .innerJoin('user', 'user', 'user.id = attendance.attendant_id');
        const products = await productsQuery.execute();
        return products;
    }
    async findOne(id) {
        const found = await this.repo.findOne({
            where: { id, deletedAt: (0, typeorm_2.IsNull)() },
        });
        if (!found) {
            throw new common_1.NotFoundException(`AttendanceProductEffective #${id} não encontrado.`);
        }
        return this.toDto(found);
    }
    async update(id, dto) {
        const entity = await this.repo.findOne({
            where: { id, deletedAt: (0, typeorm_2.IsNull)() },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`AttendanceProductEffective #${id} não encontrado.`);
        }
        if (dto.accountId !== undefined) {
            entity.accountId = dto.accountId;
        }
        if (dto.productId !== undefined) {
            entity.productId = dto.productId;
        }
        if (dto.attendantId !== undefined) {
            entity.attendantId = dto.attendantId;
        }
        if (dto.associateId !== undefined) {
            entity.associateId = dto.associateId;
        }
        if (dto.productValue !== undefined) {
            entity.productValue = dto.productValue
                ? this.cryptography.encrypt(dto.productValue)
                : null;
        }
        if (dto.quantity !== undefined) {
            entity.quantity = dto.quantity;
        }
        if (dto.valueOrQuantity !== undefined) {
            entity.valueOrQuantity = dto.valueOrQuantity;
        }
        entity.updatedAt = new Date();
        const saved = await this.repo.save(entity);
        return this.toDto(saved);
    }
    async remove(id) {
        const entity = await this.repo.findOne({
            where: { id, deletedAt: (0, typeorm_2.IsNull)() },
        });
        if (!entity) {
            throw new common_1.NotFoundException(`AttendanceProductEffective #${id} não encontrado.`);
        }
        entity.updatedAt = new Date();
        entity.deletedAt = new Date();
        await this.repo.save(entity);
        return { success: true };
    }
    toDto(entity) {
        return {
            id: entity.id,
            accountId: entity.accountId,
            productId: entity.productId,
            attendantId: entity.attendantId,
            associateId: entity.associateId,
            valueOrQuantity: entity.valueOrQuantity,
            quantity: entity.quantity,
            productValue: entity.productValue
                ? this.cryptography.decrypt(entity.productValue)
                : null,
        };
    }
    async createProductEffectiveFromBulk(attendanceProductEffectiveDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!attendanceProductEffectiveDto || attendanceProductEffectiveDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (attendanceProductEffectiveDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many requests in a single batch. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: attendanceProductEffectiveDto.length,
            });
        }
        const processedRecords = [];
        const errors = [];
        for (let i = 0; i < attendanceProductEffectiveDto.length; i += BATCH_SIZE) {
            const batch = attendanceProductEffectiveDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.account_code)
                        missingFields.push(`account_code (index ${i + index})`);
                    if (!item.product_code)
                        missingFields.push(`product_code (index ${i + index})`);
                    if (!item.associate_cpf && !item.associate_cnpj)
                        missingFields.push(`associate_cpf or associate_cnpj (index ${i + index})`);
                    if (!item.user_cpf && !item.user_cnpj)
                        missingFields.push(`user_cpf or user_cnpj (index ${i + index})`);
                    if (!item.product_value)
                        missingFields.push(`product_value (index ${i + index})`);
                    if (!item.quantity)
                        missingFields.push(`quantity (index ${i + index})`);
                    if (!item.value_or_quantity == undefined)
                        missingFields.push(`value_or_quantity (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const normalizeDocument = (document) => document.replace(/\D/g, '');
                const associateCpfList = batch
                    .filter((item) => item.associate_cpf)
                    .map((item) => normalizeDocument(item.associate_cpf));
                const associateCnpjList = batch
                    .filter((item) => item.associate_cnpj)
                    .map((item) => normalizeDocument(item.associate_cnpj));
                const encryptedAssociateCpfMap = new Map(associateCpfList.map((cpf) => [cpf, this.cryptography.encrypt(cpf)]));
                const encryptedAssociateCnpjMap = new Map(associateCnpjList.map((cnpj) => [cnpj, this.cryptography.encrypt(cnpj)]));
                const userCpfList = batch
                    .filter((item) => item.user_cpf)
                    .map((item) => normalizeDocument(item.user_cpf));
                const userCnpjList = batch
                    .filter((item) => item.user_cnpj)
                    .map((item) => normalizeDocument(item.user_cnpj));
                const encryptedUserCpfMap = new Map(userCpfList.map((cpf) => [cpf, this.cryptography.encrypt(cpf)]));
                const encryptedUserCnpjMap = new Map(userCnpjList.map((cnpj) => [cnpj, this.cryptography.encrypt(cnpj)]));
                const accountCodes = batch.map((item) => item.account_code);
                const productCodes = batch.map((item) => item.product_code);
                const [accounts, products, associates, users, existingRecords] = await Promise.all([
                    this.accountsRepository.find({ where: { code: (0, typeorm_2.In)(accountCodes) } }),
                    this.productsRepository.find({ where: { code: (0, typeorm_2.In)(productCodes) } }),
                    this.associatesService.findByDocuments([...encryptedAssociateCpfMap.values()], [...encryptedAssociateCnpjMap.values()]),
                    this.usersService.findByDocuments([...encryptedUserCpfMap.values()], [...encryptedUserCnpjMap.values()]),
                    this.repo.find({
                        where: {
                            accountId: (0, typeorm_2.In)(accountCodes),
                            productId: (0, typeorm_2.In)(productCodes),
                        },
                    }),
                ]);
                const accountMap = new Map(accounts.map((a) => [a.code, a]));
                const productMap = new Map(products.map((p) => [p.code, p]));
                const associateMap = new Map(associates.map((a) => [a.cpf ?? a.cnpj, a]));
                const userMap = new Map(users.map((u) => [u.cpf ?? u.cnpj, u]));
                const existingRecordMap = new Map(existingRecords.map((r) => [`${r.accountId}-${r.productId}-${r.associateId}`, r]));
                const newRecords = [];
                const updatedRecords = [];
                for (const item of batch) {
                    const account = accountMap.get(item.account_code);
                    const product = productMap.get(item.product_code);
                    const encryptedAssociateDocument = item.associate_cpf && encryptedAssociateCpfMap.get(normalizeDocument(item.associate_cpf))
                        ? encryptedAssociateCpfMap.get(normalizeDocument(item.associate_cpf))
                        : encryptedAssociateCnpjMap.get(normalizeDocument(item.associate_cnpj));
                    const associate = associateMap.get(encryptedAssociateDocument);
                    const encryptedUserDocument = item.user_cpf && encryptedUserCpfMap.get(normalizeDocument(item.user_cpf))
                        ? encryptedUserCpfMap.get(normalizeDocument(item.user_cpf))
                        : encryptedUserCnpjMap.get(normalizeDocument(item.user_cnpj));
                    const user = userMap.get(encryptedUserDocument);
                    if (!account || !product || !associate || !user) {
                        errors.push({
                            record: item,
                            status: 'error',
                            message: [
                                !account ? `Account not found for account_code: ${item.account_code}` : '',
                                !product ? `Product not found for product_code: ${item.product_code}` : '',
                                !associate ? `Associate not found for given associate_cpf/associate_cnpj` : '',
                                !user ? `User not found for given user_cpf/user_cnpj` : '',
                            ]
                                .filter(Boolean)
                                .join('. '),
                        });
                        continue;
                    }
                    let recordKey = `${account.id}-${product.id}-${associate.id}`;
                    let record = existingRecordMap.get(recordKey);
                    if (record) {
                        if (item.product_value)
                            record.productValue = item.product_value;
                        if (item.quantity)
                            record.quantity = item.quantity;
                        if (item.value_or_quantity)
                            record.valueOrQuantity = item.value_or_quantity;
                        updatedRecords.push(record);
                    }
                    else {
                        const newRecord = this.repo.create({
                            accountId: account.id,
                            productId: product.id,
                            associateId: associate.id,
                            attendantId: user.id,
                            productValue: item.product_value,
                            quantity: item.quantity,
                            valueOrQuantity: item.value_or_quantity,
                        });
                        newRecords.push(newRecord);
                    }
                }
                await this.repo.manager.transaction(async (transactionalEntityManager) => {
                    if (newRecords.length > 0) {
                        await transactionalEntityManager.save(newRecords);
                    }
                    if (updatedRecords.length > 0) {
                        await transactionalEntityManager.save(updatedRecords);
                    }
                });
                processedRecords.push(...newRecords, ...updatedRecords);
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some records had errors' : 'Records processed successfully',
            processedRecords,
            errors,
        };
    }
};
exports.AttendanceProductsEffectiveService = AttendanceProductsEffectiveService;
exports.AttendanceProductsEffectiveService = AttendanceProductsEffectiveService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(attendance_products_effective_entity_1.AttendanceProductEffective)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.Accounts)),
    __param(4, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        users_service_1.UsersService,
        associates_service_1.AssociatesService,
        typeorm_2.Repository,
        cryptography_1.Cryptography,
        typeorm_2.DataSource])
], AttendanceProductsEffectiveService);
//# sourceMappingURL=attendance-products-effective.service.js.map