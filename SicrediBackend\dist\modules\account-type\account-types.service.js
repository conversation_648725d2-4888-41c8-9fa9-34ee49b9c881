"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountTypeService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const account_type_entity_1 = require("./entities/account-type.entity");
const account_entity_1 = require("../accounts/entities/account.entity");
let AccountTypeService = class AccountTypeService {
    constructor(accountTypeRepository, accountRepository) {
        this.accountTypeRepository = accountTypeRepository;
        this.accountRepository = accountRepository;
    }
    async createAccountTypeFromBulk(accountTypeDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!accountTypeDto || accountTypeDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (accountTypeDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many account types in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: accountTypeDto.length,
            });
        }
        const processedAccountTypes = [];
        const errors = [];
        for (let i = 0; i < accountTypeDto.length; i += BATCH_SIZE) {
            const batch = accountTypeDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.name)
                        missingFields.push(`name (index ${i + index})`);
                    if (!item.key)
                        missingFields.push(`key (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const keys = batch.map((item) => item.key);
                const existingAccountTypes = await this.findByKeys(keys);
                const accountTypeMap = new Map(existingAccountTypes.map((a) => [a.key, a]));
                const newAccountTypes = [];
                const updatedAccountTypes = [];
                for (const item of batch) {
                    let accountType = accountTypeMap.get(item.key);
                    if (accountType) {
                        if (item.name)
                            accountType.name = item.name;
                        updatedAccountTypes.push(accountType);
                    }
                    else {
                        const newAccountType = this.accountTypeRepository.create({
                            name: item.name,
                            key: item.key,
                        });
                        newAccountTypes.push(newAccountType);
                    }
                }
                await this.accountTypeRepository.manager.transaction(async (transactionalEntityManager) => {
                    if (newAccountTypes.length > 0) {
                        await transactionalEntityManager.save(newAccountTypes);
                    }
                    if (updatedAccountTypes.length > 0) {
                        await transactionalEntityManager.save(updatedAccountTypes);
                    }
                });
                processedAccountTypes.push(...newAccountTypes.map((a) => ({ id: a.id, name: a.name, key: a.key })), ...updatedAccountTypes.map((a) => ({ id: a.id, name: a.name, key: a.key })));
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some account types had errors' : 'Account types processed successfully',
            processedAccountTypes,
            errors,
        };
    }
    async deleteAccountTypesFromBulk(keys) {
        const MAX_BATCH_SIZE = 20;
        if (!keys || keys.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (keys.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many account types in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: keys.length,
            });
        }
        const processedAccountTypes = [];
        const errors = [];
        for (const key of keys) {
            try {
                const accountType = await this.accountTypeRepository.findOne({ where: { key } });
                if (!accountType) {
                    throw new common_1.NotFoundException(`Account type with key '${key}' not found.`);
                }
                const linkedAccounts = await this.accountRepository.find({
                    where: { accountTypeId: accountType.id },
                    select: ['id', 'code'],
                });
                if (linkedAccounts.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete account type '${key}' because it is linked to ${linkedAccounts.length} account(s).`,
                        linked_accounts: linkedAccounts,
                    });
                }
                accountType.deletedAt = new Date();
                await this.accountTypeRepository.save(accountType);
                processedAccountTypes.push({
                    id: accountType.id,
                    name: accountType.name,
                    key: accountType.key,
                    deleted_at: accountType.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    account_type_key: key,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some account types had errors' : 'Account types deleted successfully',
            processedAccountTypes,
            errors,
        };
    }
    async findOne(key) {
        const data = await this.accountTypeRepository
            .createQueryBuilder('account_type')
            .where("LOWER(account_type.key) LIKE LOWER(:key)", { key: `%${key}%` })
            .getOne();
        if (!data) {
            throw new common_1.NotFoundException(`Account type with key '${key}' not found`);
        }
        return {
            id: data.id,
            name: data.name,
            key: data.key,
        };
    }
    async findByKeys(keys) {
        if (!keys || keys.length === 0) {
            return [];
        }
        return this.accountTypeRepository.find({
            where: { key: (0, typeorm_2.In)(keys) },
        });
    }
};
exports.AccountTypeService = AccountTypeService;
exports.AccountTypeService = AccountTypeService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(account_type_entity_1.AccountType)),
    __param(1, (0, typeorm_1.InjectRepository)(account_entity_1.Accounts)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], AccountTypeService);
//# sourceMappingURL=account-types.service.js.map