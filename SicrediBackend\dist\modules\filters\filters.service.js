"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FiltersService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const segment_entity_1 = require("../segments/entities/segment.entity");
const agency_entity_1 = require("../agencies/entities/agency.entity");
const user_entity_1 = require("../users/entities/user.entity");
const wallet_entity_1 = require("../wallets/entities/wallet.entity");
const user_wallets_entity_1 = require("../user-wallets/entities/user-wallets.entity");
const attendance_status_entity_1 = require("../attendance-status/entities/attendance-status.entity");
let FiltersService = class FiltersService {
    constructor(segmentRepository, agencyRepository, userRepository, walletRepository, userWalletRepository, attendanceStatusRepository) {
        this.segmentRepository = segmentRepository;
        this.agencyRepository = agencyRepository;
        this.userRepository = userRepository;
        this.walletRepository = walletRepository;
        this.userWalletRepository = userWalletRepository;
        this.attendanceStatusRepository = attendanceStatusRepository;
    }
    async getSegments() {
        return this.segmentRepository.find();
    }
    async getAgencies(cooperativeId, userProfile) {
        if (userProfile === 'ADMIN') {
            return this.agencyRepository.find();
        }
        return this.agencyRepository.find({ where: { cooperativeId } });
    }
    async getUsers(agencyId, profile) {
        return this.userRepository.find({ where: { agencyId, profile: { key: profile } } });
    }
    async getWalletsForUser(user, dynamicFilters) {
        if (dynamicFilters?.userProfile === 'ASSISTANT' || (dynamicFilters?.assistantId && !dynamicFilters?.walletManagerId)) {
            return this.walletRepository.find({ where: { agencyId: dynamicFilters?.agencyId } });
        }
        const filterAttendantId = dynamicFilters?.walletManagerId || dynamicFilters?.assistantId || user.id;
        const subQuery = this.walletRepository.manager
            .getRepository(user_wallets_entity_1.UserWallet)
            .createQueryBuilder('user_wallet')
            .innerJoin('user_wallet.wallet', 'wallet')
            .select('wallet.id')
            .where('user_wallet.user_id = :filterAttendantId', { filterAttendantId })
            .getQuery();
        return this.walletRepository
            .createQueryBuilder('wallet')
            .where(`wallet.id IN (${subQuery})`)
            .setParameter('filterAttendantId', filterAttendantId)
            .getMany();
    }
    async getAllStatus() {
        return this.attendanceStatusRepository.find();
    }
};
exports.FiltersService = FiltersService;
exports.FiltersService = FiltersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(segment_entity_1.Segment)),
    __param(1, (0, typeorm_1.InjectRepository)(agency_entity_1.Agency)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, typeorm_1.InjectRepository)(wallet_entity_1.Wallet)),
    __param(4, (0, typeorm_1.InjectRepository)(user_wallets_entity_1.UserWallet)),
    __param(5, (0, typeorm_1.InjectRepository)(attendance_status_entity_1.AttendanceStatus)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], FiltersService);
//# sourceMappingURL=filters.service.js.map