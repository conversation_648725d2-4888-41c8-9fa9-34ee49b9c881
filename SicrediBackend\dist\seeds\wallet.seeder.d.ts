import { DataSource } from 'typeorm';
export declare class WalletSeeder {
    private readonly dataSource;
    constructor(dataSource: DataSource);
    executeSeed(): Promise<void>;
    private getWallet;
    private saveWallet;
    private seedInitialWallet;
    private seedInactive;
    private seedMinorAssociate;
    private seedCoHolder;
    private seedGuarantor;
    private seedSavings;
    private seedWage;
    private seedClosed;
    private seedPrejudice;
}
