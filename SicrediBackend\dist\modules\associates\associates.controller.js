"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociatesController = void 0;
const common_1 = require("@nestjs/common");
const associates_service_1 = require("./associates.service");
const create_associate_dto_1 = require("./dto/create-associate.dto");
const update_associate_dto_1 = require("./dto/update-associate.dto");
const swagger_1 = require("@nestjs/swagger");
const paginated_associate_dto_1 = require("./dto/paginated-associate.dto");
const user_decorator_1 = require("../../common/decorators/user.decorator");
const permission_decorator_1 = require("../../common/decorators/permission.decorator");
let AssociatesController = class AssociatesController {
    constructor(associatesService) {
        this.associatesService = associatesService;
    }
    create(createAssociateDto) {
        return this.associatesService.create(createAssociateDto);
    }
    findAllPaged(user, paginationParams) {
        return this.associatesService.findAllPaged(user, paginationParams);
    }
    findAll(user) {
        return this.associatesService.findAll(user);
    }
    findOne(id) {
        return this.associatesService.findOne(+id);
    }
    update(id, updateAssociateDto) {
        return this.associatesService.update(+id, updateAssociateDto);
    }
    remove(id) {
        return this.associatesService.remove(+id);
    }
    removeWalletUser(id, request) {
        return this.associatesService.removeWalletAssociate(+id);
    }
    async createBulk(createAssociatesDto) {
        if (!createAssociatesDto || createAssociatesDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.associatesService.createAssociatesFromBulk(createAssociatesDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
    getWalletsFromAssociates(id) {
        return this.associatesService.getWalletsFromAssociates(id);
    }
    async deleteBulk(body) {
        if (!body.cpfs || body.cpfs.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.associatesService.deleteAssociatesFromBulk(body.cpfs);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.AssociatesController = AssociatesController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo Associado' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Associado criado com sucesso.',
        type: create_associate_dto_1.CreateAssociateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_associate_dto_1.CreateAssociateDto]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Associados paginada' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Associados encontrada com sucesso.',
        type: create_associate_dto_1.CreateAssociateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhuma Carteira encontrada.' }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        type: Number,
        required: false,
        description: 'Número da página (default: 1)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        type: Number,
        required: false,
        description: 'Quantidade de registros por página (default: 10)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'associateName',
        type: String,
        required: false,
        description: 'Nome do associado',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'associateCpf',
        type: String,
        required: false,
        description: 'CPF do associado',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'associateCnpj',
        type: String,
        required: false,
        description: 'CNPJ do associado',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'associateId',
        type: Number,
        required: false,
        description: 'Identificador do Associado',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'agencyId',
        type: Number,
        required: false,
        description: 'Identificador da Agência',
    }),
    (0, common_1.Get)('paged'),
    __param(0, (0, user_decorator_1.User)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, paginated_associate_dto_1.PaginatedAssociateDto]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "findAllPaged", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar lista de Associados' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Lista de Associados encontrada com sucesso.',
        type: create_associate_dto_1.CreateAssociateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Associado encontrado.' }),
    (0, common_1.Get)(),
    __param(0, (0, user_decorator_1.User)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Associado pelo ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Associado encontrado com sucesso.',
        type: create_associate_dto_1.CreateAssociateDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Associado não encontrado.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Associado' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Associado atualizado com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Associado não encontrado.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_associate_dto_1.UpdateAssociateDto]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Associado' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Associado removido com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Associado não encontrado.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associates'),
    (0, swagger_1.ApiOperation)({ summary: 'Remove associado da carteira' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Associado removido da carteira com sucesso.',
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Associado não encontrado.' }),
    (0, common_1.Delete)('remove-wallet/:id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], AssociatesController.prototype, "removeWalletUser", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar múltiplos associados em massa',
        description: `
    Este endpoint permite criar novos associados ou atualizar registros já existentes.
    Se um CPF ou CNPJ já estiver cadastrado, os dados serão atualizados com as novas informações enviadas.
    Além disso, cada associado pode ter múltiplos números de telefone cadastrados.
    O número máximo permitido de associados por requisição é **20.000**, sendo processados em lotes de **500**.
  `,
    }),
    (0, swagger_1.ApiBody)({
        type: [create_associate_dto_1.CreateAssociateDto],
        description: 'Array de objetos contendo os dados dos associados a serem criados ou atualizados.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar ou atualizar múltiplos associados com múltiplos telefones',
                value: [
                    {
                        name: 'João Silva',
                        cpf: '12345678900',
                        cnpj: null,
                        email: '<EMAIL>',
                        income: 5000,
                        isAssociate: true,
                        birthDate: '1990-03-01',
                        preApprovedCredit: true,
                        phones: [
                            '11999999999',
                            '1133224455'
                        ]
                    },
                    {
                        name: 'Empresa XYZ',
                        cpf: null,
                        cnpj: '98765432000199',
                        email: '<EMAIL>',
                        income: 500000,
                        isAssociate: true,
                        birthDate: '2010-06-15',
                        preApprovedCredit: true,
                        phones: [
                            '11988887777'
                        ]
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Associados criados ou atualizados com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Associates processed successfully',
                processedAssociates: [
                    {
                        id: 1,
                        name: 'João Silva',
                        cpf: '12345678900',
                        cnpj: null,
                        email: '<EMAIL>',
                        income: 5000,
                        isAssociate: true,
                        birthDate: '1990-03-01',
                        preApprovedCredit: true,
                        phones: [
                            '11999999999',
                            '1133224455'
                        ]
                    },
                    {
                        id: 2,
                        name: 'Empresa XYZ',
                        cpf: null,
                        cnpj: '98765432000199',
                        email: '<EMAIL>',
                        income: 500000,
                        isAssociate: true,
                        birthDate: '2010-06-15',
                        preApprovedCredit: true,
                        phones: [
                            '11988887777'
                        ]
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: name, cpf or cnpj, email',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns associados foram processados com sucesso, mas outros tiveram erros.',
        schema: {
            example: {
                processedAssociates: [
                    {
                        id: 3,
                        name: 'Ana Souza',
                        cpf: '11122233344',
                        cnpj: null,
                        email: '<EMAIL>',
                        income: 7000,
                        isAssociate: false,
                        birthDate: '1995-08-10',
                        preApprovedCredit: true,
                        phones: [
                            '31999999999'
                        ]
                    },
                ],
                errors: [
                    {
                        associate: {
                            name: 'Carlos Ferreira',
                            cpf: '44455566677',
                            cnpj: null,
                            email: '<EMAIL>',
                            phones: [
                                '21999999999'
                            ],
                        },
                        status: 'error',
                        message: 'Associate CPF already registered: 44455566677',
                    },
                    {
                        associate: {
                            name: 'Empresa ABC',
                            cpf: null,
                            cnpj: '12345678000100',
                            email: '<EMAIL>',
                            phones: [
                                '11977776666'
                            ],
                        },
                        status: 'error',
                        message: 'Associate CNPJ already registered: 12345678000100',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AssociatesController.prototype, "createBulk", null);
__decorate([
    (0, common_1.Get)('account-wallet-by-id/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AssociatesController.prototype, "getWalletsFromAssociates", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Excluir múltiplos associados',
        description: `
      Este endpoint permite a exclusão de múltiplos associados em uma única requisição.
      Caso o associado esteja vinculado a contas, atendimentos, histórico de atendimentos ou produtos de propensão, a exclusão será impedida e um erro será retornado.
      A exclusão é feita via *soft delete*.
    `,
    }),
    (0, swagger_1.ApiBody)({
        description: 'Lista de CPFs dos associados a serem excluídos',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para deletar associados',
                value: {
                    cpfs: ['***********', '***********'],
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Associados excluídos com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Associates deleted successfully',
                processedAssociates: [
                    {
                        id: 1,
                        name: 'João Silva',
                        cpf: '***********',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns associados foram excluídos, mas outros apresentaram erro.',
        schema: {
            example: {
                status: 'partial_success',
                message: 'Some associates had errors',
                processedAssociates: [
                    {
                        id: 1,
                        name: 'João Silva',
                        cpf: '***********',
                        deleted_at: '2024-03-06T14:00:00Z',
                    },
                ],
                errors: [
                    {
                        cpf: '***********',
                        status: 'error',
                        message: "Cannot delete associate '***********' because it is linked to 2 accounts, 1 attendance, and 3 propensity products.",
                        linked_records: {
                            accounts: 2,
                            attendances: 1,
                            attendance_history: 0,
                            propensity_products: 3,
                        },
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
            },
        },
    }),
    (0, common_1.Delete)('delete/bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AssociatesController.prototype, "deleteBulk", null);
exports.AssociatesController = AssociatesController = __decorate([
    (0, permission_decorator_1.Permission)('associates'),
    (0, common_1.Controller)('/api/v1/associates'),
    __metadata("design:paramtypes", [associates_service_1.AssociatesService])
], AssociatesController);
//# sourceMappingURL=associates.controller.js.map