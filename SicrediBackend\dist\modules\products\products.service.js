"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const product_entity_1 = require("./entities/product.entity");
const attendance_products_effective_entity_1 = require("../attendance-products-effective/entities/attendance-products-effective.entity");
const attendance_product_entity_1 = require("../attendance-products/entities/attendance-product.entity");
const goal_product_wallet_entity_1 = require("../goal-product-wallet/entities/goal-product-wallet.entity");
const propensity_product_entity_1 = require("../propensity-products/entities/propensity-product.entity");
const strategy_product_entity_1 = require("../strategy-products/entities/strategy-product.entity");
let ProductsService = class ProductsService {
    constructor(productRepository, attendanceProductRepository, attendanceProductEffectiveRepository, goalProductWalletRepository, propensityProductRepository, strategyProductRepository) {
        this.productRepository = productRepository;
        this.attendanceProductRepository = attendanceProductRepository;
        this.attendanceProductEffectiveRepository = attendanceProductEffectiveRepository;
        this.goalProductWalletRepository = goalProductWalletRepository;
        this.propensityProductRepository = propensityProductRepository;
        this.strategyProductRepository = strategyProductRepository;
    }
    async create(createProductDto) {
        const newData = new product_entity_1.Product();
        newData.name = createProductDto.name;
        newData.description = createProductDto.description;
        newData.valueOrQuantity = createProductDto.valueOrQuantity;
        const response = await this.productRepository.save(newData);
        return {
            id: response.id,
            name: response.name,
            description: response.description,
            valueOrQuantity: response.valueOrQuantity,
        };
    }
    async findAll() {
        return await this.productRepository
            .createQueryBuilder('product')
            .select('product.id', 'id')
            .addSelect('product.name', 'name')
            .addSelect('product.description', 'description')
            .addSelect('product.value_or_quantity', 'valueOrQuantity')
            .where('product.deleted_at IS NULL')
            .execute();
    }
    async findOne(identifier) {
        const whereCondition = typeof identifier === 'number'
            ? { id: identifier, deletedAt: (0, typeorm_2.IsNull)() }
            : { code: identifier, deletedAt: (0, typeorm_2.IsNull)() };
        const data = await this.productRepository.findOneBy(whereCondition);
        if (!data) {
            throw new common_1.NotFoundException(`Product with ${typeof identifier === 'number' ? 'ID' : 'Code'} '${identifier}' not found`);
        }
        return data;
    }
    async update(id, updateProductDto) {
        await this.findOne(id);
        await this.productRepository.update(id, {
            name: updateProductDto.name,
            description: updateProductDto.description,
            updatedAt: new Date(),
        });
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            name: updated.name,
            description: updated.description,
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.productRepository.update(id, { deletedAt: new Date() });
    }
    async createProductFromBulk(productDto) {
        const MAX_BATCH_SIZE = 5000;
        const BATCH_SIZE = 500;
        if (!productDto || productDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (productDto.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many products in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: productDto.length,
            });
        }
        const processedProducts = [];
        const errors = [];
        for (let i = 0; i < productDto.length; i += BATCH_SIZE) {
            const batch = productDto.slice(i, i + BATCH_SIZE);
            try {
                const missingFields = [];
                batch.forEach((item, index) => {
                    if (!item.code)
                        missingFields.push(`code (index ${i + index})`);
                    if (!item.name)
                        missingFields.push(`name (index ${i + index})`);
                    if (!item.description)
                        missingFields.push(`description (index ${i + index})`);
                    if (!item.type)
                        missingFields.push(`type (index ${i + index})`);
                    if (item.valueOrQuantity === undefined)
                        missingFields.push(`value_or_quantity (index ${i + index})`);
                });
                if (missingFields.length > 0) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Missing required fields: ${missingFields.join(', ')}`,
                    });
                }
                const productCodes = batch.map((item) => item.code);
                const existingProducts = await this.productRepository.find({
                    where: { code: (0, typeorm_2.In)(productCodes) },
                });
                const productMap = new Map(existingProducts.map((p) => [p.code, p]));
                const newProducts = [];
                const updatedProducts = [];
                for (const item of batch) {
                    let product = productMap.get(item.code);
                    if (product) {
                        if (item.newName)
                            product.name = item.newName;
                        if (item.description)
                            product.description = item.description;
                        if (item.type)
                            product.type = item.type;
                        if (item.valueOrQuantity !== undefined)
                            product.valueOrQuantity = item.valueOrQuantity;
                        updatedProducts.push(product);
                    }
                    else {
                        const newProduct = this.productRepository.create({
                            name: item.name,
                            code: item.code,
                            description: item.description,
                            type: item.type,
                            valueOrQuantity: item.valueOrQuantity,
                        });
                        newProducts.push(newProduct);
                    }
                }
                await this.productRepository.manager.transaction(async (transactionalEntityManager) => {
                    if (newProducts.length > 0) {
                        await transactionalEntityManager.save(newProducts);
                    }
                    if (updatedProducts.length > 0) {
                        await transactionalEntityManager.save(updatedProducts);
                    }
                });
                processedProducts.push(...newProducts.map((p) => ({
                    id: p.id,
                    name: p.name,
                    code: p.code,
                    description: p.description,
                    type: p.type,
                    valueOrQuantity: p.valueOrQuantity,
                })), ...updatedProducts.map((p) => ({
                    id: p.id,
                    name: p.name,
                    code: p.code,
                    description: p.description,
                    type: p.type,
                    valueOrQuantity: p.valueOrQuantity,
                })));
            }
            catch (error) {
                errors.push({
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some products had errors' : 'Products processed successfully',
            processedProducts,
            errors,
        };
    }
    async deleteProductsFromBulk(productCodes) {
        const MAX_BATCH_SIZE = 20;
        if (!productCodes || productCodes.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        if (productCodes.length > MAX_BATCH_SIZE) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: `Too many products in a single request. Maximum allowed is ${MAX_BATCH_SIZE}.`,
                received: productCodes.length,
            });
        }
        const processedProducts = [];
        const errors = [];
        for (const productCode of productCodes) {
            try {
                const product = await this.productRepository.findOne({ where: { code: productCode } });
                if (!product) {
                    throw new common_1.NotFoundException(`Product with code '${productCode}' not found.`);
                }
                const linkedAttendances = await this.attendanceProductRepository.count({ where: { productId: product.id } });
                const linkedAttendancesEffective = await this.attendanceProductEffectiveRepository.count({ where: { productId: product.id } });
                const linkedGoalProducts = await this.goalProductWalletRepository.count({ where: { productId: product.id } });
                const linkedPropensityProducts = await this.propensityProductRepository.count({ where: { productId: product.id } });
                const linkedStrategyProducts = await this.strategyProductRepository.count({ where: { productId: product.id } });
                const linkedData = {
                    attendance_products: linkedAttendances,
                    attendance_products_effective: linkedAttendancesEffective,
                    goal_products: linkedGoalProducts,
                    propensity_products: linkedPropensityProducts,
                    strategy_products: linkedStrategyProducts,
                };
                if (Object.values(linkedData).some(count => count > 0)) {
                    throw new common_1.BadRequestException({
                        status: 'error',
                        message: `Cannot delete product '${productCode}' because it is linked to other entities.`,
                        linked_records: linkedData,
                    });
                }
                product.deletedAt = new Date();
                await this.productRepository.save(product);
                processedProducts.push({
                    id: product.id,
                    code: product.code,
                    name: product.name,
                    deleted_at: product.deletedAt,
                });
            }
            catch (error) {
                errors.push({
                    product_code: productCode,
                    status: 'error',
                    message: error.message || 'Unexpected error occurred',
                });
            }
        }
        return {
            status: errors.length > 0 ? 'partial_success' : 'success',
            message: errors.length > 0 ? 'Some products had errors' : 'Products deleted successfully',
            processedProducts,
            errors,
        };
    }
};
exports.ProductsService = ProductsService;
exports.ProductsService = ProductsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(1, (0, typeorm_1.InjectRepository)(attendance_product_entity_1.AttendanceProduct)),
    __param(2, (0, typeorm_1.InjectRepository)(attendance_products_effective_entity_1.AttendanceProductEffective)),
    __param(3, (0, typeorm_1.InjectRepository)(goal_product_wallet_entity_1.GoalProductWallet)),
    __param(4, (0, typeorm_1.InjectRepository)(propensity_product_entity_1.PropensityProduct)),
    __param(5, (0, typeorm_1.InjectRepository)(strategy_product_entity_1.StrategyProduct)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ProductsService);
//# sourceMappingURL=products.service.js.map