"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WalletRangeValueSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const wallet_range_value_entity_1 = require("../modules/wallet-range-values/entities/wallet-range-value.entity");
const segment_entity_1 = require("../modules/segments/entities/segment.entity");
const agency_entity_1 = require("../modules/agencies/entities/agency.entity");
let WalletRangeValueSeeder = class WalletRangeValueSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedWalletRanges();
    }
    async seedWalletRanges() {
        const walletRangeValueRepository = this.dataSource.getRepository(wallet_range_value_entity_1.WalletRangeValue);
        const segmentRepository = this.dataSource.getRepository(segment_entity_1.Segment);
        const agencyRepository = this.dataSource.getRepository(agency_entity_1.Agency);
        const walletRanges = [
            {
                name: 'Menor ou igual à R$ 2.000,00',
                number: '01',
                numberOld: '1',
                segment: '01',
                agency: '0001',
            },
            {
                name: 'R$ 2.000,01 a R$ 3.999,99',
                number: '02',
                numberOld: '2',
                segment: '01',
                agency: '0001',
            },
            {
                name: 'R$ 4.000,00 a R$ 7.999,99',
                number: '03',
                numberOld: '3',
                segment: '01',
                agency: '0001',
            },
            {
                name: 'Maior ou igual à R$ 8.000,00',
                number: '04',
                numberOld: '4',
                segment: '01',
                agency: '0001',
            },
            {
                name: 'Menor ou igual R$ 500.000,00',
                number: '01',
                numberOld: '1',
                segment: '02',
                agency: '0001',
            },
            {
                name: 'Entre R$ 500.000,01 e R$ 1.599.999,99',
                number: '02',
                numberOld: '2',
                segment: '02',
                agency: '0001',
            },
            {
                name: 'Maior ou igual a R$ 1.600.000,00',
                number: '03',
                numberOld: '3',
                segment: '02',
                agency: '0001',
            },
            {
                name: 'Menor ou igual R$ 360.000,00',
                number: '01',
                numberOld: '1',
                segment: '03',
                agency: '0001',
            },
            {
                name: 'Entre R$ 360.000,01 e R$ 999.999,99',
                number: '02',
                numberOld: '2',
                segment: '03',
                agency: '0001',
            },
            {
                name: 'Entre R$ 1.000.000,00 e R$ 5.999.999,99',
                number: '03',
                numberOld: '3',
                segment: '03',
                agency: '0001',
            },
            {
                name: 'Maior ou igual à R$ 6.000.000,00',
                number: '04',
                numberOld: '4',
                segment: '03',
                agency: '0001',
            },
        ];
        for (const range of walletRanges) {
            let segment = await segmentRepository.findOne({
                where: { number: range.segment },
            });
            let agency = await agencyRepository.findOne({
                where: { agencyCode: range.agency },
            });
            if (!segment || !agency)
                continue;
            const existing = await walletRangeValueRepository.findOne({
                where: {
                    name: range.name,
                    number: range.number,
                    segmentId: segment.id,
                    agencyId: agency.id,
                },
            });
            if (!existing) {
                const walletRange = new wallet_range_value_entity_1.WalletRangeValue();
                walletRange.name = range.name;
                walletRange.number = range.number;
                walletRange.numberOld = range.numberOld;
                walletRange.segmentId = segment.id;
                walletRange.agencyId = agency.id;
                await walletRangeValueRepository.save(walletRange);
            }
        }
    }
};
exports.WalletRangeValueSeeder = WalletRangeValueSeeder;
exports.WalletRangeValueSeeder = WalletRangeValueSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], WalletRangeValueSeeder);
//# sourceMappingURL=wallet-range-value.seeder.js.map