"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const cryptography_1 = require("../../common/functions/cryptography");
const account_entity_1 = require("./entities/account.entity");
const accounts_service_1 = require("./accounts.service");
const accounts_controller_1 = require("./accounts.controller");
const associates_module_1 = require("../associates/associates.module");
const agencies_module_1 = require("../agencies/agencies.module");
const account_types_module_1 = require("../account-type/account-types.module");
const attendance_entity_1 = require("../attendances/entities/attendance.entity");
const account_wallets_entity_1 = require("../account-wallets/entities/account-wallets.entity");
let AccountsModule = class AccountsModule {
};
exports.AccountsModule = AccountsModule;
exports.AccountsModule = AccountsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([account_entity_1.Accounts, attendance_entity_1.Attendance, account_wallets_entity_1.AccountWallets]),
            (0, common_1.forwardRef)(() => associates_module_1.AssociatesModule),
            (0, common_1.forwardRef)(() => agencies_module_1.AgenciesModule),
            (0, common_1.forwardRef)(() => account_types_module_1.AccountTypeModule)
        ],
        controllers: [accounts_controller_1.AccountsController],
        providers: [accounts_service_1.AccountsService, cryptography_1.Cryptography],
        exports: [accounts_service_1.AccountsService, typeorm_1.TypeOrmModule],
    })
], AccountsModule);
//# sourceMappingURL=accounts.module.js.map