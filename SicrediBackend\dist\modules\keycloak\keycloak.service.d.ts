import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
export declare class KeycloakService {
    private readonly configService;
    private readonly http;
    private readonly keycloakUrl;
    private readonly keycloakRealm;
    private readonly keycloakAdminClientId;
    private readonly keycloakAdminLogin;
    private readonly keycloakAdminPassword;
    private readonly keycloakAdminRealm;
    constructor(configService: ConfigService, http: HttpService);
    get({ token, url, }: {
        token: string;
        url: string;
    }): Promise<{
        data: any;
        status: number;
    }>;
    post({ token, url, values, }: {
        token: string;
        url: string;
        values: any;
    }): Promise<{
        data: any;
        status: number;
    }>;
    put({ token, url, values, }: {
        token: string;
        url: string;
        values: any;
    }): Promise<{
        data: any;
        status: number;
    }>;
    delete({ token, url, }: {
        token: string;
        url: string;
    }): Promise<{
        data: any;
        status: number;
    }>;
    createUser({ userName, password, email, firstName, lastName, token, }: {
        userName: string;
        password: string;
        email: string;
        firstName: string;
        lastName: string;
        token: string;
    }): Promise<boolean>;
    usersList({ token }: {
        token: string;
    }): Promise<[]>;
    updateUser({ email, firstName, token, idKeycloak, }: {
        email: string;
        firstName: string;
        token: string;
        idKeycloak: string;
    }): Promise<boolean>;
    inactiveUser({ token, idKeycloak, }: {
        token: string;
        idKeycloak: string;
    }): Promise<boolean>;
    adminToken(): Promise<{
        data: any;
        status: number;
    }>;
    adminCreateRealm({ token, }: {
        token: string;
    }): Promise<{
        data: any;
        status: number;
    }>;
    deleteUser({ token, idKeycloak, }: {
        token: string;
        idKeycloak: string;
    }): Promise<boolean>;
}
