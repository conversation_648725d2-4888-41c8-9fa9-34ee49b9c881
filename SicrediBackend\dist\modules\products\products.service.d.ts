import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { Repository } from 'typeorm';
import { Product } from './entities/product.entity';
import { AttendanceProductEffective } from '../attendance-products-effective/entities/attendance-products-effective.entity';
import { AttendanceProduct } from '../attendance-products/entities/attendance-product.entity';
import { GoalProductWallet } from '../goal-product-wallet/entities/goal-product-wallet.entity';
import { PropensityProduct } from '../propensity-products/entities/propensity-product.entity';
import { StrategyProduct } from '../strategy-products/entities/strategy-product.entity';
export declare class ProductsService {
    private readonly productRepository;
    private readonly attendanceProductRepository;
    private readonly attendanceProductEffectiveRepository;
    private readonly goalProductWalletRepository;
    private readonly propensityProductRepository;
    private readonly strategyProductRepository;
    constructor(productRepository: Repository<Product>, attendanceProductRepository: Repository<AttendanceProduct>, attendanceProductEffectiveRepository: Repository<AttendanceProductEffective>, goalProductWalletRepository: Repository<GoalProductWallet>, propensityProductRepository: Repository<PropensityProduct>, strategyProductRepository: Repository<StrategyProduct>);
    create(createProductDto: CreateProductDto): Promise<CreateProductDto>;
    findAll(): Promise<CreateProductDto[]>;
    findOne(identifier: number | string): Promise<CreateProductDto>;
    update(id: number, updateProductDto: UpdateProductDto): Promise<UpdateProductDto>;
    remove(id: number): Promise<void>;
    createProductFromBulk(productDto: CreateProductDto[]): Promise<any>;
    deleteProductsFromBulk(productCodes: string[]): Promise<any>;
}
