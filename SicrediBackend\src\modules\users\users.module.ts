import { Lo<PERSON>, Module,forwardRef } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { KeycloakService } from 'src/modules/keycloak/keycloak.service';
import { ProfilesService } from 'src/modules/profiles/profiles.service';
import { HttpModule } from '@nestjs/axios';
import { Cryptography } from 'src/common/functions/cryptography';
import { ProfilePermissionsModule } from '../profile-permissions/profile-permissions.module';
import { ProfilePermissionsService } from '../profile-permissions/profile-permissions.service';
import { ProfilePermission } from '../profile-permissions/entities/profile-permission.entity';
import { ProfilesModule } from '../profiles/profiles.module';
import { CooperativesModule } from '../cooperatives/cooperatives.module';
import { Cooperative } from '../cooperatives/entities/cooperative.entity';
import { Profile } from '../profiles/entities/profile.entity';
import { CooperativesService } from 'src/modules/cooperatives/cooperatives.service';
import { AgenciesModule } from '../agencies/agencies.module';
import { CentralsModule } from '../centrals/centrals.module';
import { FederationsModule } from '../federations/federations.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([User, ProfilePermission, Profile]),
    forwardRef(() => CooperativesModule),
    forwardRef(() => AgenciesModule),
    forwardRef(() => CentralsModule),
    forwardRef(() => FederationsModule),
    HttpModule,
    ProfilePermissionsModule,
    ProfilesModule,
  ],
  controllers: [UsersController],
  providers: [
    UsersService,
    Logger,
    KeycloakService,
    ProfilesService,
    Cryptography,
    ProfilePermissionsService
  ],
  exports: [UsersService, Logger],
})
export class UsersModule {}
