"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SegmentSeeder = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
const segment_entity_1 = require("../modules/segments/entities/segment.entity");
let SegmentSeeder = class SegmentSeeder {
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async onModuleInit() {
        await this.seedIndividual();
        await this.seedIndividualAgro();
        await this.seedSicrediCompanies();
    }
    async getSegment(name, number, segmentRepository) {
        const existing = await segmentRepository.findOne({
            where: { name, number },
        });
        return existing;
    }
    async saveSegment(existing, name, number, numberOld, segmentRepository) {
        if (!existing) {
            const segment = new segment_entity_1.Segment();
            segment.name = name;
            segment.number = number;
            segment.numberOld = numberOld;
            return await segmentRepository.save(segment);
        }
        return false;
    }
    async seedIndividual() {
        const segmentRepository = this.dataSource.getRepository(segment_entity_1.Segment);
        const existingCreate = await this.getSegment('Pessoa Física', '01', segmentRepository);
        await this.saveSegment(existingCreate, 'Pessoa Física', '01', '1', segmentRepository);
    }
    async seedIndividualAgro() {
        const segmentRepository = this.dataSource.getRepository(segment_entity_1.Segment);
        const existingCreate = await this.getSegment('Pessoa Física AGRO', '02', segmentRepository);
        await this.saveSegment(existingCreate, 'Pessoa Física AGRO', '02', '2', segmentRepository);
    }
    async seedSicrediCompanies() {
        const segmentRepository = this.dataSource.getRepository(segment_entity_1.Segment);
        const existingCreate = await this.getSegment('Sicredi Empresas', '03', segmentRepository);
        await this.saveSegment(existingCreate, 'Sicredi Empresas', '03', '3', segmentRepository);
    }
};
exports.SegmentSeeder = SegmentSeeder;
exports.SegmentSeeder = SegmentSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], SegmentSeeder);
//# sourceMappingURL=segment.seeder.js.map