"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SchedulesService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const schedule_entity_1 = require("./entities/schedule.entity");
const cryptography_1 = require("../../common/functions/cryptography");
let SchedulesService = class SchedulesService {
    constructor(scheduleRepository, cryptography) {
        this.scheduleRepository = scheduleRepository;
        this.cryptography = cryptography;
    }
    async create(userId, createScheduleDto) {
        const newData = new schedule_entity_1.Schedule();
        newData.userId = userId;
        newData.attendanceId = createScheduleDto.attendanceId;
        newData.description = createScheduleDto.description;
        newData.title = createScheduleDto.title;
        newData.scheduleAt = new Date(createScheduleDto.scheduleAt);
        newData.scheduleAt.setHours(newData.scheduleAt.getHours());
        const response = await this.scheduleRepository.save(newData);
        return {
            id: response.id,
            userId: userId,
            attendanceId: response.attendanceId,
            description: response.description,
            scheduleAt: response.scheduleAt.toISOString(),
            title: response.title,
        };
    }
    async findAll(idAttendance) {
        const query = this.scheduleRepository
            .createQueryBuilder('schedule')
            .select('schedule.id', 'id')
            .addSelect('schedule.attendance_id', 'attendanceId')
            .addSelect('schedule.description', 'description')
            .addSelect('schedule.title', 'title')
            .addSelect('schedule.schedule_at', 'schedule_at')
            .addSelect('schedule.user_id', 'user_id')
            .where('schedule.deleted_at IS NULL');
        if (idAttendance) {
            query.andWhere('schedule.attendance_id = :idAttendance', {
                idAttendance,
            });
        }
        const data = await query.execute();
        return data;
    }
    async findOne(id) {
        const data = await this.scheduleRepository
            .createQueryBuilder('schedule')
            .leftJoin('schedule.attendance', 'attendance')
            .leftJoin('attendance.associate', 'associate')
            .leftJoin('attendance.attendant', 'attendant')
            .leftJoin('attendant.profile', 'profile')
            .leftJoin('attendant.agency', 'attendant_agency')
            .leftJoin('schedule.user', 'owner')
            .leftJoin('owner.profile', 'owner_profile')
            .leftJoin('associate.phones', 'associatePhones')
            .select([
            'schedule.id',
            'schedule.description',
            'schedule.title',
            'schedule.scheduleAt',
            'schedule.createdAt',
            'attendance.id',
            'associate.id',
            'associate.name',
            'associate.email',
            'associatePhones.phone',
            'associate.cpf',
            'associate.cnpj',
            'attendant.id',
            'attendant.name',
            'attendant.email',
            'attendant.phone',
            'attendant.cpf',
            'profile.name',
            'profile.description',
            'profile.key',
            'profile.hierarchy',
            'attendant_agency.name AS attendant_agency_name',
            'attendant_agency.agency_code AS attendant_agency_code',
            'owner.id',
            'owner.name',
            'owner.email',
            'owner.phone',
            'owner.cpf',
            'owner_profile.name',
            'owner_profile.description',
            'owner_profile.key',
            'owner_profile.hierarchy',
        ])
            .where('schedule.id = :id', { id })
            .andWhere('schedule.deletedAt IS NULL')
            .getRawOne();
        if (!data) {
            throw new common_1.NotFoundException(`Schedule with ID ${id} not found`);
        }
        const formattedData = {
            id: data['schedule_id'],
            description: data['schedule_description'],
            title: data['schedule_title'],
            scheduleAt: data['schedule_schedule_at'],
            createdAt: data['schedule_created_at'],
            attendance: data['attendance_id']
                ? {
                    id: data['attendance_id'],
                    associate: {
                        id: data['associate_id'],
                        name: data['associate_name'],
                        email: this.cryptography.decrypt(data['associate_email']),
                        phone: data['associate_phone'],
                        cpf: data['associate_cpf'],
                    },
                    attendant: {
                        id: data['attendant_id'],
                        name: data['attendant_name'],
                        email: this.cryptography.decrypt(data['attendant_email']),
                        phone: this.cryptography.decrypt(data['attendant_phone']),
                        cpf: this.cryptography.decrypt(data['attendant_cpf']),
                        profile: {
                            name: data['profile_name'],
                            description: data['profile_description'],
                            key: data['profile_key'],
                            hierarchy: data['profile_hierarchy'],
                        },
                        agency: {
                            name: data['attendant_agency_name'],
                            code: data['attendant_agency_code'],
                        },
                    },
                }
                : null,
            owner: !data['attendance_id']
                ? {
                    id: data['owner_id'],
                    name: data['owner_name'],
                    phone: this.cryptography.decrypt(data['owner_phone']),
                    email: this.cryptography.decrypt(data['owner_email']),
                    cpf: this.cryptography.decrypt(data['owner_cpf']),
                    profile: {
                        name: data['owner_profile_name'],
                        description: data['owner_profile_description'],
                        key: data['owner_profile_key'],
                        hierarchy: data['owner_profile_hierarchy'],
                    },
                }
                : null,
        };
        return formattedData;
    }
    async update(id, updateScheduleDto) {
        await this.findOne(id);
        await this.scheduleRepository.update(id, {
            attendanceId: updateScheduleDto.attendanceId,
            description: updateScheduleDto.description,
            title: updateScheduleDto.title,
            scheduleAt: updateScheduleDto.scheduleAt
                ? new Date(updateScheduleDto.scheduleAt)
                : undefined,
            updatedAt: new Date(),
        });
        const updated = await this.findOne(id);
        return {
            id: updated.id,
            attendanceId: updated.attendanceId,
            description: updated.description,
            title: updated.title,
            scheduleAt: updated.scheduleAt.toISOString(),
        };
    }
    async remove(id) {
        await this.findOne(id);
        await this.scheduleRepository.update(id, { deletedAt: new Date() });
    }
    async findPaginated(user, query) {
        const userId = user.id;
        const userProfileKey = user.profile.key;
        const userAgencyId = user.agencyId;
        const userCooperativeId = user.cooperativeId;
        const userCentralId = user.centralId;
        const userFederationId = user.federationId;
        const { page, limit, scheduleAt, startDate, endDate, scheduleDescription, associateId, associateName, associateCpf, associateCnpj, attendantName, attendantCpf, attendantId, profileKey, agencyId, } = query;
        const normalizeCPF = (cpf) => cpf.replace(/\D/g, '');
        const isPagination = page && limit;
        const validPage = isPagination ? Math.max(1, Number(page)) : 1;
        const validLimit = isPagination ? Math.max(1, Number(limit)) : null;
        const offset = isPagination && validLimit ? (validPage - 1) * validLimit : 0;
        const queryBuilder = this.scheduleRepository
            .createQueryBuilder('schedule')
            .leftJoin('schedule.attendance', 'attendance')
            .leftJoin('attendance.associate', 'associate')
            .leftJoin('associate.account', 'account', `account.associate_id = associate.id and account.id = attendance.account_id`)
            .leftJoin('account.accountWallets', 'accountWallet')
            .leftJoin('accountWallet.wallet', 'wallet')
            .leftJoin('wallet.user', 'walletUser')
            .leftJoin('schedule.user', 'owner')
            .leftJoin('associate.phones', 'associatePhones')
            .leftJoin('owner.agency', 'ownerAgency')
            .leftJoin('walletUser.agency', 'walletUserAgency')
            .leftJoin('ownerAgency.cooperative', 'ownerCooperative')
            .leftJoin('walletUserAgency.cooperative', 'walletUserCooperative')
            .leftJoin('ownerCooperative.central', 'ownerCentral')
            .leftJoin('walletUserCooperative.central', 'walletUserCentral')
            .leftJoin('ownerCentral.federation', 'ownerFederation')
            .leftJoin('walletUserCentral.federation', 'walletUserFederation')
            .leftJoin('owner.profile', 'owner_profile')
            .select([
            'schedule.id AS schedule_id',
            'schedule.description AS schedule_description',
            'schedule.title AS schedule_title',
            'schedule.scheduleAt AS schedule_schedule_at',
            'schedule.createdAt AS schedule_created_at',
            'attendance.id AS attendance_id',
            'associate.id AS associate_id',
            'associate.name AS associate_name',
            'associate.email AS associate_email',
            'associate.cpf AS associate_cpf',
            'associate.cnpj AS associate_cnpj',
            'associatePhones.phone AS associate_phone',
            'wallet.id AS wallet_id',
            'walletUser.agencyId AS wallet_user_agency_id',
            'walletUser.id AS wallet_user_id',
            'walletUser.name AS wallet_user_name',
            'walletUser.email AS wallet_user_email',
            'walletUser.phone AS wallet_user_phone',
            'walletUser.cpf AS wallet_user_cpf',
            'owner.id AS owner_id',
            'owner.name AS owner_name',
            'owner.email AS owner_email',
            'owner.phone AS owner_phone',
            'owner.cpf AS owner_cpf',
            'owner_profile.name AS owner_profile_name',
            'owner_profile.description AS owner_profile_description',
            'owner_profile.key AS owner_profile_key',
            'owner_profile.hierarchy AS owner_profile_hierarchy',
            'ownerAgency.name AS owner_agency_name',
            'ownerAgency.agency_code AS owner_agency_code',
            'ownerAgency.cooperativeId AS ownerAgency_cooperative_id',
            'walletUserAgency.cooperativeId AS walletUserAgency_cooperative_id',
            'walletUserAgency.name AS wallet_user_agency_name',
            'walletUserAgency.agency_code AS wallet_user_agency_code',
            'ownerCentral.id AS ownerCentral_id',
            'walletUserCentral.id AS walletUserCentral_id',
            'ownerFederation.id AS ownerFederation_id',
            'walletUserFederation.id AS walletUserFederation_id',
        ])
            .where('schedule.deletedAt IS NULL');
        if (userProfileKey === 'FEDERATION') {
            queryBuilder.andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('walletUserFederation.id = :federationId', {
                    federationId: userFederationId,
                })
                    .orWhere('schedule.userId = :userId AND attendance.id IS NULL', {
                    userId,
                });
            }));
        }
        if (userProfileKey === 'CENTRAL') {
            queryBuilder.andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('walletUserCentral.id = :centralId', {
                    centralId: userCentralId,
                })
                    .orWhere('schedule.userId = :userId AND attendance.id IS NULL', {
                    userId,
                });
            }));
        }
        if (userProfileKey === 'ASSISTANT' || userProfileKey === 'WALLET_MANAGER') {
            queryBuilder.andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('walletUser.id = :userId', { userId })
                    .orWhere('owner.id = :userId', { userId })
                    .orWhere('schedule.userId = :userId AND attendance.id IS NULL', {
                    userId,
                });
            }));
        }
        if (userProfileKey === 'AGENCY_MANAGER' ||
            userProfileKey === 'OPERATIONAL_ADMINISTRATIVE_MANAGER') {
            queryBuilder
                .where('walletUser.agencyId = :userAgencyId', { userAgencyId })
                .orWhere('owner.agencyId = :userAgencyId', { userAgencyId });
        }
        if (userProfileKey === 'COOPERATIVE') {
            queryBuilder.andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('walletUserAgency.cooperativeId = :coopId', {
                    coopId: userCooperativeId,
                })
                    .orWhere('schedule.userId = :userId AND attendance.id IS NULL', {
                    userId,
                });
            }));
        }
        if (!attendantName && !attendantCpf && !attendantId) {
            queryBuilder.andWhere(new typeorm_2.Brackets((qb) => {
                qb.where('attendance.id IS NOT NULL');
                qb.orWhere('schedule.userId = :userId AND attendance.id IS NULL', {
                    userId,
                });
            }));
        }
        else {
            queryBuilder.andWhere('attendance.id IS NOT NULL');
        }
        if (profileKey && profileKey.length > 0) {
            queryBuilder.andWhere('owner_profile.key IN (:...profileKeys)', {
                profileKeys: profileKey,
            });
        }
        if (scheduleAt) {
            queryBuilder.andWhere('schedule.scheduleAt = :scheduleAt', {
                scheduleAt,
            });
        }
        if (startDate && endDate) {
            queryBuilder.andWhere('schedule.scheduleAt BETWEEN :startDate AND :endDate', {
                startDate,
                endDate,
            });
        }
        else if (startDate) {
            queryBuilder.andWhere('schedule.scheduleAt >= :startDate', { startDate });
        }
        else if (endDate) {
            queryBuilder.andWhere('schedule.scheduleAt <= :endDate', { endDate });
        }
        if (scheduleDescription) {
            queryBuilder.andWhere('schedule.description LIKE :scheduleDescription', {
                scheduleDescription: `%${scheduleDescription}%`,
            });
        }
        if (associateName) {
            queryBuilder.andWhere('LOWER(associate.name) LIKE :associateName', {
                associateName: `%${associateName.toLowerCase()}%`,
            });
        }
        if (associateCpf) {
            const associateCpfEncrypt = this.cryptography.encrypt(associateCpf);
            queryBuilder.andWhere('associate.cpf = :associateCpfEncrypt', {
                associateCpfEncrypt,
            });
        }
        if (associateCnpj) {
            const associateCnpjEncrypt = this.cryptography.encrypt(associateCnpj);
            queryBuilder.andWhere('associate.cnpj = :associateCnpjEncrypt', {
                associateCnpjEncrypt,
            });
        }
        const normalizedAttendantName = attendantName?.toLowerCase().trim();
        if (attendantName) {
            queryBuilder.andWhere('LOWER(walletUser.name) LIKE :attendantName', {
                attendantName: `%${normalizedAttendantName}%`,
            });
        }
        if (attendantCpf) {
            const normalizedCpf = normalizeCPF(attendantCpf);
            const encryptedCpf = this.cryptography.encrypt(normalizedCpf.trim());
            queryBuilder.andWhere(' .cpf = :encryptedCpf', { encryptedCpf });
        }
        if (attendantId) {
            queryBuilder.andWhere('(walletUser.id = :attendantId or attendance.attendant_id = :attendantId)', {
                attendantId,
            });
        }
        if (associateId) {
            queryBuilder.andWhere('associate.id = :associateId', { associateId });
        }
        if (agencyId) {
            queryBuilder
                .where('walletUser.agencyId = :agencyId', { agencyId })
                .orWhere('owner.agencyId = :agencyId', { agencyId });
        }
        if (isPagination && validLimit) {
            queryBuilder.offset(offset).limit(validLimit);
        }
        const totalItems = await queryBuilder.getCount();
        const results = await queryBuilder.getRawMany();
        const items = results.reduce((acc, data) => {
            const existingItem = acc.find((item) => item.id === data.schedule_id);
            if (existingItem) {
                if (data.associate_phone) {
                    const decryptedPhone = this.cryptography.decrypt(data.associate_phone);
                    if (!existingItem.attendance.associate.phones.includes(decryptedPhone)) {
                        existingItem.attendance.associate.phones.push(decryptedPhone);
                    }
                }
            }
            else {
                acc.push({
                    id: data.schedule_id || '',
                    description: data.schedule_description || '',
                    title: data.schedule_title || '',
                    scheduleAt: data.schedule_schedule_at || '',
                    createdAt: data.schedule_created_at || '',
                    attendance: {
                        id: data.attendance_id || null,
                        associate: data.attendance_id
                            ? {
                                id: data.associate_id || null,
                                name: data.associate_name || '',
                                email: data.associate_email
                                    ? this.cryptography.decrypt(data.associate_email)
                                    : '',
                                cpf: data.associate_cpf || '',
                                cnpj: data.associate_cnpj || '',
                                phones: data.associate_phone
                                    ? [this.cryptography.decrypt(data.associate_phone)]
                                    : [],
                            }
                            : {},
                        attendant: data.wallet_user_id || data.owner_id
                            ? {
                                id: data.wallet_user_id || data.owner_id,
                                name: data.wallet_user_name || data.owner_name,
                                email: data.wallet_user_email || data.owner_email
                                    ? this.cryptography.decrypt(data.wallet_user_email || data.owner_email)
                                    : '',
                                cpf: data.wallet_user_cpf || data.owner_cpf
                                    ? this.cryptography.decrypt(data.wallet_user_cpf || data.owner_cpf)
                                    : '',
                                phone: data.wallet_user_phone || data.owner_phone
                                    ? this.cryptography.decrypt(data.wallet_user_phone || data.owner_phone)
                                    : '',
                                profile: {
                                    name: data.owner_profile_name || '',
                                    description: data.owner_profile_description || '',
                                    key: data.owner_profile_key || '',
                                    hierarchy: data.owner_profile_hierarchy || '',
                                },
                                agency: {
                                    name: data.wallet_user_agency_name ||
                                        data.owner_agency_name ||
                                        '',
                                    code: data.wallet_user_agency_code ||
                                        data.owner_agency_code ||
                                        '',
                                },
                            }
                            : {},
                    },
                    owner: !data.attendance_id
                        ? {
                            id: data.owner_id || null,
                            name: data.owner_name || '',
                            email: data.owner_email
                                ? this.cryptography.decrypt(data.owner_email)
                                : '',
                            phone: data.owner_phone
                                ? this.cryptography.decrypt(data.owner_phone)
                                : '',
                            cpf: data.owner_cpf
                                ? this.cryptography.decrypt(data.owner_cpf)
                                : '',
                            profile: {
                                name: data.owner_profile_name || '',
                                description: data.owner_profile_description || '',
                                key: data.owner_profile_key || '',
                                hierarchy: data.owner_profile_hierarchy || '',
                            },
                        }
                        : null,
                });
            }
            return acc;
        }, []);
        return {
            items,
            totalItems,
            totalPages: isPagination && validLimit ? Math.ceil(totalItems / validLimit) : 1,
            currentPage: isPagination ? validPage : 1,
        };
    }
    async filter(user, query) {
        const profileKey = query.optionFilter;
        const agencia = query.agencia ? query.agencia?.trim() : query.agencia;
        const userName = query.userName ? query.userName?.trim() : query.userName;
        const queryBuilder = this.scheduleRepository
            .createQueryBuilder('schedule')
            .innerJoin('schedule.user', 'user')
            .innerJoin('federation', 'federation', 'user.federation_id = federation.id')
            .innerJoin('user.cooperative', 'cooperative', 'user.centralId = cooperative.centralId')
            .innerJoin('user.central', 'central', 'user.federationId = central.federationId')
            .innerJoin('user.agency', 'agency')
            .leftJoin('schedule.attendance', 'attendance')
            .leftJoin('attendance.associate', 'associate')
            .leftJoin('user.profile', 'profile')
            .leftJoin('user', 'attendanceUser', 'attendance.attendant_id = attendanceUser.id')
            .select([
            'schedule.id AS schedule_id',
            'schedule.title AS schedule_title',
            'schedule.scheduleAt AS schedule_scheduleAt',
            'schedule.createdAt AS schedule_createdAt',
            'schedule.description AS schedule_description',
            'user.id AS user_id',
            'user.federation_id AS user_federation_id',
            'associate.id AS associate_id',
            'associate.name AS associate_name',
            'associate.email AS associate_email',
            'user.name AS user_name',
            'user.email AS user_email',
            'user.phone AS user_phone',
            'profile.name AS profile_name',
            'attendance.attendant_id AS attendance_id',
            'agency.name AS agency_name',
            'agency.agency_code AS agency_code',
            'attendanceUser.name AS attendant_name',
            'attendanceUser.email AS attendant_email',
            'attendanceUser.phone AS attendant_phone',
        ]);
        if (user.profile.key === 'AGENCY_MANAGER' && query.unidade
            ? query.unidade.toLowerCase()
            : query.unidade === 'gestor') {
            queryBuilder.andWhere('profile.key = :profileKey', { profileKey });
        }
        else if (user.profile.key === 'AGENCY_MANAGER' && query.unidade
            ? query.unidade.toLowerCase()
            : query.unidade === 'assistente') {
            queryBuilder.andWhere('profile.key = :profileKey', { profileKey });
        }
        if (user.profile.key !== 'WALLET_MANAGER' &&
            user.profile.key !== 'ASSISTANT') {
            if (agencia) {
                queryBuilder.andWhere('LOWER(agency.name) LIKE LOWER(:agencia)', {
                    agencia: `%${agencia}%`,
                });
            }
            if (userName) {
                queryBuilder.andWhere('LOWER(user.name) LIKE LOWER(:userName) or LOWER(attendanceUser.name) LIKE LOWER(:userName)', {
                    userName: `%${userName}%`,
                });
            }
        }
        queryBuilder.andWhere('schedule.deletedAt IS NULL');
        const data = await queryBuilder.getRawMany();
        if (!queryBuilder) {
            throw new common_1.NotFoundException(`Erro consulta`);
        }
        const items = data.map((data) => ({
            id: data['schedule_id'],
            description: data['schedule_description'],
            title: data['schedule_title'],
            scheduleAt: data['schedule_scheduleat'],
            createdAt: data['schedule_createdat'],
            attendance: data['attendance_id']
                ? {
                    id: data['attendance_id'],
                    associate: {
                        id: data['associate_id'],
                        name: data['associate_name'],
                        email: this.cryptography.decrypt(data['associate_email']),
                    },
                    attendant: {
                        id: data['attendant_id'],
                        name: data['attendant_name'],
                        email: this.cryptography.decrypt(data['attendant_email']),
                        phone: this.cryptography.decrypt(data['attendant_phone']),
                        profile: {
                            name: data['profile_name'],
                        },
                        agency: {
                            name: data['agency_name'],
                            code: data['agency_code'],
                        },
                    },
                }
                : null,
            owner: !data['attendance_id']
                ? {
                    id: data['owner_id'],
                    name: data['owner_name'],
                    profile: {
                        name: data['profile_name'],
                    },
                }
                : null,
            agency: {
                name: data['agency_name'],
                code: data['agency_code'],
            },
        }));
        return items;
    }
    async getSchedulesByAgencyId(user, query) {
        const idagencia = query.idAgency;
        const queryBuilder = this.scheduleRepository
            .createQueryBuilder('schedule')
            .innerJoin('schedule.user', 'user')
            .innerJoin('user.agency', 'agency')
            .leftJoin('schedule.attendance', 'attendance')
            .leftJoin('attendance.associate', 'associate')
            .leftJoin('user.profile', 'profile')
            .leftJoin('user', 'attendanceUser', 'attendance.attendant_id = attendanceUser.id')
            .select([
            'schedule.id AS schedule_id',
            'schedule.title AS schedule_title',
            'schedule.scheduleAt AS schedule_scheduleAt',
            'schedule.createdAt AS schedule_createdAt',
            'schedule.description AS schedule_description',
            'user.id',
            'user.federation_id',
            'associate.id AS associate_id',
            'associate.name AS associate_name',
            'associate.email AS associate_email',
            'user.name AS user_name',
            'user.email AS user_email',
            'user.phone AS user_phone',
            'profile.name AS profile_name',
            'profile.key AS profile_key',
            'attendance.attendant_id AS attendance_id',
            'agency.name AS agency_name',
            'agency.agency_code AS agency_code',
            'attendanceUser.name AS attendant_name',
            'attendanceUser.email AS attendant_email',
            'attendanceUser.phone AS attendant_phone',
        ]);
        queryBuilder.andWhere('user.agency_id = :idagencia', { idagencia });
        queryBuilder.andWhere('profile.key IN (:...idProfile)', {
            idProfile: ['ASSISTANT', 'WALLET_MANAGER'],
        });
        queryBuilder.andWhere('profile.key NOT IN (:...excludedProfiles)', {
            excludedProfiles: ['COOPERATIVE', 'CENTRAL', 'FEDERATION'],
        });
        queryBuilder.andWhere('schedule.deletedAt IS NULL');
        const data = await queryBuilder.getRawMany();
        if (!queryBuilder) {
            throw new common_1.NotFoundException(`Erro consulta`);
        }
        const items = data.map((data) => ({
            id: data['schedule_id'],
            description: data['schedule_description'],
            title: data['schedule_title'],
            scheduleAt: data['schedule_scheduleat'],
            createdAt: data['schedule_createdat'],
            attendance: data['attendance_id']
                ? {
                    id: data['attendance_id'],
                    associate: {
                        id: data['associate_id'],
                        name: data['associate_name'],
                        email: this.cryptography.decrypt(data['associate_email']),
                    },
                    attendant: {
                        id: data['attendant_id'],
                        name: data['attendant_name'],
                        email: this.cryptography.decrypt(data['attendant_email']),
                        profile: {
                            userName: data['user_name'],
                            name: data['profile_name'],
                            description: data['profile_description'],
                            key: data['profile_key'],
                        },
                        agency: {
                            name: data['agency_name'],
                            code: data['agency_code'],
                        },
                    },
                }
                : null,
            owner: !data['attendance_id']
                ? {
                    id: data['user_id'],
                    name: data['user_name'],
                    profile: {
                        name: data['profile_name'],
                        key: data['profile_key'],
                    },
                }
                : null,
            agency: {
                name: data['agency_name'],
                code: data['agency_code'],
            },
        }));
        return items;
    }
};
exports.SchedulesService = SchedulesService;
exports.SchedulesService = SchedulesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(schedule_entity_1.Schedule)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        cryptography_1.Cryptography])
], SchedulesService);
//# sourceMappingURL=schedules.service.js.map