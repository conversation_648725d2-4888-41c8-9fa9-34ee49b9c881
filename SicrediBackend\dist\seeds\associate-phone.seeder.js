"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociatePhoneSeeder = void 0;
const common_1 = require("@nestjs/common");
const associate_phone_entity_1 = require("../modules/associate-phone/entities/associate-phone.entity");
const associate_entity_1 = require("../modules/associates/entities/associate.entity");
const typeorm_1 = require("typeorm");
const cryptography_1 = require("../common/functions/cryptography");
let AssociatePhoneSeeder = class AssociatePhoneSeeder {
    constructor(dataSource, cryptography) {
        this.dataSource = dataSource;
        this.cryptography = cryptography;
    }
    async executeSeed() {
        const associatePhoneRepository = this.dataSource.getRepository(associate_phone_entity_1.AssociatePhone);
        const associateRepository = this.dataSource.getRepository(associate_entity_1.Associate);
        const associate = await associateRepository.findOne({
            where: { name: 'Arthur Abreu' },
        });
        await this.seedAssociatePhone({
            associate: associate,
            phone: this.cryptography.encrypt("41977777777"),
            createdAt: new Date("2025-02-21T15:55:04.967Z"),
        }, associatePhoneRepository);
        await this.seedAssociatePhone({
            associate: associate,
            phone: this.cryptography.encrypt("41988888888"),
            createdAt: new Date("2025-02-21T15:55:04.967Z"),
        }, associatePhoneRepository);
        await this.seedAssociatePhone({
            associate: associate,
            phone: this.cryptography.encrypt("41999999999"),
            createdAt: new Date("2025-02-21T15:55:04.967Z"),
        }, associatePhoneRepository);
    }
    async seedAssociatePhone(associatePhoneData, associatePhoneRepository) {
        const existing = await associatePhoneRepository.findOne({ where: { phone: associatePhoneData.phone } });
        if (!existing) {
            const associatePhone = associatePhoneRepository.create(associatePhoneData);
            await associatePhoneRepository.save(associatePhone);
        }
    }
};
exports.AssociatePhoneSeeder = AssociatePhoneSeeder;
exports.AssociatePhoneSeeder = AssociatePhoneSeeder = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource,
        cryptography_1.Cryptography])
], AssociatePhoneSeeder);
//# sourceMappingURL=associate-phone.seeder.js.map