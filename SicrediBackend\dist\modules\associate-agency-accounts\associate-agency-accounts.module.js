"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateAgencyAccountsModule = void 0;
const common_1 = require("@nestjs/common");
const associate_agency_accounts_service_1 = require("./associate-agency-accounts.service");
const associate_agency_accounts_controller_1 = require("./associate-agency-accounts.controller");
const typeorm_1 = require("@nestjs/typeorm");
const associate_agency_account_entity_1 = require("./entities/associate-agency-account.entity");
const cryptography_1 = require("../../common/functions/cryptography");
let AssociateAgencyAccountsModule = class AssociateAgencyAccountsModule {
};
exports.AssociateAgencyAccountsModule = AssociateAgencyAccountsModule;
exports.AssociateAgencyAccountsModule = AssociateAgencyAccountsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([associate_agency_account_entity_1.AssociateAgencyAccount])],
        controllers: [associate_agency_accounts_controller_1.AssociateAgencyAccountsController],
        providers: [associate_agency_accounts_service_1.AssociateAgencyAccountsService, cryptography_1.Cryptography],
    })
], AssociateAgencyAccountsModule);
//# sourceMappingURL=associate-agency-accounts.module.js.map