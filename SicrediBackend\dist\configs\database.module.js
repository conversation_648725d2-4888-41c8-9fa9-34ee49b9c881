"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const config_1 = require("@nestjs/config");
const user_entity_1 = require("../modules/users/entities/user.entity");
const federation_entity_1 = require("../modules/federations/entities/federation.entity");
const audit_log_entity_1 = require("../modules/audit-logs/entities/audit-log.entity");
const central_entity_1 = require("../modules/centrals/entities/central.entity");
const cooperative_entity_1 = require("../modules/cooperatives/entities/cooperative.entity");
const agency_entity_1 = require("../modules/agencies/entities/agency.entity");
const segment_entity_1 = require("../modules/segments/entities/segment.entity");
const permission_entity_1 = require("../modules/permissions/entities/permission.entity");
const profile_entity_1 = require("../modules/profiles/entities/profile.entity");
const profile_permission_entity_1 = require("../modules/profile-permissions/entities/profile-permission.entity");
const user_agency_entity_1 = require("../modules/user-agencies/entities/user-agency.entity");
const wallet_entity_1 = require("../modules/wallets/entities/wallet.entity");
const product_entity_1 = require("../modules/products/entities/product.entity");
const event_entity_1 = require("../modules/events/entities/event.entity");
const associate_entity_1 = require("../modules/associates/entities/associate.entity");
const associate_agency_account_entity_1 = require("../modules/associate-agency-accounts/entities/associate-agency-account.entity");
const attendance_status_entity_1 = require("../modules/attendance-status/entities/attendance-status.entity");
const attendance_entity_1 = require("../modules/attendances/entities/attendance.entity");
const attendance_product_entity_1 = require("../modules/attendance-products/entities/attendance-product.entity");
const attendance_products_effective_entity_1 = require("../modules/attendance-products-effective/entities/attendance-products-effective.entity");
const schedule_entity_1 = require("../modules/schedules/entities/schedule.entity");
const axios_1 = require("@nestjs/axios");
const auth_service_1 = require("../auth/auth.service");
const auth_module_1 = require("../auth/auth.module");
const keycloak_seeder_1 = require("../seeds/keycloak.seeder");
const keycloak_entity_1 = require("../modules/keycloak/entities/keycloak.entity");
const keycloak_service_1 = require("../modules/keycloak/keycloak.service");
const users_module_1 = require("../modules/users/users.module");
const cryptography_1 = require("../common/functions/cryptography");
const wallet_range_value_entity_1 = require("../modules/wallet-range-values/entities/wallet-range-value.entity");
const wallet_range_value_seeder_1 = require("../seeds/wallet-range-value.seeder");
const segment_seeder_1 = require("../seeds/segment.seeder");
const wallet_seeder_1 = require("../seeds/wallet.seeder");
const index_seeder_1 = require("../seeds/index.seeder");
const permissions_seeder_1 = require("../seeds/permissions.seeder");
const profile_seeder_1 = require("../seeds/profile.seeder");
const user_seeder_1 = require("../seeds/user.seeder");
const product_seeder_1 = require("../seeds/product.seeder");
const attendance_history_entity_1 = require("../modules/attendance-history/entities/attendance-history.entity");
const attendance_status_seeder_1 = require("../seeds/attendance-status.seeder");
const strategy_entity_1 = require("../modules/strategies/entities/strategy.entity");
const strategy_product_entity_1 = require("../modules/strategy-products/entities/strategy-product.entity");
const propensity_product_entity_1 = require("../modules/propensity-products/entities/propensity-product.entity");
const event_seeder_1 = require("../seeds/event.seeder");
const profile_permissions_seeder_1 = require("../seeds/profile-permissions.seeder");
const permissions_strategy_seeder_1 = require("../seeds/permissions-strategy.seeder");
const profile_permissions_strategy_seeder_1 = require("../seeds/profile-permissions-strategy.seeder");
const user_wallets_entity_1 = require("../modules/user-wallets/entities/user-wallets.entity");
const agency_seeder_1 = require("../seeds/agency.seeder");
const federation_seeder_1 = require("../seeds/federation.seeder");
const central_seeder_1 = require("../seeds/central.seeder");
const cooperative_seeder_1 = require("../seeds/cooperative.seeder");
const account_entity_1 = require("../modules/accounts/entities/account.entity");
const account_wallets_entity_1 = require("../modules/account-wallets/entities/account-wallets.entity");
const account_type_entity_1 = require("../modules/account-type/entities/account-type.entity");
const account_type_seeder_1 = require("../seeds/account-type.seeder");
const account_seeder_1 = require("../seeds/account.seeder");
const account_wallets_seeder_1 = require("../seeds/account-wallets.seeder");
const associate_seeder_1 = require("../seeds/associate.seeder");
const goal_entity_1 = require("../modules/goal/entities/goal.entity");
const goal_product_wallet_entity_1 = require("../modules/goal-product-wallet/entities/goal-product-wallet.entity");
const associate_details_entity_1 = require("../modules/associate-details/entities/associate-details.entity");
const card_entity_1 = require("../modules/cards/entities/card.entity");
const card_type_entity_1 = require("../modules/card-types/entities/card-type.entity");
const card_seeder_1 = require("../seeds/card.seeder");
const card_type_seeder_1 = require("../seeds/card-type.seeder");
const associate_detail_seeder_1 = require("../seeds/associate-detail.seeder");
const associate_phone_seeder_1 = require("../seeds/associate-phone.seeder");
const associate_phone_entity_1 = require("../modules/associate-phone/entities/associate-phone.entity");
const notification_entity_1 = require("../modules/notifications/entities/notification.entity");
let DatabaseModule = class DatabaseModule {
};
exports.DatabaseModule = DatabaseModule;
exports.DatabaseModule = DatabaseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    type: 'postgres',
                    host: configService.get('DB_HOST'),
                    port: configService.get('DB_PORT'),
                    username: configService.get('DB_USERNAME'),
                    password: configService.get('DB_PASSWORD'),
                    database: configService.get('DB_NAME'),
                    entities: [
                        goal_entity_1.Goal,
                        goal_product_wallet_entity_1.GoalProductWallet,
                        user_entity_1.User,
                        federation_entity_1.Federation,
                        audit_log_entity_1.AuditLog,
                        central_entity_1.Central,
                        cooperative_entity_1.Cooperative,
                        agency_entity_1.Agency,
                        segment_entity_1.Segment,
                        permission_entity_1.Permission,
                        profile_entity_1.Profile,
                        profile_permission_entity_1.ProfilePermission,
                        user_agency_entity_1.UserAgency,
                        wallet_entity_1.Wallet,
                        product_entity_1.Product,
                        event_entity_1.Event,
                        associate_entity_1.Associate,
                        associate_agency_account_entity_1.AssociateAgencyAccount,
                        attendance_status_entity_1.AttendanceStatus,
                        attendance_entity_1.Attendance,
                        attendance_product_entity_1.AttendanceProduct,
                        attendance_products_effective_entity_1.AttendanceProductEffective,
                        schedule_entity_1.Schedule,
                        keycloak_entity_1.Keycloak,
                        wallet_range_value_entity_1.WalletRangeValue,
                        attendance_history_entity_1.AttendanceHistory,
                        strategy_entity_1.Strategy,
                        strategy_product_entity_1.StrategyProduct,
                        propensity_product_entity_1.PropensityProduct,
                        user_wallets_entity_1.UserWallet,
                        account_entity_1.Accounts,
                        account_wallets_entity_1.AccountWallets,
                        account_type_entity_1.AccountType,
                        associate_details_entity_1.AssociateDetails,
                        card_entity_1.Card,
                        card_type_entity_1.CardType,
                        associate_phone_entity_1.AssociatePhone,
                        notification_entity_1.Notification,
                    ],
                    synchronize: true,
                    logging: configService.get('DB_LOGGING') === 'true',
                }),
                inject: [config_1.ConfigService],
            }),
            axios_1.HttpModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
        ],
        controllers: [],
        providers: [
            permissions_seeder_1.PermissionsSeeder,
            permissions_strategy_seeder_1.PermissionsStrategySeeder,
            profile_seeder_1.ProfilesSeeder,
            keycloak_seeder_1.KeycloakSeeder,
            index_seeder_1.IndexSeeder,
            user_seeder_1.UserSeeder,
            profile_permissions_seeder_1.ProfilePermissionsSeeder,
            profile_permissions_strategy_seeder_1.ProfilePermissionsStrategySeeder,
            federation_seeder_1.FederationSeeder,
            central_seeder_1.CentralSeeder,
            cooperative_seeder_1.CooperativeSeeder,
            agency_seeder_1.AgencySeeder,
            wallet_range_value_seeder_1.WalletRangeValueSeeder,
            segment_seeder_1.SegmentSeeder,
            wallet_seeder_1.WalletSeeder,
            keycloak_service_1.KeycloakService,
            auth_service_1.AuthService,
            cryptography_1.Cryptography,
            product_seeder_1.ProductSeeder,
            attendance_status_seeder_1.AttendanceStatusSeeder,
            event_seeder_1.EventSeeder,
            account_type_seeder_1.AccountTypeSeeder,
            account_seeder_1.AccountSeeder,
            account_wallets_seeder_1.AccountWalletSeeder,
            associate_seeder_1.AssociateSeeder,
            card_seeder_1.CardSeeder,
            card_type_seeder_1.CardTypeSeeder,
            associate_detail_seeder_1.AssociateDetailSeeder,
            associate_phone_seeder_1.AssociatePhoneSeeder,
        ],
    })
], DatabaseModule);
//# sourceMappingURL=database.module.js.map