"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AssociateDetailsController = void 0;
const common_1 = require("@nestjs/common");
const associate_details_service_1 = require("./associate-details.service");
const create_associate_details_dto_1 = require("./dto/create-associate-details.dto");
const update_associate_details_dto_1 = require("./dto/update-associate-details.dto");
const swagger_1 = require("@nestjs/swagger");
const response_associate_details_dto_1 = require("./dto/response-associate-details.dto");
const associate_details_dto_1 = require("./dto/associate-details.dto");
let AssociateDetailsController = class AssociateDetailsController {
    constructor(associateDetailsService) {
        this.associateDetailsService = associateDetailsService;
    }
    create(createAssociateDetailsDto) {
        return this.associateDetailsService.create(createAssociateDetailsDto);
    }
    async findOne(id) {
        return await this.associateDetailsService.findOne(id);
    }
    async findByAssociateId(id) {
        return await this.associateDetailsService.findByAssociateId(id);
    }
    update(id, updateAssociateDetailsDto) {
        return this.associateDetailsService.update(id, updateAssociateDetailsDto);
    }
    remove(id) {
        return this.associateDetailsService.remove(id);
    }
    async createBulk(associateDetailsDto) {
        if (!associateDetailsDto || associateDetailsDto.length === 0) {
            throw new common_1.BadRequestException({
                status: 'error',
                message: 'O corpo da requisição não pode estar vazio.',
            });
        }
        try {
            return await this.associateDetailsService.createAssociateDetailsFromBulk(associateDetailsDto);
        }
        catch (error) {
            throw new common_1.InternalServerErrorException({
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: error.message,
            });
        }
    }
};
exports.AssociateDetailsController = AssociateDetailsController;
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Criar novo Detalhe de Associado' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Detalhe de Associado criado com sucesso.',
        type: create_associate_details_dto_1.CreateAssociateDetailsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_associate_details_dto_1.CreateAssociateDetailsDto]),
    __metadata("design:returntype", void 0)
], AssociateDetailsController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-details'),
    (0, swagger_1.ApiOperation)({
        summary: 'Buscar Detalhe de Associado pelo ID',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Detalhe de Associado encontrado com sucesso.',
        type: response_associate_details_dto_1.ResponseAssociateDetailsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Detalhe de Associado encontrado.' }),
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AssociateDetailsController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Buscar Detalhe de Associado pelo ID do Associado' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Detalhe de Associado encontrado com sucesso.',
        type: response_associate_details_dto_1.ResponseAssociateDetailsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Nenhum Detalhe de Associado encontrado.' }),
    (0, common_1.Get)('by-associate/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AssociateDetailsController.prototype, "findByAssociateId", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Atualizar Detalhe de Associado' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Detalhe de Associado atualizado com sucesso.',
        type: update_associate_details_dto_1.UpdateAssociateDetailsDto,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Dados inválidos.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Detalhe de Associado não encontrado.' }),
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, update_associate_details_dto_1.UpdateAssociateDetailsDto]),
    __metadata("design:returntype", void 0)
], AssociateDetailsController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('private-api/associate-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Remover Detalhe de Associado' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Detalhe de Associado removido com sucesso.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Credenciais inválidas.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Detalhe de Associado não encontrado.' }),
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], AssociateDetailsController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiTags)('external-integration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Criar ou atualizar detalhes de associados em massa',
        description: `
      Este endpoint permite criar novos registros ou atualizar registros já existentes para associados em massa.
      Se um registro já existir (baseado no CPF ou CNPJ do associado), seus dados serão atualizados com as novas informações enviadas.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: [associate_details_dto_1.AssociateDetailsDto],
        description: 'Array de objetos contendo os detalhes dos associados a serem criados ou atualizados.',
        examples: {
            exemplo: {
                summary: 'Exemplo de requisição para criar e atualizar detalhes dos associados',
                value: [
                    {
                        associate_cpf: '12345678900',
                        is_restrict: true,
                        is_debtor: true,
                        lca_value: 1000.00,
                        deposit_value: 5000.00,
                        missing_installments: 5,
                        has_subscription: true,
                        term_deposit_expiration: '2025-12-31T23:59:59.999Z',
                        lca_expiration: '2025-12-31T23:59:59.999Z',
                        last_registration_update: '2024-10-01T00:00:00.000Z',
                        channel_access: '2024-11-01T00:00:00.000Z',
                        has_financial_flow: true,
                        is_blocked: true,
                        credit_card_last_buy: '2021-10-01T00:00:00.000Z',
                    },
                    {
                        associate_cnpj: '98765432000199',
                        is_restrict: false,
                        is_debtor: false,
                        lca_value: 1000.00,
                        deposit_value: 5000.00,
                        missing_installments: 10,
                        has_subscription: false,
                        term_deposit_expiration: '2025-11-30T23:59:59.999Z',
                        lca_expiration: '2025-11-30T23:59:59.999Z',
                        last_registration_update: '2024-08-15T00:00:00.000Z',
                        channel_access: '2024-09-15T00:00:00.000Z',
                        has_financial_flow: false,
                        is_blocked: true,
                        credit_card_last_buy: '2021-10-01T00:00:00.000Z',
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Detalhes dos associados criados ou atualizados com sucesso.',
        schema: {
            example: {
                status: 'success',
                message: 'Associate details processed successfully',
                processedAssociateDetails: [
                    {
                        id: 1,
                        associateId: 1001,
                        associate_cpf: '12345678900',
                        is_restrict: true,
                        has_subscription: true,
                        has_financial_flow: true,
                    },
                    {
                        id: 2,
                        associateId: 1002,
                        associate_cnpj: '98765432000199',
                        is_restrict: false,
                        has_subscription: false,
                        has_financial_flow: false,
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Requisição inválida - Dados ausentes ou incorretos.',
        schema: {
            example: {
                status: 'error',
                message: 'Missing required fields: associate_cpf or associate_cnpj, is_restrict',
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Requisição falhou pois um dos associados não foi encontrado.',
        schema: {
            example: {
                status: 'error',
                message: "Associate with CPF/CNPJ '99999999999' not found.",
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 207,
        description: 'Alguns registros foram processados com sucesso, mas outros tiveram erros.',
        schema: {
            example: {
                processedAssociateDetails: [
                    {
                        id: 3,
                        associateId: 1003,
                        associate_cpf: '32165498700',
                        is_restrict: false,
                        has_subscription: true,
                        has_financial_flow: true,
                    },
                ],
                errors: [
                    {
                        associate_details: {
                            associate_cpf: '11111111111',
                            is_restrict: true,
                            has_subscription: false,
                        },
                        status: 'error',
                        message: "Associate with CPF '11111111111' not found.",
                    },
                ],
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Erro interno ao tentar processar a solicitação.',
        schema: {
            example: {
                status: 'error',
                message: 'Ocorreu um erro ao processar a solicitação.',
                details: 'Detalhes do erro específico aqui',
            },
        },
    }),
    (0, common_1.Put)('bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], AssociateDetailsController.prototype, "createBulk", null);
exports.AssociateDetailsController = AssociateDetailsController = __decorate([
    (0, common_1.Controller)('api/v1/associate-details'),
    __metadata("design:paramtypes", [associate_details_service_1.AssociateDetailsService])
], AssociateDetailsController);
//# sourceMappingURL=associate-details.controller.js.map