"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AttendanceProductsEffectiveModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const attendance_products_effective_service_1 = require("./attendance-products-effective.service");
const attendance_products_effective_controller_1 = require("./attendance-products-effective.controller");
const attendance_products_effective_entity_1 = require("./entities/attendance-products-effective.entity");
const common_module_1 = require("../common/common.module");
const account_entity_1 = require("../accounts/entities/account.entity");
const user_entity_1 = require("../users/entities/user.entity");
const associate_entity_1 = require("../associates/entities/associate.entity");
const product_entity_1 = require("../products/entities/product.entity");
const users_module_1 = require("../users/users.module");
const associates_module_1 = require("../associates/associates.module");
let AttendanceProductsEffectiveModule = class AttendanceProductsEffectiveModule {
};
exports.AttendanceProductsEffectiveModule = AttendanceProductsEffectiveModule;
exports.AttendanceProductsEffectiveModule = AttendanceProductsEffectiveModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([attendance_products_effective_entity_1.AttendanceProductEffective, account_entity_1.Accounts, user_entity_1.User, associate_entity_1.Associate, product_entity_1.Product]),
            common_module_1.CommonModule,
            (0, common_1.forwardRef)(() => users_module_1.UsersModule),
            (0, common_1.forwardRef)(() => associates_module_1.AssociatesModule),],
        controllers: [attendance_products_effective_controller_1.AttendanceProductsEffectiveController],
        providers: [attendance_products_effective_service_1.AttendanceProductsEffectiveService],
        exports: [attendance_products_effective_service_1.AttendanceProductsEffectiveService],
    })
], AttendanceProductsEffectiveModule);
//# sourceMappingURL=attendance-products-effective.module.js.map